import { GetServerSidePropsContext } from 'next'
import { useTranslation } from 'react-i18next'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import withTranslateProvider from 'src/hoc/with-translate-provider'
import HeadMetaTags from '~/components/HeadMetaTags'
import LayoutGridSettings from '~/components/Layout/LayoutGridSettings'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { PUBLIC_APP_NAME } from '~/core/constants/env'
import If from '~/core/ui/If'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'
import ImportManagementContainer from '~/features/settings/import'

function ImportManagementPage(props: { tab?: string; type?: string }) {
  const { t } = useTranslation()
  const currentRole = useBoundStore((state) => state.currentRole)
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const enableImportJobs =
    isFeatureEnabled(PLAN_FEATURE_KEYS.import_job) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.import_job)

  const enableImportCandidates =
    isFeatureEnabled(PLAN_FEATURE_KEYS.import_candidate) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.import_candidate)

  const enableImportCourses =
    isFeatureEnabled(PLAN_FEATURE_KEYS.learning_management_system) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.learning_management_system)

  const enableImportCompanies =
    isFeatureEnabled(PLAN_FEATURE_KEYS.company) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.company)

  return (
    <>
      <HeadMetaTags
        title={`${t(`common:seo:settingMembers`, { PUBLIC_APP_NAME })}`}
      />
      <If condition={currentRole?.id}>
        <LayoutGridSettings>
          <ImportManagementContainer
            enableImportJobs={enableImportJobs}
            enableImportCandidates={enableImportCandidates}
            enableImportCourses={enableImportCourses}
            enableImportCompanies={enableImportCompanies}
            query={props}
          />
        </LayoutGridSettings>
      </If>
    </>
  )
}

export const getServerSideProps = (ctx: GetServerSidePropsContext) => {
  const { query } = ctx

  return {
    props: {
      tab: query.tab || 'import',
      object_kind: query.object_kind || ''
    }
  }
}

export default withTranslateProvider(
  withQueryClientProvider(ImportManagementPage)
)
