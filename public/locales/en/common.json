{"learn_more": "Learn more", "more": "more", "numberMore": "{{number}} more", "invite": "Invite", "sidebar": {"search": "Search", "notification": "Notification", "blocked": "Blocked", "selectCompany": "Select company", "management": {"title": "Data Management", "list": {"universalSearch": "Search", "dashboard": "Dashboard", "notifications": "Notifications", "candidates": "Candidates", "jobs": "Jobs", "tasks": "Tasks", "companies": "Companies", "clients": "Clients", "mailbox": "Mailbox", "tasksAndMeetings": "Tasks & Meetings", "reports": "Report", "calendar": "Calendar", "referrals": "Career hub", "requisitions": "Requisitions", "talentPool": "Talent pools", "placements": "Placements", "contacts": "Contacts", "courseManagement": "Courses"}}, "settings": {"title": "Settings", "list": {"accountTitle": "My Account", "accountList": {"profiles": "Profiles", "profile": "Profile", "preferences": "Preferences", "account": "Account", "emailSetting": "Email settings"}, "companyTitle": "Company", "companyList": {"general": "General", "profile": "Company profile", "careerhub": "Career hub", "requisitions": "Requisitions", "careers": "Careers page", "termsAndConditions": "Terms & Conditions", "teamsDepartments": "Teams/Departments", "teamMembers": "Members", "positions": "Positions", "locations": "Locations", "plans": "Plans", "import": "Import", "audit_logs": "Audit logs", "teams": "Teams"}, "workFlowTitle": "Workflow", "workSpaceTitle": "Workspace", "recruitingTitle": "Recruiting", "workFlowList": {"disqualificationReasons": "Disqualified reasons", "tags": "Tags"}, "companyStatus": "Company status", "companySettings": "Company settings", "templatesTitle": "Templates", "templatesList": {"emailTemplates": "Email templates", "profileFields": "Profile fields", "hiringPipelines": "Hiring Pipelines", "customFields": "Custom fields"}, "templates_title": "Templates", "templates_list": {"disqualification_reasons": "Disqualification reasons"}, "interviewKits": "Interview kits", "profileTemplates": "Profile templates", "security": "Security", "skills": "Skills"}, "closeSetting": "Close settings"}, "profile": {"viewProfile": "View Profile", "careerPage": "'s career page", "dropdown": {"account": "Account", "settings": "Settings", "careerHub": "Career Hub", "helpCenter": "Help center", "logOut": "Log out", "switchCompany": "Switch company", "downloadExtension": "Download extension"}, "tooltip_view_profile": "Your profile and settings"}, "yourCareerPage": "Your career page"}, "infinity": {"showMore": "Show more"}, "table": {"show25More": "Show 25 more", "endOfList": "You’ve reached the end of the list!"}, "error": {"404": {"title": "Page not found", "description": "Something went wrong. Check the website URL again or contact us at <0>{{email}}</0>", "button": "Back to home"}, "500": {"title": "Server error", "description": "Something went wrong. Try to refresh this page or contact us at <0>{{email}}</0>", "button": "Reload page"}, "access_denied": {"title": "Uh-oh! Can't Access this content", "description": "Looks like you're missing the right permissions to view this content. You can click Log out or Reach out to your Admin and ask for the right to access this content. ", "contact": "Questions? We're here to help! Contact <0>{{email}}</0>", "button": "Back to home"}, "tenant_blocked": {"title": "Your account is block", "description": "Looks like your account has been blocked. Contact support to reactivate your account.", "button": "Contact us"}, "expired_plan": {"title": "Oh oh, your subscription expired.", "description": "Looks like the subscription for your account has expired. Reach out to our help and support team or log in again to resolve this.", "button": "Contact us"}, "406": {"title": "Unauthorized access", "description": "You don’t have permission to access this system. Please contact your administrator for assistance. "}}, "modal": {"discard_unsaved_changes_title": "Discard unsaved changes", "discard_unsaved_change_invite": "Your invitation will be canceled", "discard_unsaved_changes_description": "Changes you’ve made will not be saved.", "discard_unsaved_changes_full": "Discard unsaved changes. Changes you’ve made will not be saved.", "discard_unsaved_upload_file": "Your template is uploading. Are you sure you want to leave this page?", "delete_note_title": "Delete note", "delete_note_description": "You are about to delete the note. This action can not be undone.", "deleteNoteTitle": "Delete note", "deleteNoteDescription": "You are about to delete the note. This action can not be undone.", "attachment_failed_title": "Attachment failed", "attachment_failed_description": "Maximum 10 files can be uploaded.", "remove_member_title": "Remove member", "remove_member_description": "Are you sure you want to remove <strong>{{name}}</strong>? They won’t be able to access this job.", "change_job_status_title": "Change job status", "change_company_status_title": "Change status", "change_job_status_description": "Are you sure you want to change <strong class='font-medium'>{{title}}</strong>'s status to <strong class='font-medium'>{{name}}</strong>?", "change_job_status_description_0": "Are you sure you want to change the job status to <strong class='font-medium'>{{name}}</strong>?", "change_job_status_description_1": "This will cause job to be unpublished on all job boards and not possible to process candidates or add notes.", "change_job_status_description_2": "This will cause job to be unpublished on all job boards.", "confirmation_title": "Confirmation", "confirmation_description": "Your current custom pipeline will be lost. This action can not be undone.", "delete_cv_title": "Delete CV", "delete_cv_description": "Are you sure you want to delete? This action cannot be undone.", "unmark_as_hired_title": "Unmark as hired", "unmark_as_hired_description": "Are you sure you want to unmark <span class='ml-1 font-medium text-gray-900'>{{title}}</span> as hired? The hired date, start date and hired by informations will be cleared. This action cannot be undone.", "delete_hiring_pipeline_title": "Delete hiring pipeline template", "delete_hiring_pipeline_description": "Are you sure you want to delete <span class='ml-1 font-medium text-gray-900'>{{title}}</span>? This action cannot be undone.", "delete_interview_title": "Delete interview kit", "delete_interview_description": "Are you sure you want to delete <span class='ml-1 font-medium text-gray-900'>{{title}}</span>? This action cannot be undone.", "delete_approval_title": "Delete approval flow", "delete_approval_description": "Are you sure you want to delete <span class='ml-1 font-medium text-gray-900'>{{title}}</span>? This action cannot be undone.", "delete_stage_title": "Delete stage", "delete_stage_description": "Are you sure you want to delete <span class='ml-1 font-medium text-gray-900'>{{title}}</span>? This action cannot be undone.", "delete_stage_description_01": "Currently, there are {{qualifiedCount}} qualified candidate(s) and {{disQualifiedCount}} disqualified candidate(s) associated with this stage. Before proceeding with stage deletion, please ensure that the stage is empty.", "delete_task_title": "Delete task", "delete_task_description_Text": "Are you sure you want to delete <b>{{title}}</b>? This action cannot be undone.", "delete_this_interview": "Delete this interview", "delete_this_interview_description": "Are you sure you want to delete the <span class='font-medium text-gray-900'>{{eventType}}</span>? This action cannot be undone. Notify candidate and attendees by selecting the checkbox below.", "delete_this_evaluation": "Delete this evaluation", "delete_this_evaluation_description": "Are you sure you want to delete this evaluation? This action cannot be undone.", "pending_feedback_reminder": "Pending feedback reminder", "pending_feedback_reminder_description": "You have awaiting feedback from the interview {{fromDatetime}} that has not been submitted yet. Would you like to:", "move_back_to_stage": "Move back to the {{stageLabel}} stage?", "move_back_to_stage_description": "Are you sure you want to move <span class='ml-1 font-medium text-gray-900'>{{fullName}}</span> back to the {{stageLabel}} stage? The placement information will be cleared. This action cannot be undone.", "delete_custom_fields": "Delete additional field", "delete_custom_fields_description": "Are you sure you want to delete the <span class='font-medium text-gray-900'>{{fieldName}}</span> field? Deleting this additional field will permanently remove data from all {{name}} and cannot be undone.", "delete_profile_templates_section": "Delete section", "delete_profile_templates_title": "Delete profile template", "delete_profile_templates_description": "Are you sure you want to delete <span class='font-medium text-gray-900'>{{title}}</span>? This action cannot be undone.", "delete_file_profile_title": "Delete file", "delete_file_profile_description": "Are you sure you want to delete  <strong class='font-medium'>{{filename}}</strong>? This action cannot be undone.", "leave_page_title": "Leave page", "leave_page_description": "Your template is uploading. Are you sure you want to leave this page?", "delete_candidate_title": "Delete candidate", "delete_candidate_description": "You're about to delete this profile and all related data. The action cannot be undone. Are you sure you want to proceed?", "deleteCandidatesDescription": "Are you sure you want to delete bulk candidates? This action cannot be undone. Are you sure you want to proceed?", "moveApplicantsToNewStage": "Move applicants to new stage", "moveApplicantsToNewStageDescription": "Some selected applicants are currently in the Hired stage. Moving them to a different stage will remove all associated hiring details. Are you sure?", "moveApplicantsToNewStageAgencyDescription": "Some selected applicants are currently in the Hired stage. Moving them to a different stage will remove all associated placement details. Are you sure?"}, "you_not_in_hiring_team": "You're not part of the hiring team, so you can't access this job", "mobile": {"labelMobileVersionUnavailable": "Mobile version unavailable", "descriptionMobileVersionUnavailable": "We are sorry for any inconvenience. {{domain}} now is not supported on mobile devices. Please switch to your desktop or laptop to continue."}, "upgradeUnlockFeature": "Upgrade to unlock this feature!", "show_more": "Show more", "seo": {"500": "Error - {{PUBLIC_APP_NAME}}", "404": "Not found - {{PUBLIC_APP_NAME}}", "tenantBlocked": "Blocked - {{PUBLIC_APP_NAME}}", "accessDenied": "Access Denied - {{PUBLIC_APP_NAME}}", "authError": "Authentication Error - {{PUBLIC_APP_NAME}}", "register": "Sign Up - {{PUBLIC_APP_NAME}}", "login": "Sign In - {{PUBLIC_APP_NAME}}", "careerHubAuthError": "Career Hub Authentication Error - {{PUBLIC_APP_NAME}}", "careerHubLogin": "Career Hub Sign In - {{PUBLIC_APP_NAME}}", "verifyEmail": "Verify Email - {{PUBLIC_APP_NAME}}", "onboarding": "Onboarding - {{PUBLIC_APP_NAME}}", "welcome": "Welcome - {{PUBLIC_APP_NAME}}", "noAssociatedCompany": "No associated company - {{PUBLIC_APP_NAME}}", "selectCompany": "Select company - {{PUBLIC_APP_NAME}}", "candidates": "Candidates - {{PUBLIC_APP_NAME}}", "candidatesWithView": "Candidates | {{VIEW_NAME}} - {{PUBLIC_APP_NAME}}", "candidateDetail": "Candidate {{name}} - {{PUBLIC_APP_NAME}}", "candidateDetailEdit": "Edit CV - {{PUBLIC_APP_NAME}}", "jobs": "Jobs - {{PUBLIC_APP_NAME}}", "jobDetail": "{{jobDetail}} - {{PUBLIC_APP_NAME}}", "jobEdit": "{{jobEdit}} - {{PUBLIC_APP_NAME}}", "jobCreate": "New job - {{PUBLIC_APP_NAME}}", "jobDuplicate": "New job - {{PUBLIC_APP_NAME}}", "jobAppliedCareerDetails": "Applied successfully - {{PUBLIC_APP_NAME}}", "calendar": "Calendar - {{PUBLIC_APP_NAME}}", "referrals": "Referrals - {{PUBLIC_APP_NAME}}", "reports": "Reports - {{PUBLIC_APP_NAME}}", "settingRequisition": "Settings - Requisitions - {{PUBLIC_APP_NAME}}", "requisition": "Requisitions - {{PUBLIC_APP_NAME}}", "settingMembers": "Settings - Members - {{PUBLIC_APP_NAME}}", "settingPositions": "Settings - Positions - {{PUBLIC_APP_NAME}}", "settingPositionDetail": "{{positionDetail}} - {{PUBLIC_APP_NAME}}", "settingPositionEdit": "{{positionEdit}} - {{PUBLIC_APP_NAME}}", "settingProfiles": "Settings - Profiles - {{PUBLIC_APP_NAME}}", "settingDepartments": "Settings - Departments - {{PUBLIC_APP_NAME}}", "settingSKills": "Settings - Skills - {{PUBLIC_APP_NAME}}", "settingLocations": "Settings - Locations - {{PUBLIC_APP_NAME}}", "settingImport": "Settings - Import - {{PUBLIC_APP_NAME}}", "settingWorkspace": "Settings - Workspace - {{PUBLIC_APP_NAME}}", "settingAccount": "Settings - Your account - {{PUBLIC_APP_NAME}}", "settingEmailSignature": "Settings - Email setting - {{PUBLIC_APP_NAME}}", "settingPreferences": "Settings - preferences - {{PUBLIC_APP_NAME}}", "settingCareerPage": "Settings - Career page - {{PUBLIC_APP_NAME}}", "settingEmailTemplates": "Settings - Email templates - {{PUBLIC_APP_NAME}}", "settingDisqualifyReasons": "Settings - Disqualify reasons - {{PUBLIC_APP_NAME}}", "settingCustomFields": "Settings - Custom fields - {{PUBLIC_APP_NAME}}", "settingHiringPipelines": "Settings - Hiring pipelines - {{PUBLIC_APP_NAME}}", "settingInterviewKits": "Settings - Interview kits - {{PUBLIC_APP_NAME}}", "settingTags": "Settings - Tags - {{PUBLIC_APP_NAME}}", "settingReferrals": "Settings - Career hub - {{PUBLIC_APP_NAME}}", "settingPlans": "Plans - {{PUBLIC_APP_NAME}}", "settingExpiredPlans": "Plan Expired - {{PUBLIC_APP_NAME}}", "settingProfileTemplates": "Settings - Profile Templates - {{PUBLIC_APP_NAME}}", "settingCompanySettings": "Settings - Company Settings - {{PUBLIC_APP_NAME}}", "feedback": "Feedback - {{PUBLIC_APP_NAME}}", "requisitionsCreate": "New requisition - {{PUBLIC_APP_NAME}}", "requisitionEdit": "{{requisitionName}} - {{PUBLIC_APP_NAME}}", "expiredTrial": "Plan Expired - {{PUBLIC_APP_NAME}}", "talentPool": "Talent pool - {{PUBLIC_APP_NAME}}", "auditLogs": "Audit logs - {{PUBLIC_APP_NAME}}", "courseManagement": "Courses - {{PUBLIC_APP_NAME}}", "agency": {"companies": "Companies - {{PUBLIC_APP_NAME}}", "contacts": "Contacts - {{PUBLIC_APP_NAME}}", "settings": {"companyStatus": "Settings - Company status - {{PUBLIC_APP_NAME}}"}}, "placements": "Placements - {{PUBLIC_APP_NAME}}", "interviewInvitation": "{{name}} - Interview invitation - {{PUBLIC_APP_NAME}}", "selfSchedule": "Self schedule booking form - {{PUBLIC_APP_NAME}}", "resumesEditor": "Profiles - Edit templates - {{PUBLIC_APP_NAME}}", "settingSecurity": "Settings - Security - {{PUBLIC_APP_NAME}}", "dashboard": "Dashboard - {{PUBLIC_APP_NAME}}", "settingSkillReport": "Skill reports - {{PUBLIC_APP_NAME}}", "settingTeams": "Settings - Teams - {{PUBLIC_APP_NAME}}", "406": "Unauthorized access - {{PUBLIC_APP_NAME}}", "careerNavigator": "My career - {{PUBLIC_APP_NAME}}", "careerNavigatorPosition": "My career - Positions - {{PUBLIC_APP_NAME}}", "learningLibrary": "Learning library - {{PUBLIC_APP_NAME}}"}, "upload": "Upload", "limit_file_size": "JPG, JPEG or PNG. Max size is {{file_size}}.", "upload_failed": "Upload failed: wrong file format.", "reach_limit_of_images": "You have reached the limit of {{num}} images for uploading.", "allSelected": "All selected", "selected": "{{numberCount}} selected", "selectedWithoutNumber": "selected", "locations": "locations", "sources": "sources", "jobs": "jobs", "notification": {"showUnread": "Show unread", "markAllAsRead": "Mark all as read", "deleteAllNotification": "Delete all notifications", "notificationSettings": "Notification settings", "empty": {"title": "No notifications", "description": "Once something happens, you'll find it here."}}, "selectedBulk": "<0>{{numberCount}}</0> of <0>{{totalCount}}</0> selected", "equivalentSKills": "Equivalent skills", "equivalentSKillDescription": "{{total}} equivalent skills", "aiSuggests": "AI suggests", "aiSuggestsDescription": "AI-suggested skills based on position, resume/CV, and work experience.", "aiSuggestsJobDescription": "List key skills or certifications. Use 'AI Suggests' for relevant recommendations. ", "noSkillSuggests": "No skill suggestions available.", "notAvailable": "Not available", "regenerate": "Regenerate", "match": "match", "generate": "Generate"}