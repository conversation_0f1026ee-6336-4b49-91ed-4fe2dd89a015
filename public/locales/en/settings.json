{"teamMembers": {"title": "Members", "description": "Manage your team members and their account permission here.", "tab": {"members": "Members", "membersTable": {"name": "Name", "role": "Role", "department": "Department", "team": "Team", "removeMemberTooltip": "Remove member"}, "membersEmpty": {"title": "No members", "description": "Looks like you don’t have any members invites at the moment!", "buttonTitle": "Invite Member", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search.", "buttonTitleSearch": ""}, "pendingMembersEmpty": {"title": "No pending invites", "description": "Looks like you don’t have any pending invites!"}, "pendingInvites": "Pending invites", "pendingInvitesTable": {"name": "Name", "role": "Role", "rolePending": "Pending", "removeMemberTooltip": "Remove member", "resendEmailTooltip": "Resend email"}, "pendingInvitesEmpty": {"title": "No pending invites", "description": "Looks like you don’t have any pending invites at the moment!", "buttonTitle": "Invite Member"}}, "inviteMemberButton": "Invite Member", "searchInput": "Search by name or email", "inviteMemberModal": {"title": "Invite member", "description": "Collaborate with team member by inviting them into this job.", "actions": {"cancel": "Cancel", "sendInvites": "Send Invites"}}, "editCHUBAccount": {"title": "Edit member account"}, "editAccountPermission": {"title": "Edit member account", "save": "Save"}, "additional_permission": {"additional_user_permissions": "User permission", "description_additional_user_permissions": "Define the specific actions permitted for this user within the platform.", "manage_requisition": "Manage requisition", "manage_requisition_description": "Can create new job requisitions and create jobs from approved requisitions.", "request_requisition": "Request requisition", "request_requisition_description": "Can create new job requisitions but not create jobs once they are approved.", "invite_2_job_base_department_location": "Invite to jobs based on assigned locations/ departments"}, "removeMemberAlert": {"title": "Remove member", "content": "Are you sure you want to remove <0>{{name}}</0>? They won’t be able to access this workplace.", "cannot_remove_admin": "Cannot remove admin", "cannot_remove_recruiter": "Cannot remove member", "cannot_remove_owner": "Cannot remove owner", "remove_admin": "Remove admin", "remove_member": "Remove member", "contentWithHaveOpenJobs": "Are you sure you want to remove <0>{{name}}</0>? Please note that this user will be removed from {{openJobsCount}} jobs. They won’t be able to access this company.", "actions": {"cancel": "Cancel", "remove": "Remove"}}, "bulkRemoveMembersAlert": {"title": "Remove members", "content": "Are you sure you want to remove <0>{{count}}</0> members?<1></1> They won’t be able to access this workplace."}, "updateMemberAlert": {"cannot_edit_admin": "Cannot edit admin", "cannot_edit_member": "Cannot edit member", "cannot_edit_owner": "Cannot edit owner", "edit_member": "Edit member", "edit_admin": "Edit admin"}, "removeInvitationAlert": {"title": "Remove invitation", "content": "Are you sure you want to remove invitation to <0>{{name}}</0>?"}, "resendMemberAlert": {"title": "<PERSON><PERSON><PERSON>"}, "headQuarter": "Head quarter"}, "departments": {"title": "Teams/Departments", "description": "Manage your teams/departments here.", "searchInput": "Search by name", "addDepartmentButton": "Add top-level department", "addDepartment": "Add department", "tab": {"departmentEmpty": {"title": "No departments", "description": "Looks like you don’t add any departments!", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search."}, "departmentTable": {"name": "Name", "openJobsCount": "Open jobs"}}, "addDepartmentModal": {"title": "Add top-level department", "actions": {"cancel": "Cancel", "add": "Add"}}, "editDepartmentModal": {"title": "Edit top-level department", "subDepartmentTitle": "Edit sub-department", "actions": {"save": "Save"}}, "moveDepartmentModal": {"title": "Move department", "actions": {"cancel": "Cancel", "move": "Move"}}, "addSubDepartmentModal": {"title": "Add sub-department", "subtitle": "Subordinate to <0>{{name}}</0> department", "actions": {"cancel": "Cancel", "add": "Add"}}, "removeDepartmentAlert": {"title": "Delete top-level department", "titleSubDepartment": "Delete sub-department", "content": "Are you sure you want to delete <0>{{name}}</0>? Deleting this apartment will also remove all of its subdepartments. This action cannot be undone.", "contentRelatedJobs": "You're about to permanently delete the <0>{{name}}</0>. This will:<1>Remove all sub-departments under <0>{{name}}</0> </1><1>Remove <0>{{name}}</0> from {{countJobs}} associated jobs</1><1>Unassign all users currently linked to it</1>\nThe action cannot be undone. Are you sure you want to proceed?", "contentSub": "Are you sure you want to delete <0>{{name}}</0>? This action cannot be undone.", "contentSubRelatedJobs": "You're about to permanently delete the <0>{{name}}</0> sub-department.<2></2>This will remove <0>{{name}}</0> from {{countJobs}} associated jobs and unassign all users currently linked to it.<2></2>The action cannot be undone. Are you sure you want to proceed?", "actions": {"cancel": "Cancel", "remove": "Delete"}}, "updateDepartmentAlert": {"title": "Edit department"}, "addSubDepartmentAlert": {"title": "Add sub-department"}, "moveDepartmentTooltip": {"title": "Move"}, "moveDepartmentUnder": "Move <span class='mx-1 font-medium text-gray-900'>{{name}}</span> under the following department:", "moveDepartmentTop": "Move <span class='mx-1 font-medium text-gray-900'>{{name}}</span> to top-level", "noResult": "No result", "allDepartments": "All Teams/Departments", "assignToDepartments": "Assign to departments", "descriptionAssignToDepartments": "Auto-add users to job hiring teams based on their department matches."}, "skills": {"title": "Skills", "description": "Manage skills to optimize candidate-job matching.", "searchInput": "Search by skill name", "searchGroupInput": "Search by group name", "addSKillButton": "Add group", "addSKill": "Add skill", "statistics": "<0>{{groupTotal}}</0> groups - <0>{{skillTotal}}</0> skills", "allGroup": "All groups", "tab": {"skillEmpty": {"title": "No skills", "description": "Looks like you don’t add any skills!", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search."}, "groupEmpty": {"title": "No groups", "description": "Looks like you don’t add any groups!", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search."}, "skillTable": {"name": "Name"}}, "addSkillModal": {"title": "Add group"}, "updateSkillModal": {"title": "Edit group"}, "editSkillModal": {"title": "Edit top-level skill", "subSkillTitle": "Edit sub-skill"}, "moveSkillModal": {"title": "Move to group"}, "addSubSkillModal": {"title": "Add skill", "subtitle": "Subordinate to <0>{{name}}</0> skill"}, "removeSkillAlert": {"title": "Delete skill", "content": "You're about to permanently delete the <0>{{name}}</0> skill.<1></1> This will remove <0>{{name}}</0> from all associated jobs and candidates.<1></1> The action cannot be undone. Are you sure you want to proceed?"}, "bulkRemoveSkillAlert": {"title": "Delete skills", "content": "You're about to permanently delete <0>{{count}}</0> skill(s).<1></1> This will remove the selected skill(s) from all associated jobs and candidates.<1></1> The action cannot be undone. Are you sure you want to proceed?"}, "removeGroupSkill": {"title": "Delete group", "content": "You're about to permanently delete the <0>{{name}}</0> group.<1></1> This will remove all skills under this group from all associated jobs and candidates.<1></1> The action cannot be undone. Are you sure you want to proceed?"}, "updateSkillAlert": {"title": "Edit skill"}, "moveSkillUnder": "Move <span class='mx-1 font-medium text-gray-900'>{{name}}</span> under the wing skill group", "bulkMoveSkillUnder": "Move <span class='font-medium text-gray-900'>{{numberCount}}</span> selected skill(s) under the wing skill group", "addGroup": "Add group", "groupName": "Group name", "editGroup": "Edit group", "skillName": "Skill name", "subordinateTo": "Subordinate to", "editSkill": "Edit skill", "addSkill": "Add skill", "mergeSkills": "Merge skills", "mergeSkillDescription": "Choose a primary skill to replace the <span class='font-medium text-gray-900'>{{numberCount}}</span> selected skills across all related jobs and profiles.", "primarySkill": "Primary skill", "skillTable": {"groups": "groups", "skillName": "Skill name", "skillGroup": "Group", "groupName": "Group name", "skillJobs": "Jobs", "skillCount": "Skills", "skillProfiles": "Profiles", "skillUpdated": "Last updated"}, "group": "Groups", "report": {"noChangeFromPreviousTime": "No change from previous time", "statistics": {"profiles": "Total candidates with the skill", "profilesHelpInfo": "Total number of candidates linked to this skill", "jobs": "Total jobs with the skill", "jobsHelpInfo": "Total number of jobs linked to this skill", "hiredJobs": "Total jobs hired", "hiredJobsHelpInfo": " Total number of jobs that were successfully filled (hired) with this skill", "jobTitle": "Job title", "candidates": "Candidates", "createdDate": "Created date", "publishDate": "Publish date", "closeDate": "Close date", "filledDate": "Filled date", "hiredCandidates": "Hired candidates", "name": "Name", "job": "Job", "stage": "Stage", "source": "Source"}, "empty": {"noDataCollect": "No data collected yet", "noResultsFound": "No results found"}, "overtimeReport": {"title": "Skill analytics: Candidates and jobs over time", "titleHelpInfo": "The chart shows trends of candidates and jobs over time for the selected skill, highlighting supply-demand gaps. Use this to analyze recruitment trends and plan skill development strategies.", "profiles": "Candidates with the <0>{{name}}</0> skill", "jobs": "Jobs requiring the <0>{{name}}</0> skill", "profilesGrowth": "Cumulative Candidates with the <0>{{name}}</0> skill", "jobsGrowth": "Cumulative Jobs with the <0>{{name}}</0> skill", "profilesTitle": "Candidates", "jobsTitle": "Jobs", "cumulativeGrowth": "Cumulative growth", "cumulativeGrowthInfo": "The chart shows cumulative candidates and jobs for selected skill, highlighting overall growth.", "supplyDemandTrends": "Supply-Demand trends", "supplyDemandTrendsInfo": "The chart shows trends of candidates and jobs over time for the selected skill, highlighting supply-demand gaps."}}, "skillDescription": "Description", "skillDescriptionPlaceholder": "Add description for skills", "skillSimilar": {"title": "Equivalent skills", "aiSuggest": "AI suggest", "placeholder": "Input equivalent skills", "aiSuggestTooltip": "AI suggest equivalent skills", "canNotUseAISuggestTooltip": "Select up to 100 skills to enable AI suggest."}, "tabs": {"skills": "Skills", "settings": "Settings"}, "settingTitleAllowAddSkill": "Allow to add new skills", "settingDescriptionAllowAddSkill": "When enabled, anyone can create new skills directly while editing jobs/ profiles."}, "positions": {"title": "Positions", "description": "Manage positions to identify skill gaps and personalize team member development.", "searchInput": "Search by name", "addPositions": "New position", "allStatus": "Status", "positionTable": {"positionName": "Position name", "positionStatus": "Status", "positionSkill": "Skills", "position": "Position", "description": "Description"}, "formPlaceholderDescription": "Enter the position description here, include key area of responsibility, requirements, benefits, etc", "titleCreatePositions": "Create a new position", "titleEditPositions": "Edit the position", "descriptionCreatePositions": "Define role requirements and map essential skills.", "generateAIDescription": "Input position name to generate a description", "empty": {"title": "No postions", "description": "Looks like you don’t add any positions!", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search."}, "positionStatus": {"active": "Active", "archived": "Archived", "draft": "Draft"}, "removePositionAlert": {"deleteTitle": "Delete position", "deleteDescription": "Are you sure you want to delete <0>{{name}}</0>? This action cannot be undone.", "canNotDeleteTitle": "Can not delete position", "canNotDeleteDescription": "The position is linked to profiles in Career navigation, so it cannot be deleted."}, "tab": {"position": "Positions", "careerPath": "Career paths"}, "careerPathNew": "New career path", "careerPathAdd": "Add career path", "careerPathEdit": "Edit career path", "careerPathTable": {"careerPathName": "Career path", "skillsMatch": "Skills match", "lastUpdated": "Last updated"}, "emptyCareerPath": {"title": "No career paths", "description": "Looks like you don’t add any career path!", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search."}, "removeCareerPathAlert": {"deleteTitle": "Delete career path", "deleteDescription": "Are you sure you want to delete <0>{{position}} -> {{nextPosition}}</0>? This action cannot be undone.", "canNotDeleteTitle": "Can not delete career path", "canNotDeleteDescription": "The career path is linked to profiles in Career navigation, so it cannot be deleted."}}, "locations": {"title": "Locations", "description": "Manage all your company locations here.", "searchInput": "Search by name or country", "addLocationButton": "Add location", "tab": {"locationEmpty": {"title": "No locations", "description": "Looks like you don't add any locations!", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search."}, "locationTable": {"name": "Name", "country": "Country"}}, "addLocationModal": {"title": "Add location", "actions": {"cancel": "Cancel", "add": "Add"}}, "editLocationModal": {"title": "Edit location", "actions": {"save": "Save"}}, "badge": {"headQuarter": "Head quarter"}, "updateLocationAlert": {"title": "Edit location"}, "removeLocationAlert": {"title": "Delete location", "content": "Are you sure you want to delete <0>{{name}}</0>? This action cannot be undone.", "content_related_job": "Are you sure you want to delete <0>{{name}}</0>? This location will permanently detach from any features currently associated with it.", "actions": {"cancel": "Cancel", "delete": "Delete"}, "label_delete_location": "Change {{count}} job(s)'s location from <0>{{name}}</0> to "}}, "emailTemplates": {"title": "Email templates", "description": "Manage all your email templates here.", "searchInput": "Search by name", "addEmailTemplateButton": "New template", "table": {"category": "Category", "name": "Name", "label": {"default": "<PERSON><PERSON><PERSON>"}, "tableEmpty": {"title": "No email templates", "description": "Create personalized email templates to send to candidates or hiring team. Saving time, saving effort.", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search.", "buttonAddNew": "Create new template"}, "hoverDeleteTitle": "Delete email template", "hoverUpdateTitle": "Update email template"}, "removeEmailTemplate": {"title": "Delete email template", "description": "Are you sure you want to delete <0>{{name}}</0>? This action can not be undone.", "actions": {"cancel": "Cancel", "remove": "Delete"}}, "updateEmailTemplate": {"title": "Edit email template"}, "addNewEmailTemplate": {"title": "Create new template"}, "emailSubject": "Email subject", "content": "Content", "placeholderContent": "Input template email content..."}, "account": {"title": "Your account", "description": "Manage your account settings", "heading": {"picture": "Account picture", "pictureCHUB": "Avatar", "general": "General"}}, "emailSetting": {"title": "Email settings", "description": "Manage your email settings.", "emailSignature": "Email signature", "customizeEmailSignature": "Customize your email signature, applied to all emails sent from {{domain}}."}, "workspace": {"title": "General", "description": "Manage your workspace settings.", "heading": {"logo": "Logo", "general": "General"}, "uploadImage": {"title": "Adjust company logo"}, "change_url_modal": {"title": "Change URL", "description": "The Career Page & Career Hub URL will change when you update. Are you sure you want to continue?", "btn_change": "Change"}}, "customDomain": {"tabTitle": "Domains", "title": "Custom domain", "description": "Take ownership of your brand with a custom career page domain. ", "form": {"domain_required": "Domain is required", "domain_invalid": "Invalid domain format", "titleInput": "Your domain", "inputPlaceholder": "Input your domain.", "btnConnect": "Connect"}, "btnDeleteDomain": "Delete domain", "btnRefreshDomain": "Refresh domain status", "pendingStatus": {"pending": "Pending", "btnCheckDNSStatus": "Check DNS status", "timeCheck": "24 hours", "description": "After updating the DNS, it may take up to <0>{{timeCheck}}</0> for changes to propagate.", "description1": "Once you've completed the DNS configuration, return to this page and click the <0>{{btnCheckStatus}}</0> button to update the status.", "table": {"description": "To use your own domain, ensure your DNS records are configured with the following values. Please contact your domain administrator or technical team for assistance with the setup.", "name": "Name", "type": "Type", "value": "Value"}, "btnCopy": "Copy"}, "connectedStatus": {"connected": "Connected"}, "delete": {"title": "Delete domain", "descriptionPending": "Are you sure you want to delete this domain? This action cannot be undone.", "descriptionConnected": "Are you sure you want to delete this domain? Visitors will no longer access your page via this domain. This action cannot be undone."}}, "careers": {"tabTitle": "General", "title": "Careers page", "description": "Manage your company career page settings.", "descriptionLogo": "You can change your company logo in the", "form": {"pageInformation": "Page information", "ga4Integration": "Google Analytics 4 integration", "ga4InputLabel": "Google Analytics 4 measurement ID", "ga4Tooltip": "A unique identifier used in Google Analytics 4 to track data for a website or app.", "ga4IntegrationDescription": "Enter tracking ID to integrate Google Analytics 4 with your Career page. Data import can take up to 48 hours.", "pageTitle": "Page title", "companyDescription": "Company description", "companyDescriptionEngPlaceholder": "Introduce your company and highlight why it's a good place to work in English.", "companyDescriptionJaPlaceholder": "Introduce your company and highlight why it's a good place to work in Japanese.", "companyDescriptionHelperTextEng": "This content will appear at the top of your careers page when the page language is English.", "companyDescriptionHelperTextJa": "This content will appear at the top of your careers page when the page language is Japanese.", "updateButton": "Update", "departmentVisibility": "Department visibility", "departmentVisibilityDescription": "Choose the level of department that candidates can use to view and filter jobs on your careers page.", "pageLanguages": "Page languages", "pageLanguagesDescription": "Enable the languages you want to display on your career page.", "terms_and_conditions": "Terms and conditions", "terms_and_conditions_description": "By enabling this feature, your application process will only occur when the user confirms they have read the terms & conditions.", "terms_and_conditions_placeholder": "Input your company's terms & conditions information.", "default": "<PERSON><PERSON><PERSON>", "setAsDefault": "Set as default"}, "department": {"show_top_level": "Show only top-level department", "show_all": "Show sub-department if have any"}, "english": "English", "japanese": "Japanese"}, "disqualify_reasons": {"title": "Disqualified reasons", "description": "Manage all your disqualified reason here.", "add_new_reason": "New reason", "emptyData": {"title": "No reasons", "description": "Looks like you don't add any reasons!"}, "tab": {"disqualified_by_us": "Disqualified by us", "disqualified_by_candidates": "Disqualified by candidates", "rejected_by_us": "Disqualified by us", "rejected_by_candidate": "Disqualified by candidates", "table": {"reason": "Reason"}}, "disqualify_reasons_form": {"add_disqualify_reasons_modal": {"title": "Add disqualification reason"}, "edit_disqualify_reasons_modal": {"title": "Edit disqualification reason"}, "actions": {"cancel": "Cancel", "add": "Add", "save": "Save"}}, "remove_disqualify_reason_alert": {"title": "Delete disqualification reason", "content": "Are you sure you want to delete <0>{{name}}</0>? This action cannot be undone.", "actions": {"cancel": "Cancel", "remove": "Delete"}}, "edit_tooltip": "Edit reason", "delete_tooltip": "Delete reason"}, "custom_fields": {"displayConfig": {"search": "Search...", "showIntable": "Show in table", "hideAll": "Hide all", "hideInTable": "Hide in table", "showAll": "Show all"}, "banner": {"contactUs": "Contact us", "toolTipMaximumFields": "Maximum {{maximum}} fields", "maxLimitFieldsDescription": "You already reached the limit usage of the Custom fields ({{totalFields}}/{{maximum}}).", "limitFieldsDescription": "You are about to reach the limit for usage of the Custom fields ({{totalFields}}/{{maximum}})."}, "title": "Custom fields", "description": "Manage all the fields that appear across profiles and other records in the system here.", "candidate_tab": "Candidate", "job_tab": "Job", "contact_tab": "Contact", "placement_tab": "Placement", "company_tab": "Company", "tab": {"system_fields_table": {"name": "Name", "visibility": "Visibility", "profile": "Profile", "visible_to": "Visible to", "field_type": "Field type", "action": "Action", "client": "Client", "careerSite": "Career sites", "careerSiteHelpText": "Shows which fields are visible in the Career sites", "filter": "Filter", "filterHelpText": "Enable filters in Career sites"}}, "button_add_new": "New field", "form": {"title_add": "Add additional fields", "title_edit_candidate": "Edit candidate fields", "title_edit_job": "Edit job fields", "title_edit_company": "Edit company fields", "title_edit_contact": "Edit contact fields", "title_edit_placement": "Edit placement fields", "field_name_label": "Field name", "field_name_placeholder": "E.g. Social insurance number", "field_object_kind": "Object", "visible_to_label": "Visible to", "field_type_label": "Field type", "filed_type_placeholder": "Select"}, "additional_field": "Additional fields", "empty": {"title": "No additional fields", "description": "Looks like you don't add any additional fields!", "btn_add": "Add field"}, "permissions": {"everyoneName": "Everyone", "everyoneDescription": "<PERSON><PERSON>, Member, Limited Member", "adminMemberName": "<PERSON><PERSON>, Member", "adminMemberDescription": "", "adminName": "Admin", "adminDescription": ""}}, "referrals": {"title": "Career hub", "description": "Enable referrals so your team members can refer a candidate to a job.", "tab": {"referralMember": {"removeMemberAlert": {"title": "Remove member from Career hub", "subTitle": "Are you sure you want to remove <0>{{name}}</0>? They can be removed from Career hub without affecting their access to other systems.", "notiSuccess": "{{name}} has been removed from the career hub!"}, "removePendingMemberAlert": {"title": "Remove invitation", "subTitle": "Are you sure you want to remove invitation to <0>{{email}}</0>?", "notiSuccess": "{{email}} has been removed from the career hub!"}}, "referral_email": {"title": "New job email notifications", "description": "Everyone will receive weekly email notifications about new job openings in the referral program."}, "referral_policy": {"title": "Referral rewards policy", "switchPolicy": "Include a referral rewards policy.", "description": "Enable to display the referral rewards policy, including criteria for successful referrals and reward distribution details.", "notifications_success": "Update successfully", "link": "Use a link", "file": "Upload a document"}, "referralChubConfig": {"title": "Registration settings", "description": "Manage who can register and customize their registration method.", "switchRegisterLabel": "Allow registration", "everyone": "Everyone can register", "restrict_email_domain": "Only emails from specific domains can register", "domainEmailPlaceholder": "E.g., company.com", "loginMethods": "Login methods", "loginMethodsDescription": "Choose the login or registration method for career hub users."}, "referralChubDescription": {"title": "Career hub description", "description": "Description of your company to be displayed on the Career hub login.", "descriptionPlaceholder": "E.g., We’re a team of innovators shaping the future of technology with passion and dedication."}, "settings": "Settings", "members": "Members", "pendingMembers": "Pending Members", "claims": "<PERSON><PERSON><PERSON>", "claimsHelperInfo": "Once a referral is hired, the claim shows here for reward processing.", "candidate": "Candidate", "emptyClaims": {"title": "No claims yet", "description": "Once a referred candidate is hired, you’ll see the claim for processing and reward."}, "noSearchResultClaims": {"title": "No results found", "description": "There are no results that match your search."}, "searchByNameMailJobTitle": "Search by name, email or job title"}, "descriptionPolicy": "Input link to your referral rewards policy", "title_custom_portal": "Customize your careers hub", "agency_title_custom_portal": "Referrals setting", "agency_description_custom_portal": "Everyone can refer people to open jobs & share the job on social media.", "description_custom_portal": "Choose the correct settings for your team members when they use the referrals portal.", "description_enable_when_creating_new_job": "Automatically enable the Career hub feature for new jobs.", "title_job_protection": "Job protection by the department", "description_job_protection": "Only members in the job’s department can view this job on the Referral portal.", "department": "Department", "referral_only_text": "Referrals only", "referral_only_description": "Anyone can refer people to open jobs & share the job on social media.", "referral_job_text": "Referrals & internal applications", "referral_job_description": "Anyone can refer people to open jobs, share the job on social media, or apply for the job.", "job_only_text": "Internal applications only", "job_only_description": "Anyone can apply for open jobs.", "referral_text": "Enable on Career hub", "job_referral_only_description": "<0>Referrals only</0> - Anyone can refer people to open jobs & share the job on social media.", "job_referral_job_description": "<0>Referrals & internal applications</0> - Anyone can refer people to open jobs, share the job on social media, or apply for the job.", "job_job_only_description": "<0>Internal applications only</0> - Anyone can apply for open jobs."}, "tags": {"title": "Tags", "description": "Manage all the tags that appear in the candidate profiles and jobs here.", "searchInput": "Search by name", "addTagButton": "New tag", "add_tag": "Add tag", "tab": {"tagEmpty": {"title": "No tags", "description": "Looks like you don’t add any tags!", "titleSearch": "No results found", "descriptionSearch": "There are no results that match your search."}, "tagTable": {"name": "Name", "candidates": "Candidates", "jobs": "Jobs"}, "job": "Job", "candidate": "Candidate"}, "addTagModal": {"title": "Add tag", "actions": {"cancel": "Cancel", "add": "Add"}}, "editTagModal": {"title": "Edit tag", "actions": {"save": "Save"}}, "updateTagAlert": {"title": "Edit tag"}, "removeTagAlert": {"title": "Delete tag", "content": "Are you sure you want to delete <0>{{name}}</0>? This action can not be undone.", "content_profiles_count": "Are you sure you want to delete <0>{{name}}</0>? Please note that this tag will be removed from {{countProfile}} profiles. This action can not be undone.", "content_profile_count": "Are you sure you want to delete <0>{{name}}</0>? Please note that this tag will be removed from {{countProfile}} profile. This action can not be undone.", "content_jobs_count": "Are you sure you want to delete <0>{{name}}</0>? Please note that this tag will be removed from {{countProfile}} jobs. This action can not be undone", "content_job_count": "Are you sure you want to delete <0>{{name}}</0>? Please note that this tag will be removed from {{countProfile}} job. This action can not be undone", "actions": {"cancel": "Cancel", "delete": "Delete"}}}, "interviewKits": {"title": "Interview kits", "description": "Manage all your interview kits here.", "table": {"tableEmpty": {"titleSearch": "No results found", "descriptionSearch": "There are no results that match your search.", "buttonTitleSearch": "", "titleNotFound": "No interview kits", "descriptionNotFound": "Looks like you don’t add any interview kits!", "buttonNotFound": "Add interview kit"}}, "form": {"descriptionName": "E.g. Sales Associate Interview kit"}, "searchByName": "Search by name", "modal": {"createInterviewKit": "Create interview kit", "descriptionCreateInterviewKit": "Interview kit is a critical tool for evaluating candidates.", "editInterviewKit": "Edit interview kit", "descriptionEditInterviewKit": "Interview kit is a critical tool for evaluating candidates.", "previewInterviewKit": "Preview interview kit"}, "inputSectionName": "Input section name", "rateCandidateSkill": "E.g. Rate candidate's communication skills.", "describeYourExp": "E.g. Describe your experience in handling customer complaints."}, "profileTemplates": {"title": "Profile templates", "description": "Manage all your profile templates here.", "table": {"tableEmpty": {"titleSearch": "No results found", "descriptionSearch": "There are no results that match your search.", "buttonTitleSearch": "", "titleNotFound": "No profile templates", "descriptionNotFound": "Looks like you don’t add any profile templates!", "buttonNotFound": "Add template"}, "default": "<PERSON><PERSON><PERSON>"}, "form": {"defaultTemplateName": "Set as <PERSON><PERSON><PERSON>", "titleTemplateName": "Template name", "descriptionTemplateName": "E.g. Harvard resume template", "descriptionGuideline": "E.g. This template outlines the recommended approach for internal recruiting opportunities, including key competencies and highlighted skills to effectively showcase your performance.", "titleTemplateHeader": "TEMPLATE HEADER", "fullName": "Full Name", "id": "ID:", "titleDate": "Date", "titleUserID": "ID", "titleAvatar": "Avatar", "titleCompanyLogo": "Company logo", "titleEmail": "Email", "titlePhoneNumber": "Phone Number", "watermark": "Add company name as watermark", "titleInformationDisplayedInTemplate": "the INFORMATION displayed in this template", "descriptionInformationDisplayedInTemplate": "Click on the sections below to add them to your template."}, "searchByName": "Search by name", "modal": {"createProfileTemplate": "Create profile template", "editProfileTemplate": "Edit profile template", "profileTemplateStep01": "Select a template style to start creating profile template", "profileTemplateStep02": "Select the content sections to display in profile template", "stepIndex": "Step {{step}}/{{total}}", "backToStep1": "Back to template style"}, "inputSectionName": "Input section name", "completion": "Completion", "customSection": "Custom section", "workExperience": {"title": "Job title", "company": "Company name", "datetime": "Start date - End date", "location": "Location", "description": "Description"}, "education": {"major": "Major", "degree": "Degree", "school": "School", "datetime": "Start date - End date", "description": "Description"}, "birthday": {"age": "Display Age", "date_and_age": "Display Date & Age"}}, "security": {"title": "Security", "description": "Manage your company’s security features to ensure safe and secure access for all users.", "enabledSSO": {"title": "Enable Single Sign-On (SSO)", "activated": "Activated", "description": "Enable SSO to streamline and secure access to your company."}, "session": {"title": "Session management", "description": "Set the session and idle timeout durations for your Workspace. Member in your Workspace will be prompted to log back in after their session expires.", "neverExpires": "Never expires"}, "modal": {"disabledTitle": "Disable Single Sign-On (SSO)", "disabledDescription": "Disabling SSO will revert all members to the standard login method. Are you sure you want to disable SSO?", "enabledTitle": "Changes to member sign-in", "enabledDescription": "Enabling SSO will change the sign-in method for all members. Are you sure you want to proceed?", "updateIPWhiteListingTitle": "Update IP whitelisting", "updateIPWhiteListingDescription": "Once updated, only users accessing from the listed IP addresses will be allowed into the system.", "removeIPWhiteListTitle": "Remove IP address", "removeIPWhiteListDescription": "Are you sure you want to delete <0>{{ipAddress}}</0> from the whitelist? Users from this IP address won’t be able to access your system. This action cannot be undone."}, "form": {"modalTitle": "Authentication with Microsoft Entra", "modalDescription": "To enable Single Sign-On (SSO), please provide your Entra credentials.", "applicationIdLabel": "Application ID", "applicationIdDescription": "Input application ID", "clientIdLabel": "Client ID", "clientIdDescription": "Input client ID", "tenantIdLabel": "Tenant ID", "tenantIdDescription": "Input tenant ID", "clientSecretLabel": "Client secret key", "clientSecretDescription": "Input client secret key", "redirectURL": "Authorized redirect URIs"}, "idleTimerOptions": {"0": "Never expires", "5": "5 minutes", "15": "15 minutes", "30": "30 minutes", "45": "45 minutes", "60": "1 hour", "120": "2 hours", "180": "3 hours", "240": "4 hours"}, "ipManagement": {"title": "IP whitelisting", "description": "Only listed IP addresses can access the system when IP(s) whitelisted. Add trusted IPs to control and restrict access.", "addIPWhiteList": "Add IP to whitelist"}}, "permissions": {"moduleName": {"career_page": "Career page", "tenant_setting": "Workspace setting", "user_management": "User management", "template_setting": "Templates", "report": "View report", "company_management": "Company management", "manage_requisition": "Manage requisition", "request_requisition": "Request requisition", "job_management": "Job management", "placement": "Revenue/Placement", "access_career_hub": "Access Career hub", "manage_career_hub": "Manage Career hub", "export_candidates": "Export candidates", "course_management": "Course management", "export_jobs": "Export jobs", "invite_user_clients": "Invite client user"}, "moduleNameDescription": {"career_page": "Customize and update your company's career page effortlessly.", "tenant_setting": "Tailor the platform to your workspace’s unique needs (tags, disqualify reasons, locations...)", "user_management": "Easily manage and control user access and permissions within the platform.", "template_setting": "Customize pre-designed templates for enhanced efficiency and consistency.", "report": "Gain valuable insights into recruitment performance with detailed reports and analytics.", "company_management": "Manage company information and enhance relationships.", "manage_requisition": "Can create new job requisitions and create jobs from approved requisitions.", "request_requisition": "Can create new job requisitions but not create jobs once they are approved.", "job_management": "Manage jobs, candidates and talent pools for recruiting process.", "placement": "Manage placements and adjust profit splits accordingly.", "access_career_hub": "Browse jobs, submit applications, and refer candidates in the Career hub.", "manage_career_hub": "Grants access to Career Hub settings, including managing members, jobs, and referral settings.", "export_candidates": "Export data from the candidate listing.", "course_management": "Manage courses in Career Hub.", "export_jobs": "Export data from the job listing.", "invite_user_clients": "Allows users to invite client users to access the platform."}, "moduleNameDropdown": {"job_management_full": "All jobs", "job_management_team": "Team jobs", "job_management_owned": "Owned jobs", "company_management_full": "All companies", "company_management_team": "Team companies", "company_management_owned": "Owned companies", "placement_full": "All placements", "placement_owned": "Placements they own", "report_full": "All data", "report_team": "Team data", "report_owned": "Owned data"}}, "plan": {"plan": "Plan", "planDescription": "Manage your subscription and usage.", "trial": "Trial", "trialLeft": "{{trial}}d left", "trialPlan": "Trial Plan", "youAreCurrentlyOnThe": "You are currently on the", "yourCurrentPlan": "Your current plan", "yourCurrentPlanHasEnded": "Your {{tenantPlanName}} plan has ended. To continue, please consider upgrading your plan.", "numberOfUsers": "Number of users", "tooltipNumberOfUsers": "Number of active users in {{domain}}.", "numberOfCandidate": "Number of candidate", "tooltipNumberOfCandidate": "Number of profiles added in your company.", "numberOfJobs": "Number of jobs", "tooltipNumberOfJobs": "Number of active jobs in your company.", "numberOfCredits": "Number of credits", "tooltipNumberOfCredits": "Number of credits used for locating contact information.", "numberOfChubUsers": "Number of Career hub users", "tooltipNumberOfChubUsers": "Number of active users in your Career Hub", "remainTrialDaysLeft": "{{remainingTrialDays}} trial days left", "planExpired": "plan. Plan expired on {{date}}.", "upToNoOfUsers": "Up to {{noOfUsers}} users", "unlimitedUsers": "Unlimited users", "upToNoOfJobs": "Up to {{noOfJobs}} active jobs", "unlimitedJobs": "Unlimited jobs", "upToNoOfCandidates": "Up to {{noOfCandidates}} candidates", "upToNoOfCredits": "Up to {{noOfCredits}} credits", "unlimitedCandidates": "Unlimited candidates", "customerSuccessManager": "Customer Success Manager", "allFeatures": "All features", "free": "Free", "getAQuote": "Get a quote", "thisPlanIncludes": "This plan includes", "currentPlan": "Current Plan", "contactSales": "Contact sales", "comparePlans": "Compare plans", "unlimited": "Unlimited", "discoverFeatures": "Discover {{domain}} features", "discoverFeaturesForm": {"feature01Title": "Source & Attract", "feature01Description01": "Chrome sourcing extension", "feature01Description02": "Customizable career page", "feature01Description03": "Multiple job boards integration", "feature01Description04": "Share jobs across several networks", "feature01Description05": "Bulk & mass CV/resume upload", "feature02Title": "Automate & Hire", "feature02Description01": "Customizable hiring pipeline", "feature02Description02": "Automation Emails by events", "feature02Description03": "Parse and search CVs", "feature02Description04": "Department hierarchy", "feature02Description05": "Multi-locations", "feature02Description06": "Integrated career site with Google Analytics 4", "feature03Title": "Track & Collaborate", "feature03Description01": "Customizable candidate profiles", "feature03Description02": "Candidates quick search", "feature03Description03": "Filtered profile fields", "feature03Description04": "Interview kits & Evaluation forms", "feature03Description05": "Event scheduler and calendar sync", "feature03Description06": "Candidate self-scheduling", "feature03Description07": "Automation pipeline tracking", "feature03Description08": "Advanced access rights", "feature03Description09": "Team notes, tasks & mentions", "feature03Description10": "Talent pools", "feature03Description11": "User permissions, roles and accesses", "feature04Title": "Report & Analyze", "feature04Description01": "General overview report", "feature04Description02": "Job reports (time to hire/time to hires,...)", "feature04Description03": "Hiring funnel reports (sources/channels)", "feature04Description04": "Candidate reports (acquisition by channel, source,...)", "feature04Description05": "Team productivity reports (sources, feedback, interviews, hires, notes,...)"}, "questionPlanOptions": "Question about Plan options?", "descriptionQuestionPlanOptions": "Have a question about users, jobs, candidate slots or any features? \nLeave us a message, and we'll get back to you quickly!", "planContent01": "Effortlessly expand your reach, centralize job posting management and stand out with enhanced visibility while saving your valuable time.", "planContent02": "Effortlessly expand your reach, centralize job posting management and stand out with enhanced visibility while saving your valuable time. <br />Contact Admin to upgrade.", "planContent03": "Seamlessly connect with potential candidates through <br /> personalized referrals. Monitor and track your referral in 1 place.", "planContent04": "Accelerate your hiring process with job requisition. Streamline <br /> collaboration with seamless communication, efficient workflows!", "planContent05": "Get the most suitable candidates recommended for the job by upgrading your plan now!", "planContent06": "Help your candidates find their ideal jobs by upgrading your plan.", "planContent07": "Upgrade your plan to access the job import feature and streamline your recruitment process.", "planContent08": "Upgrade your plan to protect your organization's data with advanced security features", "planContent09": "Upgrade your plan to access detailed records of all activities and changes within the platform.", "reachLimitedPlan": "Your have reached the limit usage of {{name}} plan.", "reachLimitedPlanUpgrade": "You have reached the limit usage of {{name}} plan. Contact admin to upgrade.", "reachLimitedPlanButton": "Contact us", "reachLimitedPlan01": "to upgrade", "job_board": "Job board", "requisition": "Requisition", "referral": "Referral", "ai_resume_parser": "AI resume parser", "custom_field": "Custom fields", "additional_field": "Custom fields", "recommendation": "Recommendation for Recruiter", "cv_template": "Profile templates", "employee_profile": "Employee profile", "import_job": "Import", "audit_log": "Audit logs", "security_setting": "Privacy and Security", "skill_management": "Skill management", "career_page_builder": "Career page builder", "ai_writer": "AI writer", "find_contact": "Find contact", "resumee_builder": "Resume Builder", "notification": "Notification", "import_candidate": "Import candidate", "profile_view": "Profile view", "ai_skill_parser": "AI skill parser", "recommendation_weight": "Recommendation weight", "custom_domain": "Custom domain", "hireforce_mail": "TalentsForce Mail", "career_navigator": "Career Navigator", "learning_management_system": "Learning Management System", "import_tenant_course": "Import Tenant Course", "application_form": "Application Form", "company": "Company", "placement": "Placement", "ip_whitelisting": "IP Whitelisting", "ai_assistant": "AI Assistant"}, "hiringPipeline": {"title": "Hiring pipeline templates", "description": "Manage all your pipeline templates here.", "dialog": {"create": {"title": "Create new template", "description": "Manage candidate by setting up a hiring pipeline for jobs"}, "edit": {"title": "Edit template"}}, "table": {"inputSearch": "Search by name", "empty": {"title": "No results found", "description": "There are no results that match your search.", "btnAddNew": "Add new"}, "columnsName": {"name": "Name", "stages": "Stages", "lastUpdated": "Last updated"}}}, "requisitions": {"title": "Requisitions", "description": "Create job requisitions and set up approval flows.", "form": {"placeholderApprovalFlow": "Input approval flow name", "labelApprovalFlow": "Approval flow", "descriptionApprovalFlow": "Assign approvers and set the minimum required number for approval."}, "stepIndex": "Step {{itemIndex}}", "approvers": "Approver(s)", "requiredApprovals": "Required approvals", "searchInput": "Search by name", "empty": {"title": "No approval flows", "description": "Looks like you don’t add any approval flow!", "titleSearch": "No approval flows", "descriptionSearch": "There are no results that match your search."}, "table": {"numberOfSteps": "Number of steps", "lastUpdated": "Last updated"}}, "rolePermissions": {"admin": "Full access in {{domain}}: jobs, candidates, settings, report and more.", "limitedMember": "Take notes and give feedback on the candidates within the assigned job.", "member": "Can access, manage jobs that assigned to and view all settings."}, "profiles": {"activePage": {"title": "Profile activation", "description": "Activate your profile to share your information for recruitment \n opportunities.", "modalActive": {"title": "Terms and Conditions agreement", "description1": "By activating my profile, I confirm that I have read the", "description2": " Terms and Conditions ", "description3": "and agree to {{companyName}} storing my personal data for processing my job application."}, "alert": {"createFail": "Cannot create profile at this time!", "deletedFile": "File deleted.", "updatePermissionSuccess": "Shared permission updated"}}, "tab": {"information": "Information", "resume/cv": "Resume/CV", "files": "Files"}, "title": "Your profile", "description": "Set up your profile to explore recruitment opportunities."}, "import": {"import": "Import", "history": "History", "upload_files": "Upload files", "mapping_data": "Mapping data", "next": "Next", "view_sample_file": "For best results, please view <0>sample file</0> and adjust your file accordingly.", "only_support_csv": "Only supported for csv, maximum {{numberOfRecord}} records, up to {{maxFile}}MB", "file_fields": "File fields", "job_fields": "Job fields", "profile_fields": "Candidate fields", "currently_importing_data": "We're currently importing your data...", "import_will_completed_shortly": "Your import will be completed shortly, and we will send you an email once when it's done.", "import_failed": "Import failed", "update_and_try_again": "An expected error occurred. Please update your file and try again.", "column": "Column", "row": "Row", "reason": "Reason", "try_again": "Try again", "file_name": "File name", "status": "Status", "objects": "Objects", "entries": "Entries", "uploaded_by": "Uploaded by", "import_date": "Import date", "reupload": "Re-upload", "empty_history": {"title": "No Import Files", "description": "Looks like you don’t import any files!", "titleSearch": "No result found", "descriptionSearch": "There's no results found that match your search", "import_file": "Import file"}, "created": "<0>{{num}}</0> imported", "updated": "<0>{{num}}</0> updated", "failed": "<0>{{num}}</0> failed", "no_action": "<0>{{num}}</0> ignored", "retryJobs": "<0>{{num}}</0> jobs failed skill-parsing", "job_id": "Job ID", "profile_id": "ID", "sources": {"jobs": {"title": "Jobs", "description": "Fill out required information and publish your job."}, "candidate": {"title": "Candidate", "description": "Fill out required information for the import."}, "course": {"title": "Course", "description": "Quickly bring in existing course content to save time and effort."}, "company": {"title": "Company", "description": "Fill out required information for the import."}}}, "audit_logs": {"title": "Audit logs", "description": "The Audit Log records all activities and changes within this workspace.", "table": {"from_to": "From - to", "user": "User", "event": "Event", "trackingIp": "IP Address", "date": "Date", "title_search": "No results found", "description_search": "There are no results that match your search.", "title_empty": "No activities", "description_empty": "It looks like there’s no activity to show right now."}}, "transferOwnerTitle": "Transfer ownership", "transferOwnerDescription": "Reassign your responsibilities and assets to another team member.", "team": {"addNewTeam": "New top-level team", "search": "Search by team name", "member": "Member", "teamsTitle": "Teams", "teamsDescription": "Manage all your teams here.", "addNewTeamModalTitle": "Add top-level team", "editTeamModalTitle": "Edit team", "deleteTopLevelTeam": "Delete top-level team", "deleteTopLevelTeamDescription": "You're about to permanently delete the <0>{{teamName}}</0> team. This will:<1>Remove all the sub-teams under <0>{{teamName}}</0></1><1>Unassign all users currently linked to it</1>The action cannot be undone. Are you sure you want to proceed?", "addSubTeamTitle": "Add sub-team", "addSubTeamDescription": "Subordinate to <0>{{teamName}}</0> team", "moveTeam": "Move team", "moveTeamUnder": "Move <span class='mx-1 font-medium text-gray-900'>{{name}}</span> under the following team:", "teamEmptyTitle": "No teams", "teamEmptyDescription": "Looks like you don’t add any teams!", "moveTeamTop": "Move <span class='mx-1 font-medium text-gray-900'>{{name}}</span> into a top-level", "deleteSubTeamDescription": "Are you sure you want to delete <0>{{teamName}}</0> team? Deleting this team will also remove all of the users who are associated with. This action cannot be undone.", "deleteSubTeamTitle": "Delete sub-team", "titleSearchEmpty": "No results found", "descriptionSearchEmpty": "There are no results that match your search.", "name": "Name", "allTeams": "All Teams"}, "somethingWentWrong": "Something went wrong"}