{"add_member": "Add member", "filter_by_member": "Filter by member", "filter_by_job": "Filter by Job", "filter_by_status": "Filter by Status", "filter_by_stage": "Filter by Stage", "filter_by_currency": "Filter by <PERSON><PERSON>", "filter_by_owner": "Filter by Owner", "filter_by_hiring_member": "Filter by Hiring member", "filter_by_hired_by": "Filter by who has been marked as <PERSON><PERSON> by", "filter_by_location": "Filter by Location", "filter_by_talent_pool": "Filter by Talent Pool", "filter_by_department": "Filter by Department", "filter_by_tag_name": "Filter by Tag {{name}}", "filter_by_pool_members": "Filter by pool members", "filter_by_level": "Filter by Job level", "filter_by_applied": "Filter by applied", "filter_by_cv": "Filter by Resume", "filter_by_company": "Filter by Company", "filter_by_save_job": "Filter by Save Job", "collapse_view": "Collapse view", "expand_view": "Expand view", "visibleTo": "Visible to", "copied_link": "Copied link", "copy_link": "Copy link", "the_final_decision_maker_for_candidate": "The final decision-maker for candidate acceptance or rejection.", "can_access_all_jobs": "Can access all jobs", "resend_email": "Resend email", "remove_member": "Remove member", "preview": "Preview", "edit": "Edit", "canNotEdit": "Cannot edit", "delete": "Delete", "canNotDelete": "Cannot delete", "note": "Note", "createdAtDate": "Created at {{date}}", "lastActivityDate": "Last activity {{date}}", "updatedAtActivityDate": "Updated at {{date}}", "sourcedByUser": "Sourced by {{user}}", "ownerByUser": "Owned by {{user}}", "viewCV": "View CV", "viewFile": "View file", "refreshFile": "Refresh file", "interview": "Interview", "disqualify": "Disqualify", "addAttendee": "Add attendee", "linkCopied": "Link copied", "userMatchingWithJobDepartment": "Users matching with job departments can assist hiring teams.", "thePersonalizationTokenWillPopulate": "The personalization token will populate with the corresponding property value when you send the email template.", "uploadANewFile": "Upload a new file", "changeJobNotMadeArchived": "Changes to this job cannot be made, job is archived.", "changeJobNotMadeDisqualified": "Changes to this job cannot be made, candidate is disqualified.", "approvedRequisition": "You have just approved this requisition successfully.", "hasApprovedRequisition": "This requisition has been approved by other members.", "rejectedRequisition": "You have just rejected this requisition successfully.", "hasRejectedRequisition": "This requisition has been rejected by other members.", "approvalApproved": "Your approval step has fully approved by other members.", "cannotCustomize": "Cannot customize", "dragToReorder": "Drag to reorder", "feedbackIsUsedInternally": "Feedback is used internally and will not be shared to the candidate.", "close": "Close", "previous": "Previous", "next": "Next", "addApprover": "Add approver", "approvalWorkflowAdmin": "Approval workflow includes admin system. You can edit approvers later.", "download": "Download", "headcount": "Headcount", "re_parser": "Re-parse", "filter_by_members": "Filter by Members", "filter_by_hired_date": "Filter by Hired date", "filter_by_start_date": "Filter by Start date", "filter_by_created_at": "Filter by Created at", "currentContactHaveNotBeenInvited": "Current contacts haven’t been invited to this job yet.", "viewDetails": "View details", "onboardDate": "Onboard date", "endOfProbation": "End of probation", "deleteFeedback": "Deleted feedback.", "canAccessAllTalentPool": "Can access all talent pools.", "thisPipeLineTemplateWillBeUsed": "This pipeline template will be used as a default on all new jobs", "viewPlacementInfo": "View placement info", "tryAgain": "Try again", "visibilityProfile": "Shows which fields are visible in the profiles on the Career hub", "visibilityApplicant": "Shows which fields are visible in the Applicant profiles", "visibilityJobs": "Shows which fields are visible in the Jobs", "visibilityContact": "Shows which fields are visible in the Contacts", "visibilityPlacement": "Shows which fields are visible in the Placements", "visibilityCompanies": "Shows which fields are visible in the Companies", "profile_file_download": "Download file", "profile_file_delete": "Delete file", "profile_file_permission": "Permission file", "profile_file_permission_disable": "Cannot modify files uploaded by other Admins/ Members.", "profile_file_delete_disable": "Cannot modify files uploaded by other Admins/ Members.", "not_have_permission": "You don’t have permission", "completionHelper": "Shown the percentage of fields you’ve completed.", "thisFieldIsBlocked": "This field is blocked", "hiredDate": "Hired date", "clientShare": "Share with hiring portal", "hiddenShare": "Hide from the hiring portal", "clientVisibilityApplicant": "Shows which fields are visible from the client view", "client_cannot_view": "Client cannot view", "help_center": "Help center", "move": "Move", "addSubTeam": "Add sub-team", "editTeam": "Edit team", "filter_by_team": "Filter by Team", "filter_by_teams": "Filter by Teams", "filter_by_job_location": "Filter by Job's locations", "filter_by_job_department": "Filter by Job's departments", "filter_by_referral_by": "Filter by <PERSON><PERSON><PERSON>", "filter_by_source": "Filter by Source", "filter_by_created_by": "Filter by who created hires", "youDoNotHaveAccessToThisJob": "You don't have access to this job.", "viewHireInformation": "View hire information {{time}}", "hiredTime": "Hired time", "filter_by_remote_status": "Filter by Remote Status", "filter_by_candidates": "Filter by Candi<PERSON>", "filter_by_companies": "Filter by Companies", "menuToolbar": {"heading": "Heading", "bold": "Bold", "italic": "Italic", "underline": "Underline", "textColor": "Text color", "insertLinks": "Insert links", "insertImages": "Insert images", "bulletedList": "Bulleted list", "numberedList": "Numbered list", "expand": "Expand", "attachFiles": "Attach files", "mention": "Mention", "moreOptions": "More options", "alignLeft": "<PERSON><PERSON> left", "alignCenter": "Align center", "alignRight": "Align right", "highlightText": "Highlight text", "quotes": "Quotes", "clearFormat": "Clear format"}, "skillCreatedByUser": "Created by {{fullName}}, latest update on {{timeFormatted}}", "skillCreatedByUnknown": "Latest update on {{timeFormatted}}", "previousPeriod": "Previous period", "visibilityMergeAction": "Please select at least two specific skills to enable merge skills.", "visibilityExportSkillAction": "No skill to export.", "view": "View: {{viewName}}", "filter_by_assignee": "Filter by assignee", "retryJobs": "Reload in a few minutes to see results", "filter_by_sourced_name": "Filter by sourced name", "tool_tip_referral": "Number of candidates referred within the selected time frame, along with job details, referral sources, and rewards earned.", "avoidExportPDF": "Select fewer than 100 candidates to enable export PDF.", "equivalentSkills": "For better recommendation accuracy, the system will suggest the standard skill when an equivalent skill is entered", "filterJobsByStatus": {"created": "Filter by jobs imported", "updated": "Filter by jobs updated", "failed": "Filter by jobs failed to import", "no_action": "Filter by jobs ignored", "retryJobs": "Filter by jobs failed skill-parsing"}, "warning_application_field_deleted": "This field may be deleted, resulting in information loss", "placementId": "Placement ID", "filterBySkills": "filter by skills", "filterCoursesByStatus": {"created": "Filter by courses imported", "updated": "Filter by courses updated", "failed": "Filter by courses failed to import"}, "filterProfilesByStatus": {"created": "Filter by candidates imported", "updated": "Filter by candidates updated", "failed": "Filter by candidates failed to import", "no_action": "Filter by candidates ignored"}, "filterCompaniesByStatus": {"created": "Filter by companies imported", "updated": "Filter by companies updated", "failed": "Filter by companies failed to import", "no_action": "Filter by companies ignored"}}