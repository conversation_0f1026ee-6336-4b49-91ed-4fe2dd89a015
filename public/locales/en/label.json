{"viewSalary": {"covertSalary": " mil", "negotiableSalary": "Negotiable", "salaryTo": "Up to {{salaryTo}}", "salaryFrom": "From {{salaryFrom}}"}, "paging": {"labelEndOfList": "You’ve reached the end of the list!"}, "optionJobStatus": {"internal": "The job is published internally and not visible on the Career page; only those with the link can apply.", "publish": "The job is published and active on your career page. Anyone can apply.", "archived": "The job has been archived, is closed for applications and moved into the archived job list."}, "optionJobAction": {"viewJob": "View job", "editJob": "Edit job", "duplicate": "Duplicate job", "shareJob": "Share job", "cannotShareJob": "Cannot share", "deleteJob": {"title": "Delete job", "description": "Are you sure you want to delete <0>{{jobTitle}}</0>? This action cannot be undone.", "descriptionDeleted": "You’re about to delete the job <0>{{jobTitle}}</0> and all related data. This action cannot be undone. Are you sure you want to proceed?"}}, "jobCardLabel": {"new": "New", "sourced": "Sourced", "applied": "Applied", "Screening": "Screening", "Interview": "Interview", "Offer": "Offer", "Hired": "<PERSON><PERSON>"}, "placeholder": {"select": "Select", "input": "Input", "search": "Search", "member": "Member", "members": "Members", "teams": "Teams", "job": "Job", "status": "Status", "stage": "Stage", "currency": "<PERSON><PERSON><PERSON><PERSON>", "tag": "Tag", "tags": "tags", "owner": "Owner", "sourced": "Source", "company": "Company", "hiring_member": "Hiring member", "hired_by": "Hired by", "visibleTo": "Visible to", "dueDate": "Due date", "search_by_name_phone_email_link": "Search by name, phone, email, link", "formTo": "From - to", "assignTo": "Assign to", "selectDate": "Select date", "month": "Month", "week": "Week", "year": "Year", "min": "Min", "max": "Max", "email": "Email", "clickToEnterValue": "Click to enter a value", "inputEmailSubject": "Input email subject", "inputEmailTemplate": "Input email template name", "inputStageName": "Input stage name", "optionNumber": "Option {{number}}", "min_salary": "Monthly salary", "departments": "departments", "search_by_job_title_skill": "Search by id, name, skill", "created_by": "Created by", "assignedTo": "Assigned to", "source": "Source", "candidates": "Candidates", "companies": "Companies", "relatedTo": "Related to", "assignee": "Assignee", "referrer": "<PERSON><PERSON><PERSON>", "referType": "Refer type", "sourcedName": "Sourced name", "fieldFormat": "{{field}}, comma, space or enter separated", "searchLearningLibrary": "Search by course title, skills name, description content", "inputCompanyName": "Input company's name", "inputSchoolName": "Input school's name"}, "tab": {"any_match": "Any match", "match_all": "Match all", "candidates": "Candidates", "analytics": "Analytics", "details": "Details", "notes": "Notes", "job_boards": "Job boards", "activities": "Activities"}, "job_status": {"internal": "Internal", "publish": "Publish", "archived": "Archived", "draft": "Draft"}, "status": {"pending": "Pending"}, "any_open_job": "Any open jobs", "tag_any_match": "(any match)", "tag_match_all": "(match all)", "jobs": "jobs", "tags": "tags", "companies": "Companies", "location": "Location", "locations": "locations", "industry": "Industry", "industryCount": "industry", "countLocation": "{{countLocation}} locations", "referral": "referral", "referrals": "referrals", "assign_job": "Assign job", "new": "New", "edit_pipeline": "Edit pipeline", "add_interview_kit": "Add interview kit", "savingNote": "Saving note", "everyone": "Everyone", "follow_up_task": "Follow up task", "dueDate": "due date", "add_to_stage": "Add to stage", "interview_kit": "Interview kit", "interview_kits_template": "interview kits template", "no_interview_kits": "No interview kits.", "edit_interview_kit": "Edit interview kit", "preview_interview_kit": "Preview interview kit", "newStage": "New stage", "addToHires": "Add to <PERSON>res", "editToHires": "Edit to <PERSON><PERSON>", "editStage": "Edit stage", "tooltip": {"maximumStages": "Maximum {{number}} stages"}, "share": "Share", "refer": "<PERSON><PERSON>", "reason": "Reason", "email": "Email", "emailExists": "Email exists.", "composeEmail": "Compose email", "emailToCandidate": "Email to candidate", "emailToAttendees": "Email to attendees", "talentPools": "Talent pools", "disqualifyCandidate": "Disqualify candidate", "markAsHired": "<PERSON> as hired", "editHireInformation": "Edit hire information", "hireDate": "Hire date", "startDate": "Start date", "hiredBy": "Hired by", "evaluation": "Evaluation", "note": "Note", "from": "From", "to": "To", "cc": "Cc", "bcc": "Bcc", "category": "Category", "jobCategory": "Job category", "employmentType": "Employment type", "experienceLevel": "Experience level", "skills": "Skills", "salary": "Salary", "salaryType": "Salary type", "currency": "<PERSON><PERSON><PERSON><PERSON>", "emailTemplate": "Email template name", "URL": "URL", "department": "Department", "name": "Name", "namePlaceholder": "Eg. Marketing", "assignJob": "Assign job", "unavailable": "Unavailable", "candidate": "Candidate", "job": "Job", "stage": "Stage", "stageType": "Stage type", "attendees": "Attendees", "organizer": "Organizer", "sendNotificationEmail": "Send notification email", "visibleTo": "Visible to", "adjustProfilePicture": "Adjust profile picture", "today": "Today", "yesterday": "Yesterday", "tomorrow": "Tomorrow", "createdAt": "Created at", "gaveReferralFor": "gave a referral for", "present": "Present", "time": "Time", "subject": "Subject", "noOptions": "No options", "powered_by": "Powered by", "movedTo": "Moved to {{stageName}}: ", "candidates": "Candidates", "administrator": "administrator", "hires": "<PERSON><PERSON>", "all": "All", "days": "days", "day": "day", "dragAndDrop": {"clickToUpload": "Click to upload", "orDragAndDrop": "or drag and drop"}, "formattedMonth": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "language": "Language", "canAccessThisJob": "can access this job", "draft": "Draft", "loading": "Loading", "of": "of", "endOfList": "End of list.", "members": "Members", "noMembersInvited": "No members invited.", "noMembersAdded": "No members added.", "disqualifyByUs": "Disqualify by us", "disqualifyByCandidate": "Disqualify by candidate", "emailContent": "Email content", "resend": "Resend", "candidateProfiles": "candidate profiles", "noCandidatesMatchThisJob": "No candidates match this job", "undefined": "Undefined", "defaultTemplate": "Full profile", "date": "Date", "year": "Year", "shareWithHiringPortal": "Share with hiring portal", "choos_from_files": "Choose from Files", "choose_from_computer": "Choose from computer", "cv_resume": "CV/Resume", "files": "Files", "no_files": "No Files", "noFilesDescription": "Looks like no file has been uploaded yet!", "memberOwner": "Owner", "moveClientSubmissionStage": "Move to submission stage", "moveClientSubmissionStageNew": "Move to Client submission stage", "contactDetail": "contact details", "companyDetail": "company details", "teams": "Teams", "member": "Member", "fonts": "Fonts", "fontSize": {"title": "Font size: {{fontSize}}", "small": "Small", "medium": "Medium", "large": "Large"}, "aA": "aA", "format": "Format", "teamsLowercase": " teams", "reportKPI": {"job_added": "Jobs added", "job_notes": "Jobs notes", "candidate_sourced": "Candidates sourced", "application_notes": "Applications notes", "stages_moved": "Stages moved", "disqualifications": "Disqualification", "tasks": "Tasks added", "interviews": "Interviews", "interview_feedbacks": "Interview feedback", "applicant_feedbacks": "Application feedback", "offers": "Offers", "hires": "<PERSON><PERSON>", "company_added": "Companies added", "company_notes": "Companies notes", "contact_added": "Contacts added", "contact_call_logs": "Contact call logs", "job_ordered": "Jobs ordered", "client_submissions": "Submission", "placements": "Placements", "sent_candidates": "Sent candidates", "candidate_added": "Add new candidate", "total_upload_resumes": "Upload resume/CV", "candidate_call_logs": "Candidate call logs", "jobs": "Jobs"}, "team": "Team", "description": "Description", "sharedWith": "Shared with", "taskDetails": "Task details", "tabs": {"interview": "Interviews", "task": "Tasks"}, "adjustImage": "Adjust image", "newTask": "New task", "imageRatio": "Image ratio", "template": "Template", "page": "Page", "rowsPerPage": "Rows per page", "uSearch": {"title": {"profiles": "CANDIDATES", "companies": "COMPANIES", "jobs": "JOBS"}, "navigate": "Navigate", "open": "Open", "empty": {"title": "No data", "searchTitle": "No results found", "searchDescription": "There are no results that match your search.", "description": "Enter a keyword to find what you're looking for quickly"}}, "phoneCall": "Log call", "callLogDirectionOptions": {"outbound": "Outbound", "inbound": "Inbound"}, "callLogOutComeOptions": {"answered": "Answered", "busy": "Busy", "left_message": "Left message", "no_answer": "No answer", "wrong_number": "Wrong number"}, "createBy": "Create By", "updateBy": "Update By", "provider": "Provider", "level": "Level", "type": "Type", "languages": "Languages", "countName": {"skills": "skills", "providers": "providers", "levels": "levels", "types": "types", "languages": "languages", "courses": "courses", "course": "course"}, "qualified": "Qualified", "disqualified": "Disqualified"}