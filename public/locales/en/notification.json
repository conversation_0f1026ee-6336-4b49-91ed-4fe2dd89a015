{"settings": {"teamMembers": {"success_sent_title": "<PERSON><PERSON><PERSON> sent!", "success_sent_content": "Invitation for {{email}} has been resent.", "success_sent_content_new_invitation": "An email has been sent to notify them to register for {{domain}}.", "success_remove_member": "{{fullName}} has been removed from this workplace!", "success_update_role_member": "Member account updated.", "success_sent_number_of_emails": "{{num}} email(s) were sent!", "membersDeleted": "Members removed."}, "departments": {"success_sent_title": "Department added!", "success_remove_department": "Department deleted!", "success_update_department": "Changes saved!"}, "skills": {"success_sent_title_parent": "Skill category added.", "success_remove_skill": "Skill deleted!", "success_update_skill": "Changes saved!", "skillGroupAdded": "Skill group added", "skillGroupUpdated": "Skill group updated", "skillAdded": "<PERSON>ll added", "skillUpdated": "Skill updated", "groupDeleted": "Skill group deleted!", "skillMerged": "Skills merged", "skillMoved": "<PERSON><PERSON> moved to {{parentName}} group", "skillsMoved": "Skills moved to {{parentName}} group", "skillsDeleted": "Skills deleted", "skillsAISuggest": "Equivalent skills added"}, "emailTemplates": {"success_sent_title": "Email template added.", "success_remove_email_template": "Email template deleted.", "success_update_email_template": "Email template updated."}, "locations": {"success_sent_title": "Location added!", "success_remove_location": "{{name}} has been removed from this workplace!", "success_update_location": "{{name}} has been updated", "success_update": "Changes saved!", "success_delete": "Location deleted."}, "account": {"successUpdate": "Changes saved!"}, "workspace": {"message_update_success": "Changes saved!"}, "careers": {"message_update_success": "Changed saved!", "message_update_success_career": "Career page setting updated.", "message_update_success_tc": "Terms and conditions updated."}, "disqualify_reasons": {"success_sent_title": "Disqualification reason added.", "success_update": "Disqualification reason updated.", "success_delete": "Disqualification reason deleted."}, "tags": {"success_sent_title": "Tag added.", "success_remove": "Tag deleted.", "success_update": "Tag updated."}, "custom_fields": {"custom_field_add_success": "Additional field added", "custom_field_update_success": "Additional field updated", "custom_field_delete_success": "Additional field deleted"}, "profileFields": {"profileFieldsUpdated": "Setting visibility updated.", "saveChanges": "Save changes!"}, "requisition": {"approvalFlowUpdated": "Approval flow updated.", "approvalFlowAdded": "Approval flow added.", "approvalFlowDeleted": "Approval flow deleted."}, "teams": {"added": "Team added!", "deleted": "Team deleted!", "updated": "Team updated!"}, "company_settings": {"message_update_activated": "Company feature activated.", "message_update_deactivated": "Company feature deactivated."}}, "saveChanges": "Save changes!", "jobs": {"detail": {"changesSaved": "Changes saved!"}, "jobCard": {"changesSaved": "Changes saved!", "deleteJob": "Job deleted", "deleteJobs": "Jobs deleted", "jobSaved": "Job saved", "unJobSaved": "Saved job removed"}, "candidateAssignedToJob": "Candidate assigned to job!", "add_jd_success": "Job description added successfully", "replace_jd_success": "Job description replaced successfully", "application_form_updated": "Application form updated."}, "candidates": {"tags": {"success_update": "Profile updated.", "field_max_number": "A tag has maximum {{number}} characters.", "invalid_value": "Invalid value", "limit_field_number": "Number of tags reach limit"}, "candidateRequalified": "Candidate requalified!", "add_summary_success": "Summary added successfully", "replace_summary_success": "Summary replaced successfully", "add_summary_fail": "Failed to generate, please retry"}, "talent_pool": {"add_success": "Talent pool added.", "update_success": "Talent pool updated."}, "mark_as_hired": {"success": "{{name}} has been marked as hired.", "remove": "{{name}}'s {{hired}} status has been removed.", "editSuccess": "Hire information updated.", "candidate": "Candidate(s)"}, "hiring_manager_updated": "Hiring manager updated.", "hiring_pipeline_updated": "Hiring pipeline updated!", "recruiter_updated": "Recruiter updated.", "success_remove_member_from_job": "{{name}} has been removed from this job.", "success_add_member_to_job": "{{name}} has been added to this job.", "request_to_post_this_job_sent": "Request to post this job on {{name}} is sent", "request_to_unpublish_this_job_sent": "Request to unpublish this job on {{name}} is sent", "note_added": "Note added", "note_updated": "Note updated", "note_deleted": "Note deleted", "attachment_deleted": "Attachment deleted.", "please_select_at_least_1_assignee": "Please select at least 1 assignee.", "interview_kit_updated": "Interview kit updated.", "interview_kit_added": "Interview kit added.", "interview_kit_deleted": "Interview kit deleted.", "delete_interview_kit": "Delete interview kit", "referral_rewards_updated": "Referral rewards updated.", "referral_rewards_added": "Referral rewards added.", "savedDraft": "Saved draft!", "positionCreated": "Position created", "positionUpdated": "Position updated", "positionDeleted": "Position deleted", "careerPathCreated": "Career path created", "careerPathUpdated": "Career path updated", "careerPathCreateDraft": "Career path saved as draft", "careerPathDeleted": "Career path deleted", "jobPublished": "Job published!", "changesSaved": "Changes saved!", "yourRequestSuccessfullySent": "Your request successfully sent", "agency": {"company_status": {"add_success": "Company status added.", "update_success": "Company status updated.", "deleted_success": "Company status deleted.", "change_saved": "Changes saved."}}, "attachmentFailed": "Attachment failed", "attachment_not_save": "Cannot save attachment", "attachmentDeleted": "Attachment deleted.", "attachmentFailedDescription": "Maximum {{number}} files can be uploaded.", "pleaseSelectAtLeastAssignee": "Please select at least {{number}} assignee.", "attachmentFailedDescription01": "Total size of attachment must be less than {{number}}.", "interview": {"scheduleInterviewFailed": "Schedule Interview failed", "interviewDeleted": "Interview deleted.", "interviewUpdated": "Interview updated", "interviewCreated": "Interview created", "aNotificationEmailHasBeenSent": "A notification email has been sent to the candidate", "emailSent": "<PERSON><PERSON> sent."}, "note": {"noteCreated": "Note created.", "noteUpdated": "Note updated.", "noteDeleted": "Note deleted.", "savingNote": "Saving note", "cvDeleted": "CV deleted."}, "profile": {"deleteProfile": "Candidate deleted", "deleteProfiles": "Candidates deleted"}, "referral": {"createdReferral": "Created referral!"}, "requisition": {"requisitionSavedAsDraft": "Requisition saved as draft.", "requisitionSubmitted": "Requisition submitted."}, "gAnalytic": {"successfullyDisconnected": "Successfully disconnected.", "successfullyIntegrated": "Successfully integrated."}, "pipeline": {"pipelineTemplateAdded": "Pipeline template added.", "pipelineTemplateDeleted": "Pipeline template deleted.", "pipelineTemplateUpdated": "Hiring pipeline template updated."}, "task": {"taskCreated": "Task created.", "taskDeleted": "Task deleted.", "taskDone": "Task done.", "pleaseWaitMoment": "Please Wait a moment", "taskMovedToDoList": "Task moved to to-do list"}, "upload": {"uploadCanceled": "Upload canceled.", "uploadSuccessful": "Upload successful.", "uploadFailed": "Upload failed", "profileDeleted": "Profile deleted.", "uploadFailedDescription": "Maximum {{number}} files can be uploaded.", "uploadMaximumFailedDescription": "Failed to upload. Maximum {{number}} files can be uploaded at once."}, "missingInformationToPublishDomain": "Missing information to publish this job on {{domain}}.", "candidateDisqualified": "Candidate disqualified", "descriptionCandidateDisqualified": "A notification email has been sent to the candidate.", "domainExisting": "This domain is already existing.", "roleUpdated": "Role updated.", "errorPermission": "You do not have permission to access the requested page. Please contact your system administrator for further assistance.", "clientContact": {"successSentTitle": "<PERSON><PERSON><PERSON> sent!", "successSentContentNewInvitation": "An email has been sent to notify them to register for {{domain}}.", "successSentContentInvitation": "Successfully invited to job", "contactHasBeenRemoved": "{{<PERSON><PERSON><PERSON>}} has been removed from this job.", "invitationForEmailHasBeenResent": "Invitation for {{email}} has been resent."}, "placementUpdated": "Placement updated.", "feedbackSubmitted": "<PERSON><PERSON><PERSON> submitted", "feedbackUpdated": "Feedback updated", "profile_template_updated": "Profile template updated.", "profile_template_added": "Profile template created.", "profile_template_deleted": "Profile template deleted.", "delete_profile_template": "Delete profile template", "experience_deleted": "Experience deleted", "education_deleted": "Education deleted", "reference_deleted": "Reference deleted", "certificate_deleted": "Certificate deleted", "language_deleted": "Language deleted", "link_deleted": "Link deleted", "duplicated_title": "Duplicated fields", "import_mapping_require_title": "Missing required fields", "attachmentSaved": "<PERSON><PERSON><PERSON><PERSON> saved", "network_error": "Network Error", "network_upload_error": "Failed to upload. Please try again.", "ssoSuccessfullyDisabled": "SSO successfully disabled.", "ssoSuccessfullyEnabledTitle": "SSO enabled successfully.", "ssoSuccessfullyEnabledDescription": "All active members must reauthenticate to continue access.", "ssoAuthenticationFailedTitle": "Authentication failed.", "ssoAuthenticationFailedDescription": "Please try again or contact your administrator.", "sessionManagementUpdated": "Session management updated", "attachmentSuccessfully": "Resume was successfully uploaded to Files", "transferOwnerSuccessfully": "Ownership has been successfully reassigned.", "moveStageAndEmailSent": "{{candidate<PERSON><PERSON>}} moved to Client submission stage. Email sent.", "moveStageAndPlacementDeleted": "{{candidateName}} moved to {{stageName}} stage. Placement deleted.", "assign_job_success": "Candidate(s) assigned to job", "assign_talent_pool_success": "Candidate(s) added to talent pool", "notificationDeleted": "Notification deleted", "allNotificationsDeleted": "All notifications deleted", "task_done": "Task done", "task_move_to_todo_list": "Task moved to to-do list", "profileViewDisplayCreated": "New view created", "profileViewDisplayDeleted": "View deleted", "processing": "Processing", "updatedEmails": "Update {{progress}} emails", "emailExist": "The following emails already exist in this company: {{keys}}", "candidateMoveToStage": "Candidate(s) moved to {{stageName}}", "callLogs": {"create": {"toastSuccess": "Created a log", "toastFailed": "Call log updated"}, "update": {"toastSuccess": "Call log updated", "toastFailed": "Call log updated"}, "delete": {"toastSuccess": "Call log deleted!", "toastFailed": "Call log delete failed!"}}, "updatedApplicants": "Update {{progress}} applicants", "IPWhiteListUpdated": "IP whitelisting updated", "courses": {"deleteCourse": "Courses deleted"}, "deleteCompany": "Company deleted", "deleteCompanies": "Companies deleted"}