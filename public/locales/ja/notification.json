{"settings": {"teamMembers": {"success_sent_title": "招待状が送信されました!", "success_sent_content": "{{email}} への招待状が再送信されました。", "success_sent_content_new_invitation": "{{domain}} に登録するよう通知するメールが送信されました。", "success_remove_member": "{{fullName}} はこのワークプレイスから削除されました。", "success_update_role_member": "メンバーアカウントが更新されました。", "success_sent_number_of_emails": "{{num}} 件のメールが送信されました！", "membersDeleted": "メンバーが削除されました。"}, "departments": {"success_sent_title": "部門が追加されました！", "success_remove_department": "部門が削除されました!", "success_update_department": "変更が保存されました！"}, "skills": {"success_sent_title_parent": "スキルカテゴリーが追加されました。", "success_remove_skill": "スキルが削除されました!", "success_update_skill": "変更を保存しました!", "skillGroupAdded": "スキルグループが追加されました", "skillGroupUpdated": "スキルグループが更新されました。", "skillAdded": "スキルを追加しました", "skillUpdated": "スキルが更新されました", "groupDeleted": "グループが削除されました", "skillMerged": "スキルが統合されました。", "skillsMoved": "スキルが {{parentName}} グループに移動されました。", "skillMoved": "スキルが {{parentName}} グループに移動されました。", "skillsDeleted": "スキルが削除されました", "skillsAISuggest": "同等のスキルを追加"}, "emailTemplates": {"success_sent_title": "メールテンプレートが追加されました。", "success_remove_email_template": "メールテンプレートが削除されました。", "success_update_email_template": "メールテンプレートが更新されました。"}, "locations": {"success_sent_title": "場所が追加されました！", "success_remove_location": "{{name}} はこのワークプレイスから削除されました。", "success_update_location": "{{name}}が更新されました", "success_update": "変更が保存されました！", "success_delete": "ロケーションが削除されました。"}, "account": {"successUpdate": "変更が保存されました！"}, "workspace": {"message_update_success": "変更が保存されました！"}, "careers": {"message_update_success": "変更が保存されました!", "message_update_success_career": "キャリア ページの設定が更新されました。", "message_update_success_tc": "利用規約が更新されました。"}, "disqualify_reasons": {"success_sent_title": "不採用事由を追加しました。", "success_update": "不採用理由を更新しました。", "success_delete": "不採用理由を削除しました。"}, "tags": {"success_sent_title": "タグが追加されました。", "success_remove": "タグが削除されました。", "success_update": "タグが更新されました。"}, "custom_fields": {"custom_field_add_success": "追加フィールドが追加されました。", "custom_field_update_success": "追加フィールドが更新されました。", "custom_field_delete_success": "追加フィールドが削除されました。"}, "profileFields": {"profileFieldsUpdated": "閲覧設定が更新されました。", "saveChanges": "変更内容が保存されました！"}, "requisition": {"approvalFlowUpdated": "承認フローが更新されました。", "approvalFlowAdded": "承認フローを追加しました。", "approvalFlowDeleted": "承認フローが削除されました。"}, "teams": {"added": "チームが追加されました。", "deleted": "チームが削除されました。", "updated": "チームが更新されました。"}, "company_settings": {"message_update_activated": "会社機能が有効化されました。", "message_update_deactivated": "会社機能が無効化されました。"}}, "saveChanges": "変更内容が保存されました！", "jobs": {"detail": {"changesSaved": "変更が保存されました！"}, "jobCard": {"changesSaved": "変更が保存されました！", "deleteJob": "求人が削除されました。", "jobSaved": "ジョブを保存しました", "unJobSaved": "保存した求人を削除しました"}, "candidateAssignedToJob": "候補者が求人にアサインされました。", "add_jd_success": "職務内容が正常に追加されました", "replace_jd_success": "職務記述書が正常に置き換えられました", "application_form_updated": "応募フォームが更新されました。"}, "candidates": {"tags": {"success_update": "プロフィールが更新されました。", "field_max_number": "タグの最大文字数は {{number}} 文字です。", "invalid_value": "無効な値です。", "limit_field_number": "タグ数が制限に達しました。"}, "candidateRequalified": "候補者が再度適格となりました！", "add_summary_success": "概要が正常に追加されました", "replace_summary_success": "サマリーが正常に置き換えられました", "add_summary_fail": "生成に失敗しました。再試行してください"}, "talent_pool": {"add_success": "タレントプールが追加されました。", "update_success": "タレントプールが更新されました。"}, "mark_as_hired": {"success": "{{name}} は雇用済みとしてマークされています。", "remove": "{{name}} の {{hired}} ステータスが削除されました。", "editSuccess": "採用情報が更新されました。", "candidate": "候補者"}, "hiring_manager_updated": "採用担当者を更新しました。", "hiring_pipeline_updated": "採用パイプラインが更新されました!", "recruiter_updated": "採用担当者を更新しました。", "success_remove_member_from_job": "{{name}} はこのジョブから削除されました。", "success_add_member_to_job": "{{name}} がこの求人に追加されました。", "request_to_post_this_job_sent": "この求人を {{name}} に投稿するリクエストが送信されます", "request_to_unpublish_this_job_sent": "{{name}} でこの求人を非公開にするリクエストが送信されました", "note_added": "ノートが追加されました。", "note_updated": "ノートが更新されました。", "note_deleted": "ノートが削除されました。", "attachment_deleted": "添付ファイルが削除されました。", "please_select_at_least_1_assignee": "少なくとも 1 人の譲受人を選択してください。", "interview_kit_updated": "面接キットを更新しました。", "interview_kit_added": "面接キットを追加しました。", "interview_kit_deleted": "面接キットが削除されました。", "delete_interview_kit": "面接キットを削除する。", "referral_rewards_updated": "リファーラル報酬が更新されました。", "referral_rewards_added": "リファーラル報酬が追加されました。", "savedDraft": "下書きを保存しました。", "positionCreated": "ポジションが作成されました", "positionUpdated": "ポジションが更新されました", "positionDeleted": "ポジションが削除されました", "careerPathCreated": "キャリアパスを作成しました", "careerPathUpdated": "キャリアパスを更新しました", "careerPathCreateDraft": "キャリアパスを下書きとして保存しました", "careerPathDeleted": "キャリアパスを削除しました", "jobPublished": "求人が公開されました！", "changesSaved": "変更が保存されました！", "yourRequestSuccessfullySent": "リクエストは正常に送信されました。", "agency": {"company_status": {"add_success": "会社ステータスを追加しました。", "update_success": "会社ステータスを更新しました。", "deleted_success": "会社ステータスが削除されました。", "change_saved": "変更が保存されました！"}}, "attachmentFailed": "添付に失敗しました。", "attachment_not_save": "添付ファイルを保存できません。", "attachmentDeleted": "添付ファイルが削除されました。", "attachmentFailedDescription": "最大 {{number}} 個のファイルをアップロードできます。", "pleaseSelectAtLeastAssignee": "少なくとも {{number}} 人の譲受人を選択してください。", "attachmentFailedDescription01": "添付ファイルの合計サイズは {{number}} 未満である必要があります。", "interview": {"scheduleInterviewFailed": "面接の設定が失敗しました。", "interviewDeleted": "面接は削除されました。", "interviewUpdated": "面接は更新されました。", "interviewCreated": "面接が作成されました。", "aNotificationEmailHasBeenSent": "候補者に通知メールが送信されました", "emailSent": "メールが送信されました。"}, "note": {"noteCreated": "ノートが作成されました。", "noteUpdated": "ノートが更新されました。", "noteDeleted": "ノートは削除されました。", "savingNote": "メモの保存", "cvDeleted": "履歴書が削除されました。"}, "profile": {"deleteProfile": "候補者が削除されました", "deleteProfiles": "候補者が削除されました"}, "referral": {"createdReferral": "リファーラルを作成しました。"}, "requisition": {"requisitionSavedAsDraft": "要請はドラフトとして保存されました。", "requisitionSubmitted": "要請が提出されました。"}, "gAnalytic": {"successfullyDisconnected": "正常に切断されました。", "successfullyIntegrated": "統合に成功しました。"}, "pipeline": {"pipelineTemplateAdded": "パイプラインテンプレートが追加されました。", "pipelineTemplateDeleted": "パイプライン テンプレートが削除されました。", "pipelineTemplateUpdated": "採用パイプライン テンプレートが更新されました。"}, "task": {"taskCreated": "タスクが作成されました。", "taskDeleted": "タスクが削除されました。", "taskDone": "タスクが完了しました。", "pleaseWaitMoment": "しばらくお待ちください。", "taskMovedToDoList": "タスクが To Do リストに移動されました"}, "upload": {"uploadCanceled": "アップロードがキャンセルされました。", "uploadSuccessful": "アップロードに成功しました。", "uploadFailed": "アップロードに失敗しました", "profileDeleted": "プロフィールが削除されました。", "uploadFailedDescription": "最大 {{number}} 個のファイルをアップロードできます。", "uploadMaximumFailedDescription": "アップロードに失敗しました。一度にアップロードできるファイルは最大 {{number}} 個です。"}, "missingInformationToPublishDomain": "{{domain}} でこの求人を公開するための情報が不足しています。", "candidateDisqualified": "候補者は不採用となりました。", "descriptionCandidateDisqualified": "候補者に通知メールが送信されました。", "domainExisting": "このドメインはすでに存在しています。", "roleUpdated": "役割が更新されました。", "errorPermission": "要求されたページにアクセスする権限がありません。サポートが必要な場合は、システム管理者にお問い合わせください。", "clientContact": {"successSentTitle": "招待状が送信されました!", "successSentContentNewInvitation": "{{domain}} に登録するよう通知するメールが送信されました。", "successSentContentInvitation": "ジョブに招待されました。", "contactHasBeenRemoved": "{{contactName}} はこの求人から削除されました。", "invitationForEmailHasBeenResent": "{{email}} へ招待状が再送信されました。"}, "placementUpdated": "プレイスメント情報が更新されました。", "feedbackSubmitted": "フィードバックが提出されました。", "feedbackUpdated": "フィードバックが更新されました。", "profile_template_updated": "プロフィール テンプレートが更新されました。", "profile_template_added": "プロフィールテンプレートが作成されました。", "profile_template_deleted": "プロフィール テンプレートが削除されました。", "delete_profile_template": "プロフィール テンプレートが削除されました", "experience_deleted": "職務経歴が削除されました。", "education_deleted": "学歴が削除されました。", "reference_deleted": "推薦状が削除されました。", "certificate_deleted": "証明書が削除されました。", "language_deleted": "言語が削除されました。", "link_deleted": "リンクが削除されました。", "duplicated_title": "重複したフィールド", "import_mapping_require_title": "必須フィールドが不足しています", "attachmentSaved": "添付ファイルが保存されました", "network_error": "ネットワークエラー", "network_upload_error": "アップロードに失敗しました。もう一度お試しください。", "ssoSuccessfullyDisabled": "SSOは使用不可と設定致しました。", "ssoSuccessfullyEnabledTitle": "SSO が正常に有効化されました。", "ssoSuccessfullyEnabledDescription": "アクセスを続行するには、すべてのアクティブ メンバーが再認証する必要があります。", "ssoAuthenticationFailedTitle": "認証に失敗しました。", "ssoAuthenticationFailedDescription": "もう一度お試しいただくか、管理者にお問い合わせください。", "sessionManagementUpdated": "セッション管理が更新されました", "attachmentSuccessfully": "履歴書がファイルに正常にアップロードされました", "transferOwnerSuccessfully": "所有権が正常に再割り当てされました。", "moveStageAndEmailSent": "{{candidateName}} はクライアント提出段階に移動しました。電子メールが送信されました。", "moveStageAndPlacementDeleted": "{{candidateName}} が {{stageName}} ステージに移動されました。プレースメントが削除されました。", "assign_job_success": "求人に割り当てられた候補者", "assign_talent_pool_success": "候補者が人材プールに追加されました", "notificationDeleted": "通知を削除しました", "allNotificationsDeleted": "すべての通知を削除しました", "task_done": "タスクが完了しました。", "task_move_to_todo_list": "タスクが To Do リストに移動されました。", "processing": "処理", "updatedEmails": "{{progress}} メールを更新", "callLogs": {"create": {"toastSuccess": "通話記録を作成しました", "toastFailed": "通話記録が更新されました"}, "update": {"toastSuccess": "通話記録が更新されました", "toastFailed": "通話記録が更新されました"}, "delete": {"toastSuccess": "通話記録が削除されました!", "toastFailed": "通話履歴の削除に失敗しました。"}}, "profileViewDisplayCreated": "新しいビューが作成されました", "profileViewDisplayDeleted": "ビューが削除されました", "candidateMoveToStage": "候補者が{{stageName}}に移動しました", "updatedApplicants": "{{progress}} 人の応募者を更新", "IPWhiteListUpdated": "IPホワイトリストを更新しました", "courses": {"deleteCourse": "コースを削除しました"}, "deleteCompany": "会社を削除しました", "deleteCompanies": "削除された企業"}