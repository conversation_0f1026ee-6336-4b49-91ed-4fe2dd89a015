{"teamMembers": {"title": "メンバー", "description": "ここではチームメンバーとそのアカウント権限を管理できます。", "tab": {"members": "メンバー", "membersTable": {"name": "氏名", "role": "役割", "department": "部門", "team": "チーム", "removeMemberTooltip": "メンバーの削除"}, "membersEmpty": {"title": "メンバーがいません", "description": "現在、メンバーへの招待がないようです。", "buttonTitle": "メンバー招待", "titleSearch": "結果が見つかりません", "descriptionSearch": "検索に一致する結果はありません。", "buttonTitleSearch": ""}, "pendingMembersEmpty": {"title": "保留中の招待はありません", "description": "保留中の招待はないようです！"}, "pendingInvites": "保留中の招待", "pendingInvitesTable": {"name": "氏名", "role": "役割", "rolePending": "保留中", "removeMemberTooltip": "メンバーの削除", "resendEmailTooltip": "メールを再送信"}, "pendingInvitesEmpty": {"title": "保留中の招待はありません", "description": "現在保留中の招待はないようです。", "buttonTitle": "メンバー招待"}}, "inviteMemberButton": "メンバー招待", "searchInput": "氏名またはメールアドレスで検索", "inviteMemberModal": {"title": "メンバー招待", "description": "チーム メンバーをこの求人に招待して共同作業します。", "actions": {"cancel": "キャンセル", "sendInvites": "招待状を送信する"}}, "editCHUBAccount": {"title": "メンバーアカウントを編集する"}, "editAccountPermission": {"title": "メンバーアカウントを編集する", "save": "保存"}, "additional_permission": {"additional_user_permissions": "追加のユーザー権限", "description_additional_user_permissions": "プラットフォーム内でこのユーザーに許可されるアクションを定義します。", "manage_requisition": "求人要請の管理", "manage_requisition_description": "新しい求人要請を作成したり、承認された求人要請から求人を作成したりできます。", "request_requisition": "求人要請のリクエスト", "request_requisition_description": "新しい求人依頼を作成できますが、求人を作成することはできません。", "invite_2_job_base_department_location": "割り当てられた場所/部門に基づいて求人に招待する"}, "removeMemberAlert": {"title": "メンバーの削除", "content": "<0>{{name}}</0> を削除してもよろしいですか？彼らはこのワークスペースにアクセスできなくなります。", "cannot_remove_admin": "アドミンが削除できません", "cannot_remove_recruiter": "採用担当者を削除できません", "cannot_remove_owner": "オーナーが削除できません", "remove_admin": "アドミンを削除します", "remove_member": "メンバーを削除", "contentWithHaveOpenJobs": "<0>{{name}}</0> を削除してもよろしいですか？このユーザーは {{openJobsCount}} 求人から削除されることになります。このユーザーは、今後この会社にアクセスできなくなります。", "actions": {"cancel": "キャンセル", "remove": "削除"}}, "bulkRemoveMembersAlert": {"title": "メンバーを削除", "content": "<0>{{count}}</0> 人のメンバーを削除してもよろしいですか?<1></1> これらのメンバーはこの職場にアクセスできなくなります。"}, "updateMemberAlert": {"cannot_edit_admin": "アドミンが編集できません", "cannot_edit_member": "メンバーを編集できません", "cannot_edit_owner": "オーナーが編集できません", "edit_member": "メンバーを編集します", "edit_admin": "アドミンを編集します"}, "removeInvitationAlert": {"title": "招待状を削除する", "content": "<0>{{name}}</0> への招待を削除してもよろしいですか?"}, "resendMemberAlert": {"title": "メールを再送"}, "headQuarter": "本社"}, "departments": {"title": "チーム・部門", "description": "チーム・部門をここで管理", "searchInput": "氏名で検索", "addDepartmentButton": "最上位の部門を追加する", "addDepartment": "部門の追加", "tab": {"departmentEmpty": {"title": "部門はありません", "description": "部門を追加していないようです。", "titleSearch": "結果が見つかりません", "descriptionSearch": "検索に一致する結果はありません。"}, "departmentTable": {"name": "氏名", "openJobsCount": "募集中の求人"}}, "addDepartmentModal": {"title": "最上位の部門を追加する", "actions": {"cancel": "キャンセル", "add": "追加"}}, "editDepartmentModal": {"title": "最上位の部門を編集する", "subDepartmentTitle": "サブ部門の編集", "actions": {"save": "保存"}}, "moveDepartmentModal": {"title": "部門を移動する", "actions": {"cancel": "キャンセル", "move": "移動"}}, "addSubDepartmentModal": {"title": "サブ部門の追加", "subtitle": "<0>{{name}}</0>部門の下に追加", "actions": {"cancel": "キャンセル", "add": "追加"}}, "removeDepartmentAlert": {"title": "最上位部門を削除", "titleSubDepartment": "サブ部門を削除", "content": "<0>{{name}}</0> を削除してもよろしいですか? このアパートメントを削除すると、そのサブ部門もすべて削除されます。この操作は元に戻せません。", "contentRelatedJobs": "<0>{{name}}</0> を完全に削除しようとしています。これにより、次のことが行われます:<1><0>{{name}}</0> の下にあるすべてのサブ部門が削除されます</1><1> {{countJobs}} の関連ジョブから <0>{{name}}</0> が削除されます</1><1>現在リンクされているすべてのユーザーの割り当てが解除されます</1>\nこの操作は元に戻せません。続行してもよろしいですか?", "contentSub": "<0>{{name}}</0> を削除してもよろしいですか? この操作は元に戻せません。", "contentSubRelatedJobs": "<0>{{name}}</0> サブ部門を完全に削除しようとしています。<2></2>これにより、<0>{{name}}</0> が {{countJobs}} 件の関連ジョブから削除され、現在リンクされているすべてのユーザーの割り当てが解除されます。<2></2>この操作は元に戻せません。続行してもよろしいですか?", "actions": {"cancel": "キャンセル", "remove": "削除"}}, "updateDepartmentAlert": {"title": "部門を編集"}, "addSubDepartmentAlert": {"title": "サブ部門を追加"}, "moveDepartmentTooltip": {"title": "移動"}, "moveDepartmentUnder": "<span class='mx-1 font-medium text-gray-900'>{{name}}</span> を次の部門の下に移動します。", "moveDepartmentTop": "<span class='mx-1 font-medium text-gray-900'>{{name}}</span> を最上位部門に移動", "noResult": "検索結果はありません", "allDepartments": "すべてのチーム・部門", "assignToDepartments": "部門にアサインする", "descriptionAssignToDepartments": "部門の一致に基づいてユーザーを採用チームに自動追加します。"}, "skills": {"title": "スキル", "description": "最適なマッチングの為のスキル管理", "searchInput": "スキルで検索", "searchGroupInput": "グループ名で検索", "addSKillButton": "グループを追加", "addSKill": "スキルを追加", "statistics": "<0>{{groupTotal}}</0> グループ - <0>{{skillTotal}}</0> スキル", "allGroup": "すべてのグループ", "tab": {"skillEmpty": {"title": "スキルなし", "description": "スキルを追加しないようですね！", "titleSearch": "結果が見つかりませんでした", "descriptionSearch": "検索に一致する結果はありません。"}, "groupEmpty": {"titleSearch": "結果が見つかりませんでした", "descriptionSearch": "検索に一致する結果はありません。"}, "skillTable": {"name": "氏名"}}, "addSkillModal": {"title": "グループを追加"}, "updateSkillModal": {"title": "グループの編集"}, "editSkillModal": {"title": "トップレベルのスキルを編集する", "subSkillTitle": "サブスキルの編集"}, "moveSkillModal": {"title": "スキルの移動"}, "addSubSkillModal": {"title": "スキルを追加", "subtitle": "<0>{{name}}</0> 下位に追加"}, "removeSkillAlert": {"title": "スキルを削除", "content": "<0>{{name}}</0> スキルを完全に削除しようとしています。<1></1> これにより、関連するすべての求人と候補者から <0>{{name}}</0> が削除されます。<1></1> この操作は元に戻せません。続行してもよろしいですか?"}, "bulkRemoveSkillAlert": {"title": "スキルを削除", "content": "{{count}} 件のスキルを完全に削除しようとしています。この操作により、選択したスキルが関連付けられているすべての求人および候補者から削除されます。.<1></1> この操作は元に戻すことができません。本当に実行しますか？?"}, "removeGroupSkill": {"title": "グループを削除", "content": "<0>{{name}}</0> グループを完全に削除しようとしています。この操作により、このグループ内のすべてのスキルが関連するすべての求人および候補者から削除されます。<1></1> この操作は元に戻すことができません。本当に実行しますか？"}, "updateSkillAlert": {"title": "スキルを編集"}, "moveSkillUnder": "<span class='mx-1 font-medium text-gray-900'>{{name}}</span> を次のスキルカテゴリー下に移動", "bulkMoveSkillUnder": "<span class='font-medium text-gray-900'>{{numberCount}}</span> をウィングスキルグループの下に移動します", "addGroup": "グループを追加", "groupName": "グループ名", "editGroup": "グループを編集", "skillName": "スキル名", "subordinateTo": "配下のグループ", "editSkill": "スキルを編集", "addSkill": "スキルを追加", "mergeSkills": "スキルの統合", "mergeSkillDescription": "選択した <span class='font-medium text-gray-900'>{{numberCount}}</span> 件のスキルを、すべての関連する求人およびプロフィールで置き換えるためのプライマリスキルを選択してください", "primarySkill": "プライマリスキル", "skillTable": {"groups": "グループ", "skillName": "スキル名", "skillJobs": "求人", "skillGroup": "グループ", "skillCount": "スキル", "skillProfiles": "プロフィール", "skillUpdated": "最終更新日"}, "group": "グループ", "report": {"noChangeFromPreviousTime": "前回から変更なし", "statistics": {"profiles": "スキルを持つ候補者の総数", "profilesHelpInfo": "このスキルにリンクされている候補者の総数", "jobs": "このスキルを持つ総求人数", "jobsHelpInfo": "このスキルにリンクされた総求人数", "hiredJobs": "成功した求人数 (採用済み)", "hiredJobsHelpInfo": " このスキルで成功裏に埋められた総求人数 (採用済み)", "jobTitle": "役職", "candidates": "候補者", "createdDate": "作成日", "publishDate": "公開日", "closeDate": "締め切り日", "filledDate": "記入日", "hiredCandidates": "採用候補者", "name": "氏名", "job": "求人", "stage": "ステージ", "source": "ソース"}, "empty": {"noDataCollect": "まだデータはありません。", "noResultsFound": "結果は見つかりません。"}, "overtimeReport": {"title": "スキル分析: 候補者と求人の推移", "titleHelpInfo": "このグラフには、選択したスキルの候補者と求人の推移が示され、需要と供給のギャップが強調表示されます。これを使用して、採用動向を分析し、スキル開発戦略を計画します。", "profiles": "<0>{{name}}</0> スキルを持つ候補者", "jobs": "<0>{{name}}</0> スキルを必要とする求人", "profilesGrowth": "<0>{{name}}</0> スキルを持つ累積候補者数", "jobsGrowth": "<0>{{name}}</0> スキルを持つ累積求人数", "profilesTitle": "候補者", "jobsTitle": "求人", "cumulativeGrowth": "累積成長", "cumulativeGrowthInfo": "このグラフは、選択したスキルの累積候補者数と求人数を示し、全体的な成長を可視化します。", "supplyDemandTrends": "需給トレンド", "supplyDemandTrendsInfo": "このグラフは、選択したスキルにおける候補者と求人の推移を示し、需給のギャップを可視化します。"}}, "skillDescription": "説明", "skillDescriptionPlaceholder": "スキルの説明を追加", "skillSimilar": {"title": "同等のスキル", "aiSuggest": "AI による提案", "placeholder": "同等のスキルを入力", "aiSuggestTooltip": "AIが同等のスキルを提案します", "canNotUseAISuggestTooltip": "AI による提案を有効にするには、最大 100 個のスキルを選択します。"}, "tabs": {"skills": "スキル", "settings": "設定"}, "settingTitleAllowAddSkill": "新しいスキルの追加を許可", "settingDescriptionAllowAddSkill": "有効にすると、誰でも仕事やプロフィールを編集しながら新しいスキルを直接作成できます。"}, "positions": {"title": "ポジション", "description": "ポジションを管理してスキルのギャップを特定し、チームメンバーの成長を個別化します。", "searchInput": "名称または国で検索", "addPositions": "新規ポジション", "allStatus": "スターテス", "positionTable": {"positionName": "ポジション名", "positionSkill": "スキル", "positionStatus": "スターテス", "position": "ポジション", "description": "詳細説明"}, "formPlaceholderDescription": "職務の説明を入力してください。主な責任範囲、要件、福利厚生などを含めてください", "titleCreatePositions": "新しいポジションを作成", "titleEditPositions": "ポジションを編集", "descriptionCreatePositions": "役割の要件を定義し、必要なスキルをマッピングしましょう", "generateAIDescription": "ポジション名を入力して説明を生成", "empty": {"title": "ポジションなし", "description": "まだポジションが追加されていません！", "titleSearch": "結果が見つかりません。", "descriptionSearch": "検索に一致する結果はありません。"}, "positionStatus": {"active": "アクティブ", "archived": "アーカイブ", "draft": "ドラフト"}, "removePositionAlert": {"deleteTitle": "ポジションを削除", "deleteDescription": "本当に <0>{{name}}</0> を削除しますか？この操作は元に戻せません。", "canNotDeleteTitle": "ポジションを削除できません", "canNotDeleteDescription": "このポジションはキャリアナビゲーションのプロフィールにリンクされているため、削除できません。"}, "tab": {"position": "ポジション", "careerPath": "キャリアパス"}, "careerPathNew": "新しいキャリアパス", "careerPathAdd": "キャリアパスを追加", "careerPathEdit": "Eキャリアパスを編集", "careerPathTable": {"careerPathName": "キャリアパス", "skillsMatch": "スキル一致率", "lastUpdated": "最終更新日"}, "emptyCareerPath": {"title": "キャリアパスがありません", "description": "Lまだキャリアパスが追加されていないようです。", "titleSearch": "ポジションを削除できません", "descriptionSearch": "このポジションはキャリアナビゲーションのプロフィールにリンクされているため、削除できません。"}, "removeCareerPathAlert": {"deleteTitle": "キャリアパスを削除", "deleteDescription": "本当に <0>{{position}} -> {{nextPosition}}</0> を削除しますか？この操作は取り消せません。"}}, "locations": {"title": "所在地", "description": "すべての会社の所在地をここで管理します。", "searchInput": "名称または国で検索", "addLocationButton": "場所を追加", "tab": {"locationEmpty": {"title": "場所がありません", "description": "場所を追加していないようです。", "titleSearch": "結果が見つかりません", "descriptionSearch": "検索に一致する結果はありません。"}, "locationTable": {"name": "所在地名", "country": "国"}}, "addLocationModal": {"title": "場所を追加", "actions": {"cancel": "キャンセル", "add": "追加"}}, "editLocationModal": {"title": "場所を編集する", "actions": {"save": "保存"}}, "badge": {"headQuarter": "本社"}, "updateLocationAlert": {"title": "場所を編集する"}, "removeLocationAlert": {"title": "場所の削除", "content": "<0>{{name}}</0> を削除してもよろしいですか? この操作は元に戻せません。", "content_related_job": "<0>{{name}}</0> を削除してもよろしいですか? この場所は、現在関連付けられているすべての機能から永久に切り離されます。", "actions": {"cancel": "キャンセル", "delete": "削除"}, "label_delete_location": "{{count}} 件の求人の場所を <0>{{name}}</0> から"}}, "emailTemplates": {"title": "メールテンプレート", "description": "すべてのメールテンプレートをここで管理します。", "searchInput": "氏名で検索", "addEmailTemplateButton": "新しいテンプレート", "table": {"category": "カテゴリー", "name": "氏名", "label": {"default": "デフォルト"}, "tableEmpty": {"title": "メールテンプレートはありません", "description": "パーソナライズされたメールテンプレートを作成して、候補者または採用チームに送信します。", "titleSearch": "結果が見つかりません", "descriptionSearch": "検索に一致する結果はありません。", "buttonAddNew": "新しいテンプレートを作成する"}, "hoverDeleteTitle": "メールテンプレートを削除する", "hoverUpdateTitle": "メールテンプレートを更新する"}, "removeEmailTemplate": {"title": "メールテンプレートを削除する", "description": "<0>{{name}}</0> を削除してもよろしいですか?この操作は元に戻すことができません。", "actions": {"cancel": "キャンセル", "remove": "削除"}}, "updateEmailTemplate": {"title": "メールテンプレートを編集する"}, "addNewEmailTemplate": {"title": "新しいテンプレートを作成する"}, "emailSubject": "メールの件名", "content": "コンテンツ", "placeholderContent": "テンプレートメールの内容を入力してください"}, "account": {"title": "あなたのアカウント", "description": "アカウント設定を管理する", "heading": {"picture": "アカウント画像", "pictureCHUB": "アバター", "general": "一般"}}, "emailSetting": {"title": "メール設定", "description": "メール設定を管理します。", "emailSignature": "メール署名", "customizeEmailSignature": "メール署名をカスタマイズします。{{domain}}から送信されるすべてのメールに適用されます。"}, "workspace": {"title": "一般", "description": "ワークスペースの設定を管理します。", "heading": {"logo": "ロゴ", "general": "一般"}, "uploadImage": {"title": "会社のロゴを調整する"}, "change_url_modal": {"title": "URLの変更", "description": "更新すると、キャリア ページとキャリア ハブの URL が変更されます。続行してもよろしいですか?", "btn_change": "変化"}}, "customDomain": {"tabTitle": "ドメイン", "title": "カスタムドメイン", "description": "カスタムキャリアページドメインを使用して、ブランドの所有権を取得します。", "form": {"domain_required": "ドメインは必須です", "domain_invalid": "ドメイン形式が無効です", "titleInput": "あなたのドメイン", "inputPlaceholder": "ドメインを入力してください。", "btnConnect": "接続する"}, "btnDeleteDomain": "ドメインを削除", "btnRefreshDomain": "ドメインのステータスを更新する", "pendingStatus": {"pending": "保留中", "btnCheckDNSStatus": "DNSステータスを確認する", "timeCheck": "24時間", "description": "DNS を更新した後、変更が反映されるまでに最大 <0>{{timeCheck}}</0> かかる場合があります。", "description1": "DNS 設定が完了したら、このページに戻り、<0>{{btnCheckStatus}}</0> ボタンをクリックしてステータスを更新します。", "table": {"description": "独自のドメインを使用するには、DNS レコードが次の値で設定されていることを確認してください。設定のサポートについては、ドメイン管理者または技術チームにお問い合わせください。", "name": "名前", "type": "タイプ", "value": "価値"}, "btnCopy": "コピー"}, "connectedStatus": {"connected": "接続"}}, "careers": {"tabTitle": "一般的な", "title": "キャリアページ", "description": "会社のキャリアページの設定を管理します。", "descriptionLogo": "会社のロゴを変更できます。", "form": {"pageInformation": "ページ情報", "ga4Integration": "Google アナリティクス 4 の統合", "ga4InputLabel": "Google アナリティクス 4 測定 ID", "ga4Tooltip": "ウェブサイトまたはアプリのデータを追跡するための Google アナリティクス 4 で使用される一意の識別子", "ga4IntegrationDescription": "トラッキング ID を入力して、Google アナリティクス 4 をキャリア ページと統合します。データのインポートには最大 48 時間かかる場合があります。", "pageTitle": "ページタイトル", "companyDescription": "会社概要", "companyDescriptionEngPlaceholder": "あなたの会社を紹介し、なぜその会社が英語で働くのに適した場所であるかを強調します。", "companyDescriptionJaPlaceholder": "あなたの会社を紹介し、なぜそれが日本語で働くのに適した場所であるかを強調してください。", "companyDescriptionHelperTextEng": "このコンテンツは、ページの言語が英語の場合、キャリア ページの上部に表示されます。", "companyDescriptionHelperTextJa": "ページの言語が日本語の場合、このコンテンツはキャリア ページの上部に表示されます。", "updateButton": "アップデート", "departmentVisibility": "部門の可視性", "departmentVisibilityDescription": "候補者がキャリア ページで求人を表示およびフィルタリングするために使用できる部門のレベルを選択します。", "pageLanguages": "ページの言語", "pageLanguagesDescription": "キャリアページに表示する言語を有効にします。", "terms_and_conditions": "利用規約", "terms_and_conditions_description": "この機能を有効にすると、ユーザーが利用規約を読んだことを確認した場合にのみ、申請プロセスが実行されます。", "terms_and_conditions_placeholder": "会社の利用規約情報を入力します。", "default": "デフォルト", "setAsDefault": "デフォルトとして設定"}, "department": {"show_top_level": "最上位の部門のみを表示", "show_all": "サブ部署がある場合は表示"}, "english": "英語", "japanese": "日本語"}, "disqualify_reasons": {"title": "不採用理由", "description": "不採用の理由は、すべてここで管理します。", "add_new_reason": "新規理由", "emptyData": {"title": "理由はありません", "description": "理由を追加していないようです。"}, "tab": {"disqualified_by_us": "当社による不採用", "disqualified_by_candidates": "候補者からのお断り", "rejected_by_us": "当社による不採用", "rejected_by_candidate": "候補者からのお断り", "table": {"reason": "理由"}}, "disqualify_reasons_form": {"add_disqualify_reasons_modal": {"title": "不採用理由を追加"}, "edit_disqualify_reasons_modal": {"title": "不採用理由を編集する"}, "actions": {"cancel": "キャンセル", "add": "追加", "save": "保存"}}, "remove_disqualify_reason_alert": {"title": "不採用理由の削除", "content": "<0>{{name}}</0> を削除してもよろしいですか？この操作は元に戻すことができません。", "actions": {"cancel": "キャンセル", "remove": "削除"}}, "edit_tooltip": "理由を編集", "delete_tooltip": "理由を削除"}, "custom_fields": {"displayConfig": {"search": "検索...", "showIntable": "表に表示", "hideAll": "すべて非表示", "hideInTable": "表示しない", "showAll": "すべて表示"}, "banner": {"contactUs": "お問い合わせください", "toolTipMaximumFields": "最大 {{maximum}} 項目", "maxLimitFieldsDescription": "カスタムフィールドの使用上限 ({{totalFields}}/{{maximum}}) に達しました 。", "limitFieldsDescription": "カスタム 項目の使用制限 ({{totalFields}}/{{maximum}}) に近づいています。"}, "title": "カスタム項目", "description": "システム内のプロフィールや他のレコードに表示されるすべてのフィールドをここで管理します。", "candidate_tab": "候補者", "job_tab": "求人", "contact_tab": "連絡先", "placement_tab": "配置", "company_tab": "会社", "tab": {"system_fields_table": {"name": "氏名", "visibility": "可視性", "profile": "プロフィール", "visible_to": "閲覧者", "field_type": "項目タイプ", "action": "アクション", "client": "クライアント", "careerSite": "キャリアサイト", "careerSiteHelpText": "キャリアサイトで表示されるフィールドを表示", "filter": "フィルター", "filterHelpText": "キャリアサイトでフィルターを有効にする"}}, "button_add_new": "新規項目", "form": {"title_add": "新規項目を追加", "title_edit_candidate": "候補者項目を編集", "title_edit_job": "求人項目を編集", "title_edit_company": "会社を編集", "title_edit_contact": "連絡先を編集", "title_edit_placement": "配置フィールドを編集する", "field_name_label": "項目名", "field_name_placeholder": "例：マイナンバーカード", "field_object_kind": "対象", "visible_to_label": "閲覧者", "field_type_label": "項目タイプ", "filed_type_placeholder": "選択"}, "additional_field": "追加項目", "empty": {"title": "追加項目なし", "description": "追加項目はまだない様です。", "btn_add": "項目を追加"}, "permissions": {"everyoneName": "全員", "everyoneDescription": "管理者、メンバー、限定メンバー", "adminMemberName": "管理者、メンバー", "adminMemberDescription": "", "adminName": "管理者", "adminDescription": ""}}, "referrals": {"title": "キャリアハブ", "description": "リファラルを有効にして、チームメンバーが候補者を求人に推薦できるようにしましょう。", "tab": {"referralMember": {"removeMemberAlert": {"title": "キャリアハブからメンバーを削除する", "subTitle": "<0>{{name}}</0> を削除してもよろしいですか? 他のシステムへのアクセスに影響を与えることなく、キャリア ハブから削除できます。", "notiSuccess": "{{name}} はキャリア ハブから削除されました。"}, "removePendingMemberAlert": {"title": "招待を削除", "subTitle": "<0>{{email}}</0>への招待を削除してもよろしいですか?", "notiSuccess": "{{email}} はキャリアハブから削除されました。"}}, "referral_email": {"title": "新しい求人のメール通知", "description": "紹介プログラムの新着求人情報は、毎週メールで全員に送信されます。"}, "referral_policy": {"title": "リファーラルリワードポリシー", "switchPolicy": "リファーラルリワードポリシーを含めます。", "description": "紹介成功の基準や報酬分配の詳細などの紹介インセンティブポリシーを表示できるようにします。", "notifications_success": "無事に更新されました。", "link": "リンクを使用する", "file": "ドキュメントをアップロードする"}, "referralChubConfig": {"title": "登録設定", "description": "登録できるユーザーを管理し、登録方法をカスタマイズします。", "switchRegisterLabel": "登録を許可する", "everyone": "誰でも登録できます", "restrict_email_domain": "特定のドメインのメールアドレスのみ登録できます", "domainEmailPlaceholder": "例: company.com", "loginMethods": "ログイン方法", "loginMethodsDescription": "キャリアハブユーザーのログインまたは登録方法を選択してください。"}, "referralChubDescription": {"title": "キャリアハブの説明", "description": "キャリア ハブのログイン時に表示される会社の説明。", "descriptionPlaceholder": "例: 私たちは、情熱と献身をもってテクノロジーの未来を形作るイノベーターのチームです。"}, "settings": "設定", "members": "メンバー", "pendingMembers": "保留中のメンバー", "claims": "クレーム", "claimsHelperInfo": "紹介者が採用されると、報酬処理の請求がここに表示されます。", "candidate": "候補者", "emptyClaims": {"title": "まだ請求はありません", "description": "紹介された候補者が採用されると、処理と報酬の請求が表示されます。"}, "noSearchResultClaims": {"title": "結果が見つかりません", "description": "検索に一致する結果はありません。"}, "searchByNameMailJobTitle": "名前、メールアドレス、役職で検索"}, "descriptionPolicy": "リファーラルリワードポリシーへのリンクを入力します", "title_custom_portal": "キャリアハブをカスタマイズする", "agency_title_custom_portal": "紹介設定", "agency_description_custom_portal": "誰でも候補者を募集中の求人に紹介したり、求人情報をソーシャルメディアで共有したりできます。", "description_custom_portal": "チームメンバーが紹介ポータルを使用する際に、正しい設定を選択してください。", "description_enable_when_creating_new_job": "新しい求人に対してキャリアハブ機能を自動的に有効化します。", "title_job_protection": "部門による求人保護", "description_job_protection": "この求人は、該当する部署のメンバーのみがリファラルポータルで閲覧できます。", "department": "部門", "referral_only_text": "リファーラルのみ", "referral_only_description": "誰でも候補者を募集中の求人に紹介したり、求人情報をソーシャルメディアで共有したりできます。", "referral_job_text": "リファーラルと社内応募", "referral_job_description": "誰でも候補者を募集中の求人に紹介、求人情報をソーシャルメディアで共有、または応募ができます。", "job_only_text": "社内アプリケーションのみ", "job_only_description": "誰でも募集中の求人に応募できます。", "referral_text": "キャリアハブで有効にする", "job_referral_only_description": "<0>リファーラルのみ</0> - 誰でも候補者を募集中の求人に紹介したり、求人情報をソーシャルメディアで共有したりできます。", "job_referral_job_description": "<0>リファーラルと社内応募</0> - 誰でも候補者を募集中の求人に紹介、求人情報をソーシャルメディアで共有、または応募ができます。", "job_job_only_description": "<0>社内アプリケーションのみ</0> - 誰でも募集中の求人に応募できます。"}, "tags": {"title": "タグ", "description": "候補者のプロフィールと求人に表示されるすべてのタグをここで管理します。", "searchInput": "氏名で検索", "addTagButton": "新規タグ", "add_tag": "タグを追加", "tab": {"tagEmpty": {"title": "タグなし", "description": "タグを追加していないようです。", "titleSearch": "結果が見つかりません", "descriptionSearch": "検索に一致する結果はありません。"}, "tagTable": {"name": "氏名", "candidates": "候補者", "jobs": "求人"}, "job": "仕事", "candidate": "候補者"}, "addTagModal": {"title": "タグ付けする", "actions": {"cancel": "キャンセル", "add": "追加"}}, "editTagModal": {"title": "タグを編集する", "actions": {"save": "保存"}}, "updateTagAlert": {"title": "タグを編集する"}, "removeTagAlert": {"title": "タグの削除", "content": "<0>{{name}}</0> を削除してもよろしいですか?この操作は元に戻すことができません。", "content_profiles_count": "<0>{{name}}</0> を削除してもよろしいですか?このタグは {{countProfile}} プロフィールから削除されることに注意してください。この操作は元に戻すことができません。", "content_profile_count": "<0>{{name}}</0> を削除してもよろしいですか? このタグは {{countProfile}} のプロフィールから削除されることに注意してください。この操作は元に戻せません。", "content_jobs_count": "<0>{{name}}</0> を削除してもよろしいですか? このタグは {{countProfile}} のジョブから削除されることに注意してください。この操作は元に戻せません。", "content_job_count": "<0>{{name}}</0> を削除してもよろしいですか? このタグは {{countProfile}} のジョブから削除されることに注意してください。この操作は元に戻せません。", "actions": {"cancel": "キャンセル", "delete": "削除"}}}, "interviewKits": {"title": "面接キット", "description": "すべてのインタビュー キットをここで管理します。", "table": {"tableEmpty": {"titleSearch": "結果が見つかりません", "descriptionSearch": "検索に一致する結果はありません。", "buttonTitleSearch": "", "titleNotFound": "面接キットなし", "descriptionNotFound": "面接キットは追加していないようです。", "buttonNotFound": "面接キットを追加"}}, "form": {"descriptionName": "例：販売員面接キット"}, "searchByName": "氏名で検索", "modal": {"createInterviewKit": "面接キットを作成する", "descriptionCreateInterviewKit": "面接キットは候補者を評価するための重要なツールです。", "editInterviewKit": "面接キットを編集する", "descriptionEditInterviewKit": "面接キットは候補者を評価するための重要なツールです。", "previewInterviewKit": "面接キットをプレビュー"}, "inputSectionName": "セクション名を入力", "rateCandidateSkill": "例：候補者のコミュニケーションスキルを評価します。", "describeYourExp": "例：顧客からの苦情に対処した経験について説明してください。"}, "profileTemplates": {"title": "プロフィールテンプレート", "description": "ここですべてのプロフィール テンプレートを管理します。", "table": {"tableEmpty": {"titleSearch": "結果が見つかりません", "descriptionSearch": "検索に一致する結果はありません。", "buttonTitleSearch": "", "titleNotFound": "プロフィールテンプレートなし", "descriptionNotFound": "プロフィールテンプレートを追加していないようです。", "buttonNotFound": "テンプレートを追加"}, "default": "デフォルト"}, "form": {"defaultTemplateName": "デフォルトのテンプレートとして設定", "titleTemplateName": "テンプレート名", "descriptionTemplateName": "例: ハーバード大学の履歴書テンプレート", "descriptionGuideline": "例: このテンプレートでは、あなたのパフォーマンスを効果的にアピールするための主要な能力や強調表示されたスキルなど、社内採用の機会に対する推奨アプローチの概要を説明します。", "titleTemplateHeader": "テンプレートヘッダー", "fullName": "氏名", "id": "ID:", "titleDate": "日付", "titleUserID": "ID", "titleAvatar": "アバター", "titleCompanyLogo": "会社のロゴ", "titleEmail": "Email", "titlePhoneNumber": "電話番号", "watermark": "会社名をウォーターマークとして追加", "titleInformationDisplayedInTemplate": "このテンプレートに表示される情報", "descriptionInformationDisplayedInTemplate": "以下のセクションをクリックしてテンプレートに追加します。"}, "searchByName": "氏名で検索", "modal": {"createProfileTemplate": "プロフィールテンプレートを作成する", "editProfileTemplate": "プロフィールテンプレートを編集", "profileTemplateStep01": "プロフィールテンプレートを作成するには、まずテンプレートスタイルを選択してください。", "profileTemplateStep02": "プロフィールテンプレートに表示するセクションを選択", "stepIndex": "ステップ {{step}}/{{total}}", "backToStep1": "テンプレートスタイルに戻る"}, "inputSectionName": "セクション名を入力", "completion": "完了", "customSection": "カスタムセクション", "workExperience": {"title": "職務タイトル", "company": "会社名", "datetime": "開始日 - 終了日", "location": "勤務地", "description": "詳細説明"}, "education": {"major": "専攻", "degree": "学位", "school": "学校名", "datetime": "開始日 - 終了日", "description": "詳細説明"}, "birthday": {"age": "年齢を表示", "date_and_age": "日付と年齢を表示"}}, "security": {"title": "セキュリティ", "description": "大切な情報を守る為に、セキュリティ設定を行なって下さい。", "enabledSSO": {"title": "SSOを有効化する", "activated": "有効化", "description": "安全の為にSSOを有効化して下さい。"}, "session": {"title": "セッション管理", "description": "ワークスペースのセッションとタイムアウト期間を設定します。ワークスペースのメンバーは、セッションの有効期限が切れると再度ログインするよう求められます。", "neverExpires": "無期限"}, "modal": {"disabledTitle": "シングル サインオン (SSO) を無効にする", "disabledDescription": "SSOを無効にすると、すべてのメンバーがメールとパスワードを使用した標準のログイン方法に戻ります。SSOを無効にしてもよろしいですか？", "enabledTitle": "メンバーサインインへの変更", "enabledDescription": "SSOを有効にすると、全てのメンバーのサインイン方法が変更されます。続行してもよろしいですか？", "updateIPWhiteListingTitle": "IPホワイトリストを更新", "updateIPWhiteListingDescription": "更新後は、登録されたIPアドレスからのみシステムにアクセスできます。", "removeIPWhiteListTitle": "IPアドレスを削除", "removeIPWhiteListDescription": "<0>{{ipAddress}}</0> をホワイトリストから削除しますか？このIPからはシステムにアクセスできなくなります。この操作は元に戻せません。"}, "form": {"modalTitle": "Microsoft Entra による認証", "modalDescription": "シングル サインオン (SSO) を有効にするには、Entra の認証情報を入力してください。", "applicationIdLabel": "アプリケーションID", "applicationIdDescription": "応募者IDを入力してください", "clientIdLabel": "クライアントID", "clientIdDescription": "クライアントIDを入力してください", "tenantIdLabel": "テナントID", "tenantIdDescription": "テナントIDを入力", "clientSecretLabel": "クライアント秘密鍵", "clientSecretDescription": "クライアント秘密鍵を入力してください", "redirectURL": "承認済リダイレクトURL"}, "idleTimerOptions": {"0": "期限切れなし", "5": "5分", "15": "15分", "30": "30分", "45": "45分", "60": "1時間", "120": "2時間", "180": "3時間", "240": "4時間"}, "ipManagement": {"title": "IPホワイトリスト", "description": "ホワイトリストに登録されたIPアドレスのみがシステムにアクセスできます。信頼できるIPを追加してアクセスを制限してください。", "addIPWhiteList": "IPを追加"}}, "permissions": {"moduleName": {"career_page": "キャリアページ設定", "tenant_setting": "ワークスペース設定", "user_management": "ユーザー管理", "template_setting": "テンプレート設定", "report": "レポート閲覧", "company_management": "企業管理", "manage_requisition": "求人要請管理", "request_requisition": "求人要請リクエスト", "job_management": "求人管理", "placement": "収益/プレイスメント", "access_career_hub": "キャリアハブにアクセス", "manage_career_hub": "キャリアハブを管理", "export_candidates": "候補者をエクスポート", "course_management": "コース管理", "export_jobs": "輸出ジョブ", "invite_user_clients": "クライアントユーザーを招待"}, "moduleNameDescription": {"career_page": "会社のキャリアページのカスタマイズ・アップデートを許可します。", "tenant_setting": "ワークスペースの設定・アップデートを許可します。", "user_management": "会社内のアクセス管理やアップデートを許可します。", "template_setting": "テンプレートのカスタマイズを許可します。", "report": "詳細レポート・分析の閲覧を許可します。", "company_management": "企業情報を管理します。", "manage_requisition": "新しい求人を作成したり、承認された求人から求人を作成したりできます。", "request_requisition": "求人要請を行うことを許可します（求人自体の作成はできません）。", "job_management": "採用プロセスの求人、候補者、人材プールを管理します。", "placement": "プレイスメントや利益配分を管理します。", "access_career_hub": "キャリアハブで求人を閲覧し、応募を提出し、候補者を推薦してください。", "manage_career_hub": "キャリアハブの設定へのアクセスを許可します。これには、メンバー、求人、推薦設定の管理が含まれます。", "export_candidates": "候補者リストのデータをエクスポートします。", "course_management": "Career Hubでコースを管理", "export_jobs": "求人リストからデータをエクスポートします。", "invite_user_clients": "ユーザーがクライアントユーザーをプラットフォームにアクセスするよう招待することを許可します。"}, "moduleNameDropdown": {"job_management_full": "全ての求人", "job_management_team": "チームの求人", "job_management_owned": "所有する求人", "company_management_full": "全ての企業", "company_management_team": "チームの会社", "company_management_owned": "所有する会社", "placement_full": "すべてのプレイスメント", "placement_owned": "彼らが所有するプレイスメント", "report_full": "すべてのデータ", "report_team": "チームデータ", "report_owned": "所有しているデータ"}}, "plan": {"plan": "プラン", "planDescription": "サブスクリプションと使用状況を管理します。", "trial": "トライアル", "trialLeft": "残り{{trial}}日", "trialPlan": "トライアルプラン", "youAreCurrentlyOnThe": "現在、", "yourCurrentPlan": "現在のプラン", "yourCurrentPlanHasEnded": "{{tenantPlanName}} プランは終了しました。続行するには、プランのアップグレードをご検討ください。", "numberOfUsers": "ユーザー数", "tooltipNumberOfUsers": "{{domain}}上でのアクティブユーザー数", "numberOfCandidate": "候補者数", "tooltipNumberOfCandidate": "御社内で追加された候補者数", "numberOfJobs": "求人数", "tooltipNumberOfJobs": "御社内で有効な求人数", "numberOfCredits": "クレジット数", "tooltipNumberOfCredits": "連絡先情報を見つけるために使用されるクレジットの数。", "numberOfChubUsers": "キャリアハブのユーザー数", "tooltipNumberOfChubUsers": "あなたのキャリアハブ内のアクティブユーザー数", "remainTrialDaysLeft": "トライアル残り{{remainingTrialDays}} 日", "planExpired": "プランは {{date}} に期限切れとなりました。", "upToNoOfUsers": "最大 {{noOfUsers}} ユーザー", "unlimitedUsers": "無制限のユーザー", "upToNoOfJobs": "最大 {{noOfJobs}} 個のアクティブな求人", "unlimitedJobs": "無制限の求人", "upToNoOfCandidates": "最大 {{noOfCandidates}} 人の候補者", "upToNoOfCredits": "最大{{noOfCredits}}クレジット", "unlimitedCandidates": "無制限の候補者", "customerSuccessManager": "カスタマーサクセスマネージャー", "allFeatures": "すべての機能", "free": "無料", "getAQuote": "見積もりを取得", "thisPlanIncludes": "このプランには以下が含まれます", "currentPlan": "現在のプラン", "contactSales": "コンタクトする", "comparePlans": "プランを比較する", "unlimited": "無制限", "discoverFeatures": "{{domain}}の機能を確認する", "discoverFeaturesForm": {"feature01Title": "ソース＆アトラクション", "feature01Description01": "Chromeソーシング拡張機能", "feature01Description02": "カスタマイズ可能なキャリアページ", "feature01Description03": "複数の求人サイト統合", "feature01Description04": "複数のネットワーク間で求人を共有する", "feature01Description05": "履歴書の一括アップロード", "feature02Title": "自動化と採用", "feature02Description01": "カスタマイズ可能な採用パイプライン", "feature02Description02": "イベントごとに自動化されたメール", "feature02Description03": "履歴書の解析と検索", "feature02Description04": "階層化された部門", "feature02Description05": "マルチロケーション", "feature02Description06": "Google Analytics 4と統合されたキャリアサイト", "feature03Title": "追跡と共同作業", "feature03Description01": "カスタマイズ可能な候補者のプロフィール", "feature03Description02": "候補者のクイック検索", "feature03Description03": "フィルタリングされたプロファイル項目", "feature03Description04": "面接キットと評価フォーム", "feature03Description05": "イベントスケジューラーとカレンダーの同期", "feature03Description06": "候補者による自己スケジュール設定", "feature03Description07": "自動パイプライン追跡", "feature03Description08": "高度なアクセス権設定", "feature03Description09": "チームノート、タスク及びメンション", "feature03Description10": "タレントプール", "feature03Description11": "ユーザーの権限、役割、およびアクセス権管理", "feature04Title": "レポートと分析", "feature04Description01": "概要レポート", "feature04Description02": "求人レポート (案件作成〜紹介〜採用までの時間等)", "feature04Description03": "採用ファネルレポート (ソース/チャネル)", "feature04Description04": "候補者レポート (応募経路やそれ毎の成功率等)", "feature04Description05": "チームの生産性レポート (ソース、フィードバック、インタビュー、採用、ノート等)"}, "questionPlanOptions": "プランオプションについてご質問ございますか？", "descriptionQuestionPlanOptions": "ユーザー、求人、候補者、またはその他の機能についてご質問ございますか？是非メッセージをお待ちしております。すぐにお答えさせて頂きます。", "planContent01": "時間を節約しながら、リーチを拡大、求人情報管理を一元化し、求人効果を高めます。", "planContent02": "時間を節約しながら、リーチを拡大、求人情報管理を一元化し、求人効果を高めます。 <br />アップグレードするには管理者に連絡してください。", "planContent03": "リファーラルを通じて<br /> 潜在的な候補者とシームレスにつながります。リファーラルをまとめて管理・トラックできます。", "planContent04": "求人要請機能で採用プロセスを加速します。<br>シームレスなコミュニケーションと効率的なワークフローでコラボレーションを合理化します。", "planContent05": "今すぐプランをアップグレードして、求人に最適な候補者をリコメンドしてもらいましょう。", "planContent06": "プランをアップグレードして、候補者が理想の求人を見つけられる様にしましょう。", "planContent07": "プランをアップグレードして、求人インポート機能にアクセスしましょう。", "planContent08": "プランをアップグレードして、高機能なセキュリティ設定により会社の情報を守りましょう。", "planContent09": "プランをアップグレードすると、プラットフォーム内のすべてのアクティビティと変更の詳細な記録にアクセスできます。", "reachLimitedPlan": "{{name}} プランの使用制限に達しました。", "reachLimitedPlanUpgrade": "{{name}} プランの使用制限に達しました。アップグレードするには、管理者に連絡してください。", "reachLimitedPlanButton": "お問い合わせ", "reachLimitedPlan01": "アップグレードする為に", "job_board": "求人サイト", "requisition": "要請", "referral": "リファーラル", "ai_resume_parser": "AIによる履歴書解析と読み取り", "custom_field": "カスタム項目", "additional_field": "カスタム項目", "recommendation": "採用担当者への推薦", "cv_template": "プロフィール テンプレート", "employee_profile": "従業員プロフィール", "import_job": "インポート", "audit_log": "監査ログ", "security_setting": "プライバシーとセキュリティ", "skill_management": "スキル管理", "career_page_builder": "キャリアページビルダー", "ai_writer": "AIライター", "find_contact": "連絡先を探す", "resumee_builder": "履歴書ビルダー", "notification": "通知", "import_candidate": "候補者をインポート", "profile_view": "プロフィールビュー", "ai_skill_parser": "AIスキルパーサー", "recommendation_weight": "推薦の重み", "custom_domain": "カスタムドメイン", "hireforce_mail": "TalentsForce メール", "career_navigator": "キャリアナビゲーター", "learning_management_system": "学習管理システム", "import_tenant_course": "テナントコース取り込み", "application_form": "応募フォーム", "company": "会社", "placement": "配置", "ip_whitelisting": "IPホワイトリスト", "ai_assistant": "AIアシスタント"}, "hiringPipeline": {"title": "パイプライン テンプレートの採用", "description": "すべてのパイプライン テンプレートをここで管理します。", "dialog": {"create": {"title": "新しいテンプレートを作成する", "description": "求人の採用パイプラインを設定して候補者を管理する"}, "edit": {"title": "テンプレートの編集"}}, "table": {"inputSearch": "氏名で検索", "empty": {"title": "結果が見つかりません", "description": "検索に一致する結果はありません。", "btnAddNew": "新しく追加する"}, "columnsName": {"name": "氏名", "stages": "ステージ", "lastUpdated": "最終更新"}}}, "requisitions": {"title": "要請", "description": "求人を作成し、承認フローを設定します。", "form": {"placeholderApprovalFlow": "承認フロー名を入力", "labelApprovalFlow": "承認フロー", "descriptionApprovalFlow": "承認者の割り当てと、必要な最低承認数を設定します。"}, "stepIndex": "ステップ {{itemIndex}}", "approvers": "承認者", "requiredApprovals": "承認が必要", "searchInput": "氏名で検索", "empty": {"title": "承認フローがありません", "description": "承認フローを追加していないようです。", "titleSearch": "承認フローがありません", "descriptionSearch": "検索に一致する結果はありません。"}, "table": {"numberOfSteps": "ステップ数", "lastUpdated": "最終更新"}}, "rolePermissions": {"admin": "{{domain}} へのフルアクセス: 求人、候補者、設定、レポート、他等", "limitedMember": "アサインされた求人において、候補者のノートやフィードバックの記載を行います。", "member": "すべてのアサインされた求人の管理や設定、表示できます。"}, "profiles": {"activePage": {"title": "プロフィールの有効化", "description": "プロフィールを有効にして、採用や機会に関する情報を共有してください。", "modalActive": {"title": "利用規約同意", "description1": "プロフィールを有効化することで、私は以下の内容を読んだことを確認します。", "description2": " 利用規約 ", "description3": "そして、{{companyName}} が自身の求人応募を処理する為に、個人情報を保存することに同意します。"}, "alert": {"createFail": "現時点ではプロフィールを作成できません。", "deletedFile": "ファイルが削除されました。", "updatePermissionSuccess": "共有権限が更新されました"}}, "tab": {"information": "情報", "resume/cv": "履歴書", "files": "ファイル"}, "title": "あなたのプロフィール", "description": "魅力的な機会を発見する為に、プロフィールを設定してください。"}, "import": {"import": "インポート", "history": "履歴", "upload_files": "アップロードファイル", "mapping_data": "データのマッピング", "next": "次へ", "view_sample_file": "最良の結果を得るには、<0>サンプル ファイル</0> を参照し、それに応じてファイルを調整してください。", "only_support_csv": "最大 {{numberOfRecord}} レコード、最大 {{maxFile}} MBのCSVのみサポートされています。", "file_fields": "ファイル項目", "job_fields": "求人項目", "profile_fields": "候補フィールド", "currently_importing_data": "現在、データをインポートしています...", "import_will_completed_shortly": "インポートはまもなく完了します。完了しましたら、Eメールをお送りします。", "import_failed": "インポートに失敗しました", "update_and_try_again": "予期されないエラーが発生しました。ファイルを更新して、もう一度お試しください。", "column": "列", "row": "行", "reason": "理由", "try_again": "もう一度お試しください", "file_name": "ファイル名", "status": "ステータス", "objects": "オブジェクト", "entries": "エントリー", "uploaded_by": "アップロード", "import_date": "インポート日付", "reupload": "再アップロード", "empty_history": {"title": "インポートファイルがありません", "description": "ファイルをインポートしていないようです!", "titleSearch": "結果が見つかりません。", "descriptionSearch": "検索条件に一致する結果はありません。", "import_file": "インポートファイル"}, "created": "<0>{{num}}</0> がインポートされました", "updated": "<0>{{num}}</0> が更新されました", "failed": "<0>{{num}}</0> が失敗しました", "no_action": "<0>{{num}}</0> インポートがスキップされました", "retryJobs": "<0>{{num}}</0> 件のジョブがスキル解析に失敗しました", "job_id": "ジョブ ID", "profile_id": "ID", "sources": {"jobs": {"title": "仕事", "description": "必要な情報を入力して、求人を公開します。"}, "candidate": {"title": "候補者", "description": "インポートに必要な情報を入力します。"}, "course": {"title": "コース", "description": "既存コースを取り込み効率化"}, "company": {"title": "会社", "description": "インポートに必要な情報を入力します。"}}}, "audit_logs": {"title": "監査ログ", "description": "監査ログには、このワークスペース内のすべてのアクティビティと変更が記録されます。", "table": {"from_to": "期間", "user": "ユーザー", "event": "イベント", "trackingIp": "IPアドレス", "date": "日付", "title_search": "結果が見つかりません", "description_search": "検索に一致する結果はありません。", "title_empty": "アクティビティはありません", "description_empty": "現在、表示するアクティビティはないようです。"}}, "transferOwnerTitle": "所有権の譲渡", "transferOwnerDescription": "責任と資産を別のチーム メンバーに再割り当てします。", "team": {"addNewTeam": "新しいトップレベルチーム", "search": "チーム名から検索", "member": "メンバー", "addSubTeamTitle": "サブチームを追加", "addSubTeamDescription": "<0>{{teamName}}</0>チームの配下に", "teamsTitle": "チーム", "teamsDescription": "すべてのチームをここで管理", "addNewTeamModalTitle": "トップレベルのチームを追加", "editTeamModalTitle": "チームを編集", "deleteTopLevelTeam": "トップレベルのチームを削除", "deleteTopLevelTeamDescription": "<0>{{teamName}}</0>チームを完全に削除しようとしています。これにより、<1><0>{{teamName}}</0>のすべてのサブチームが削除されます。</1><1>現在リンクされているすべてのユーザーの割り当ても解除されます。</1>削除してもよろしいですか？", "deleteTeamDescription": "<0>{{teamName}}</0> チームを削除してもよろしいですか？ <br> このチームを削除すると、関連するユーザーもすべて削除されます。この操作は元に戻せません。", "moveTeam": "チームを移動", "moveTeamUnder": "<span class='mx-1 font-medium text-gray-900'>{{name}}</span> を次のチームの配下に移動", "teamEmptyTitle": "チームはありません。", "teamEmptyDescription": "チームがまだ追加されていません。", "moveTeamTop": "<span class='mx-1 font-medium text-gray-900'>{{name}}</span>をトップレベルに移動", "deleteSubTeamDescription": "<0>{{teamName}}</0> チームを削除してもよろしいですか？ <br> このチームを削除すると、関連するユーザーもすべて削除されます。この操作は元に戻せません。", "deleteSubTeamTitle": "サブチームを削除", "titleSearchEmpty": "結果が見つかりません", "descriptionSearchEmpty": "検索に一致する結果はありません。", "name": "名", "allTeams": "すべてのチーム"}, "somethingWentWrong": "何か問題が発生しました"}