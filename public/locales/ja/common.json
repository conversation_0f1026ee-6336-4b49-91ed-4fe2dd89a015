{"learn_more": "もっと詳しく知る", "more": "もっと", "numberMore": "さらに {{number}} 個", "invite": "招待", "sidebar": {"search": "検索", "notification": "通知", "blocked": "ブロック中", "selectCompany": "会社を選択してください", "management": {"title": "データ管理", "list": {"universalSearch": "検索", "dashboard": "ダッシュボード", "notifications": "通知", "candidates": "候補者", "jobs": "求人サイト", "tasks": "タスク", "companies": "企業", "clients": "クライアント", "mailbox": "メールボックス", "tasksAndMeetings": "タスクと会議", "reports": "報告", "calendar": "カレンダー", "referrals": "キャリアハブ", "requisitions": "要請", "talentPool": "タレントプール", "placements": "配置", "contacts": "連絡先", "courseManagement": "コース"}}, "settings": {"title": "設定", "list": {"accountTitle": "マイ アカウント", "accountList": {"profiles": "プロフィール", "profile": "プロフィール", "preferences": "環境設定", "account": "アカウント", "emailSetting": "メール設定"}, "companyTitle": "会社", "companyList": {"general": "一般", "profile": "会社概要", "careerhub": "キャリアハブ", "requisitions": "要請", "careers": "キャリアページ", "termsAndConditions": "利用規約", "teamsDepartments": "チーム・部門", "teamMembers": "メンバー", "positions": "ポジション", "locations": "所在地", "plans": "プラン", "import": "インポート", "audit_logs": "監査ログ", "teams": "チーム"}, "workFlowTitle": "ワークフロー", "workSpaceTitle": "ワークスペース", "recruitingTitle": "人材募集", "workFlowList": {"disqualificationReasons": "不採用理由", "tags": "タグ"}, "companyStatus": "会社の状況", "companySettings": "会社設定", "templatesTitle": "テンプレート", "templatesList": {"emailTemplates": "電子メールテンプレート", "profileFields": "プロフィールフィールド", "hiringPipelines": "採用パイプライン", "customFields": "カスタム項目"}, "templates_title": "テンプレート", "templates_list": {"disqualification_reasons": "不採用理由"}, "interviewKits": "面接キット", "profileTemplates": "プロフィールテンプレート", "security": "セキュリティ", "skills": "スキル"}, "closeSetting": "設定を閉じる"}, "profile": {"viewProfile": "プロフィールを見る", "careerPage": "のキャリアページ", "dropdown": {"account": "アカウント", "settings": "設定", "careerHub": "キャリアハブ", "helpCenter": "ヘルプセンター", "logOut": "ログアウト", "switchCompany": "会社を切り替える", "downloadExtension": "ダウンロード拡張機能"}, "tooltip_view_profile": "プロフィールと設定"}, "yourCareerPage": "キャリアページ"}, "infinity": {"showMore": "さらに表示"}, "table": {"show25More": "さらに 25 件を表示", "endOfList": "リストの最後に到達しました!"}, "error": {"404": {"title": "ページが見つかりません", "description": "何か問題が発生しました。ウェブサイトの URL をもう一度確認するか、<0>{{email}}</0> までお問い合わせください。", "button": "家に帰る"}, "500": {"title": "サーバーエラー", "description": "何か問題が発生しました。このページを更新してみるか、<0>{{email}}</0> までご連絡ください。", "button": "ページをリロードする"}, "access_denied": {"title": "ええとああ！このコンテンツにアクセスできません", "description": "権限エラーが起こっている様です。一度ログアウトボタンを押して、再度ログインを試して下さい。", "contact": "もしまだ直らなければ、お手数ですが管理者にお問い合わせ頂くか、下記までご連絡下さいませ。<0>{{email}}</0>", "button": "家に帰る"}, "tenant_blocked": {"title": "あなたのアカウントはブロックされています", "description": "あなたのアカウントはブロックされているようです。アカウントを再度アクティブ化するには、サポートにお問い合わせください。", "button": "お問い合わせ"}, "expired_plan": {"title": "ああ、サブスクリプションの有効期限が切れました。", "description": "アカウントのサブスクリプションの有効期限が切れたようです。この問題を解決するには、ヘルプおよびサポート チームにお問い合わせいただくか、再度ログインしてください。", "button": "お問い合わせ"}, "406": {"title": "不正なアクセス", "description": "このシステムへのアクセス権限をお持ちでありません。サポートが必要な場合は、システム管理者までご連絡ください。"}}, "modal": {"discard_unsaved_changes_title": "保存されていない変更を破棄する", "discard_unsaved_change_invite": "あなたの招待はキャンセルされます", "discard_unsaved_changes_description": "加えた変更は保存されません。", "discard_unsaved_changes_full": "保存されていない変更を破棄します。加えた変更は保存されません。", "discard_unsaved_upload_file": "テンプレートがアップロード中です。本当にこのページを離れますか？", "delete_note_title": "ートを削除する", "delete_note_description": "ノートを削除しようとしています。この操作は元に戻すことができません。", "deleteNoteTitle": "ートを削除する", "deleteNoteDescription": "ノートを削除しようとしています。この操作は元に戻すことができません。", "attachment_failed_title": "添付に失敗しました", "attachment_failed_description": "最大 10 ファイルをアップロードできます。", "remove_member_title": "メンバーの削除", "remove_member_description": "<strong>{{name}}</strong> を削除してもよろしいですか?彼らはこのジョブにアクセスできなくなります。", "change_job_status_title": "ジョブステータスの変更", "change_company_status_title": "ステータスを変更", "change_job_status_description": "<strong class='font-medium'>{{title}}</strong> のステータスを <strong class='font-medium'>{{name}}</strong> に変更してもよろしいですか?", "change_job_status_description_0": "ジョブ ステータスを <strong class='font-medium'>{{name}}</strong> に変更してもよろしいですか?", "change_job_status_description_1": "これにより、すべての求人サイトで求人が非公開になり、候補者の処理やメモの追加ができなくなります。", "change_job_status_description_2": "これにより、すべての求人サイトで求人が非公開になります。", "confirmation_title": "確認", "confirmation_description": "現在のカスタム パイプラインは失われます。この操作は元に戻すことができません。", "delete_cv_title": "履歴書の削除", "delete_cv_description": "消去してもよろしいですか？この操作は元に戻すことができません。", "unmark_as_hired_title": "雇用済みとしてマークを解除する", "unmark_as_hired_description": "<span class='ml-1 font-medium text-gray-900'>{{title}}</span> の雇用マークを解除してもよろしいですか?採用日、入社日、採用者情報がクリアされます。この操作は元に戻すことができません。", "delete_hiring_pipeline_title": "採用パイプライン テンプレートを削除する", "delete_hiring_pipeline_description": "<span class='ml-1 font-medium text-gray-900'>{{title}}</span> を削除してもよろしいですか?この操作は元に戻すことができません。", "delete_interview_title": "インタビューキットを削除する", "delete_interview_description": "<span class='ml-1 font-medium text-gray-900'>{{title}}</span> を削除してもよろしいですか?この操作は元に戻すことができません。", "delete_approval_title": "承認フローを削除", "delete_approval_description": "<span class='ml-1 font-medium text-gray-900'>{{title}}</span> を削除してもよろしいですか?この操作は元に戻すことができません。", "delete_stage_title": "ステージの削除", "delete_stage_description": "<span class='ml-1 font-medium text-gray-900'>{{title}}</span> を削除してもよろしいですか?この操作は元に戻すことができません。", "delete_stage_description_01": "現在、このステージに関連付けられている資格のある候補者は {{qualifiedCount}} 名、失格となった候補者は {{disQualifiedCount}} 名です。ステージの削除を続行する前に、ステージが空であることを確認してください。", "delete_task_title": "タスクを削除", "delete_task_description_Text": "<b>{{title}}</b> を削除してもよろしいですか?この操作は元に戻すことができません。", "delete_this_interview": "このインタビューを削除する", "delete_this_interview_description": "<span class='font-medium text-gray-900'>{{eventType}}</span> を削除してもよろしいですか?この操作は元に戻すことができません。下のチェックボックスを選択して、候補者と出席者に通知します。", "delete_this_evaluation": "この評価を削除する。", "delete_this_evaluation_description": "この評価を削除してもよろしいですか?この操作は元に戻すことができません。", "pending_feedback_reminder": "フィードバックリマインダーを保留中", "pending_feedback_reminder_description": "まだ送信されていないインタビュー {{fromDatetime}} からのフィードバックを待っています。", "move_back_to_stage": "{{stage<PERSON><PERSON><PERSON>}} ステージに戻りますか?", "move_back_to_stage_description": "<span class='ml-1 font-medium text-gray-900'>{{fullName}}</span> を {{stageLabel}} ステージに戻してもよろしいですか?配置情報がクリアされます。この操作は元に戻すことができません。", "delete_custom_fields": "追加フィールドを削除", "delete_custom_fields_description": "<span class='font-medium text-gray-900'>{{fieldName}}</span> オプションを削除してもよろしいですか? このオプションを削除すると、すべての {{name}} からデータが完全に削除され、元に戻すことはできません。", "delete_profile_templates_section": "セクションを削除", "delete_profile_templates_title": "プロフィールテンプレートを削除", "delete_profile_templates_description": "<span class='font-medium text-gray-900'>{{title}}</span> を削除してもよろしいですか? この操作は元に戻せません。", "delete_file_profile_title": "ファイルを削除する", "delete_file_profile_description": "<strong class='font-medium'>{{filename}}</strong> を削除してもよろしいですか? この操作は元に戻せません。", "leave_page_title": "ページを離れる", "leave_page_description": "テンプレートがアップロード中です。本当にこのページを離れますか？", "delete_candidate_title": "候補者を削除", "delete_candidate_description": "このプロフィールとすべての関連データを削除しようとしています。この操作は元に戻せません。続行してもよろしいですか?", "deleteCandidatesDescription": "候補者を一括削除してもよろしいですか? この操作は元に戻せません。続行してもよろしいですか?", "moveApplicantsToNewStage": "応募者を新しいステージに移動", "moveApplicantsToNewStageDescription": "選択された応募者の一部は現在「採用済み」ステージにいます。別のステージに移動すると、すべての関連する採用情報が削除されます。本当に移動しますか？", "moveApplicantsToNewStageAgencyDescription": "選択された応募者の一部は現在「採用済み」ステージにいます。別のステージに移動すると、すべての関連する配置情報が削除されます。本当に移動しますか？"}, "you_not_in_hiring_team": "あなたは採用チームに追加されていない為、この求人にアクセスすることはできません。", "mobile": {"labelMobileVersionUnavailable": "モバイル版は利用不可", "descriptionMobileVersionUnavailable": "ご不便をおかけして申し訳ございません。 {{domain}} は現在、モバイル デバイスではサポートされていません。続行するには、デスクトップまたはラップトップに切り替えてください。"}, "upgradeUnlockFeature": "アップグレードしてこの機能をロック解除してください。", "show_more": "さらに表示", "seo": {"500": "エラー - {{PUBLIC_APP_NAME}}", "404": "見つかりません - {{PUBLIC_APP_NAME}}", "tenantBlocked": "ブロックされた。 - {{PUBLIC_APP_NAME}}", "accessDenied": "アクセスが拒否されました。 - {{PUBLIC_APP_NAME}}", "authError": "承認エラー - {{PUBLIC_APP_NAME}}", "register": "サインアップ - {{PUBLIC_APP_NAME}}", "login": "サインイン - {{PUBLIC_APP_NAME}}", "careerHubAuthError": "キャリアハブ 承認エラー - {{PUBLIC_APP_NAME}}", "careerHubLogin": "キャリアハブ サインイン - {{PUBLIC_APP_NAME}} ", "verifyEmail": "E メール確認 - {{PUBLIC_APP_NAME}}", "onboarding": "オンボーディング - {{PUBLIC_APP_NAME}}", "welcome": "いらっしゃいませ - {{PUBLIC_APP_NAME}}", "noAssociatedCompany": "関連会社なし - {{PUBLIC_APP_NAME}}", "selectCompany": "会社を選択 - {{PUBLIC_APP_NAME}}", "candidates": "候補者 - {{PUBLIC_APP_NAME}}", "candidatesWithView": "候補者 | {{VIEW_NAME}} - {{PUBLIC_APP_NAME}}", "candidateDetail": "候補者 {{name}} - {{PUBLIC_APP_NAME}}", "candidateDetailEdit": "Edit CV - {{PUBLIC_APP_NAME}}", "jobs": "仕事 - {{PUBLIC_APP_NAME}}", "jobDetail": "{{jobDetail}} - {{PUBLIC_APP_NAME}}", "jobEdit": "{{jobEdit}} - {{PUBLIC_APP_NAME}}", "jobCreate": "新しい仕事 - {{PUBLIC_APP_NAME}}", "jobDuplicate": "新しい仕事 - {{PUBLIC_APP_NAME}}", "jobAppliedCareerDetails": "応募しました - {{PUBLIC_APP_NAME}}", "calendar": "カレンダー - {{PUBLIC_APP_NAME}}", "referrals": "紹介 - {{PUBLIC_APP_NAME}}", "reports": "レポート - {{PUBLIC_APP_NAME}}", "settingRequisition": "設定ーリクエスト - {{PUBLIC_APP_NAME}}", "requisition": "リクエスト - {{PUBLIC_APP_NAME}}", "settingMembers": "設定ーメンバー - {{PUBLIC_APP_NAME}}", "settingPositions": "設定 - ポジション - {{PUBLIC_APP_NAME}}", "settingPositionDetail": "{{positionDetail}} - {{PUBLIC_APP_NAME}}", "settingPositionEdit": "{{positionEdit}} - {{PUBLIC_APP_NAME}}", "settingProfiles": "設定 - プロフィール - {{PUBLIC_APP_NAME}}", "settingDepartments": "設定 - 部門 - {{PUBLIC_APP_NAME}}", "settingSKills": "設定 - スキル - {{PUBLIC_APP_NAME}}", "settingLocations": "設定 - 場所 - {{PUBLIC_APP_NAME}}", "settingImport": "設定 - インポート - {{PUBLIC_APP_NAME}}", "settingWorkspace": "設定 - ワークスペース - {{PUBLIC_APP_NAME}}", "settingAccount": "設定 - アカウント - {{PUBLIC_APP_NAME}}", "settingEmailSignature": "設定 - メール設定 - {{PUBLIC_APP_NAME}}", "settingPreferences": "設定 - 環境設定 - {{PUBLIC_APP_NAME}}", "settingCareerPage": "設定 - キャリアページ - {{PUBLIC_APP_NAME}}", "settingEmailTemplates": "設定 - メールテンプレート - {{PUBLIC_APP_NAME}}", "settingDisqualifyReasons": "設定 - 失格理由 - {{PUBLIC_APP_NAME}}", "settingCustomFields": "設定 - カスタムフィールド - {{PUBLIC_APP_NAME}}", "settingHiringPipelines": "設定 - パイプラインの雇用 - {{PUBLIC_APP_NAME}}", "settingInterviewKits": "設定 - インタビューキット - {{PUBLIC_APP_NAME}}", "settingTags": "設定 - タグ - {{PUBLIC_APP_NAME}}", "settingReferrals": "設定 ー キャリアハブ - {{PUBLIC_APP_NAME}}", "settingPlans": "プラン - {{PUBLIC_APP_NAME}}", "settingExpiredPlans": "プランの有効期限が切れました - {{PUBLIC_APP_NAME}}", "settingProfileTemplates": "設定 - プロフィール テンプレート - {{PUBLIC_APP_NAME}}", "settingCompanySettings": "設定 - 会社設定 - {{PUBLIC_APP_NAME}}", "feedback": "フィードバック - {{PUBLIC_APP_NAME}}", "requisitionsCreate": "新規リクエスト - {{PUBLIC_APP_NAME}}", "requisitionEdit": "{{requisitionName}} - {{PUBLIC_APP_NAME}}", "expiredTrial": "プランの有効期限が切れました - {{PUBLIC_APP_NAME}}", "talentPool": "タレントプール - {{PUBLIC_APP_NAME}}", "auditLogs": "監査ログ - {{PUBLIC_APP_NAME}}", "courseManagement": "コース - {{PUBLIC_APP_NAME}}", "agency": {"companies": "会社 - {{PUBLIC_APP_NAME}}", "contacts": "連絡先 - {{PUBLIC_APP_NAME}}", "settings": {"companyStatus": "設定ー会社のステータス - {{PUBLIC_APP_NAME}}"}}, "placements": "配置 - {{PUBLIC_APP_NAME}}", "interviewInvitation": "{{name}} - 面接の招待 - {{PUBLIC_APP_NAME}}", "selfSchedule": "セルフスケジュール予約フォーム - {{PUBLIC_APP_NAME}}", "resumesEditor": "プロフィール - テンプレートの編集 - {{PUBLIC_APP_NAME}}", "settingSecurity": "設定 - セキュリティ - {{PUBLIC_APP_NAME}}", "dashboard": "ダッシュボード - {{PUBLIC_APP_NAME}}", "settingSkillReport": "スキルレポート - {{PUBLIC_APP_NAME}}", "settingTeams": "設定 - チーム - {{PUBLIC_APP_NAME}}", "careerNavigator": "キャリアナビゲーター - {{PUBLIC_APP_NAME}}", "careerNavigatorPosition": "キャリアナビゲーター - ポジション - {{PUBLIC_APP_NAME}}", "406": "アクセス権がありません - {{PUBLIC_APP_NAME}}", "learningLibrary": "学習ライブラリ - {{PUBLIC_APP_NAME}}"}, "upload": "アップロード", "limit_file_size": "対応画像ファイル形式はJPG、JPEG、PNGです。ファイルの最大サイズは{{file_size}}です。", "upload_failed": "アップロードに失敗しました。ファイル形式は正しくありません。", "reach_limit_of_images": "アップロード可能な画像の上限である{{num}}枚に達しました。", "allSelected": "すべて選択", "selected": "{{numberCount}}選択", "selectedWithoutNumber": "選択", "locations": "所在地", "sources": "ソース", "jobs": "求人サイト", "notification": {"showUnread": "未読を表示", "markAllAsRead": "すべてを既読にする", "deleteAllNotification": "すべての通知を削除する", "notificationSettings": "通知設定", "empty": {"title": "通知なし", "description": "何かが起きたら、ここに表示されます。"}}, "selectedBulk": "<0>{{numberCount}}</0> 件中 <0>{{totalCount}}</0> 件が選択されました", "equivalentSKills": "同等のスキル", "equivalentSKillDescription": "{{total}} 同等のスキル", "aiSuggests": "AIの提案", "aiSuggestsDescription": "ポジション、履歴書/CV、職務経験に基づくAI推奨スキル", "aiSuggestsJobDescription": "主要なスキルや資格を入力してください。「AI提案」を使用すると、関連するおすすめを取得できます", "noSkillSuggests": "スキルの提案はありません。", "notAvailable": "利用不可", "regenerate": "再生成", "match": "一致", "generate": "生成"}