import { useRouter } from 'next/router'
import { useEffect, useMemo, useState } from 'react'
import withPermissionSettingProvider from 'src/hoc/with-permission-setting'
import ImportFailedFile from '~/components/Settings/Import/components/ImportFailedFile'
import ImportCompletedCompanyFile from '~/components/Settings/Import/ImportCompletedCompanyFile'
import ImportCompletedFile from '~/components/Settings/Import/ImportCompletedFile'
import ImportCompletedProfileFile from '~/components/Settings/Import/ImportCompletedProfileFile'
import ImportCompletedTenantCourseFile from '~/components/Settings/Import/ImportCompletedTenantCourseFile'
import ImportingFile from '~/components/Settings/Import/ImportingFile'
import ImportLayout from '~/components/Settings/Import/ImportLayout'
import { FeatureName } from '~/components/Subscription/subscription'
import { withSubscriptionPlanLockFearture } from '~/components/Subscription/SubscriptionPlan'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import configuration from '~/configuration'
import {
  canAccessSetting,
  PERMISSIONS_LIST
} from '~/core/utilities/feature-permission'
import useImportingFileStatusHook from '~/lib/features/settings/import/hooks/use-importing-file-status-hook'
import { ISourceImportType } from '~/lib/features/settings/import/types'
import {
  ENUM_IMPORT_FILE_STATUS,
  IMPORT_CANDIDATES,
  IMPORT_COMPANIES,
  IMPORT_COURSES,
  IMPORT_JOBS
} from '~/lib/features/settings/import/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import { ImportTypeKey } from '~/lib/features/settings/import/types'
import IconWrapper from '~/core/ui/IconWrapper'

interface Props {
  enableImportJobs: boolean | undefined
  enableImportCandidates: boolean | undefined
  enableImportCourses: boolean | undefined
  enableImportCompanies: boolean | undefined
  query?: { tab?: string; object_kind?: string }
  suspend?: boolean
}

type ImportType = {
  key: ImportTypeKey
  data: ISourceImportType
  enable: boolean
  featureName: FeatureName
  CompletedComponent: React.FC<{
    importType: ISourceImportType
    onTryAgain: () => void
    tab: string
    onBackToHistoryTabList: () => void
  }>
}

type DataImportType = {
  [key in ImportTypeKey]: ImportType
}

const ImportManagementContainer = ({
  enableImportJobs,
  enableImportCandidates,
  enableImportCourses,
  enableImportCompanies,
  suspend,
  query
}: Props) => {
  const router = useRouter()

  const dataImportType = useMemo(() => {
    return {
      job: {
        key: 'job',
        data: IMPORT_JOBS,
        enable: enableImportJobs,
        featureName: PLAN_FEATURE_KEYS.import_job,
        CompletedComponent: ImportCompletedFile
      },
      profile: {
        key: 'profile',
        data: IMPORT_CANDIDATES,
        enable: enableImportCandidates,
        featureName: PLAN_FEATURE_KEYS.import_candidate,
        CompletedComponent: ImportCompletedProfileFile
      },
      tenant_course: {
        key: 'tenant_course',
        data: IMPORT_COURSES,
        enable: enableImportCourses,
        featureName: PLAN_FEATURE_KEYS.learning_management_system,
        CompletedComponent: ImportCompletedTenantCourseFile
      },
      company: {
        key: 'company',
        data: IMPORT_COMPANIES,
        enable: enableImportCompanies,
        featureName: PLAN_FEATURE_KEYS.company,
        CompletedComponent: ImportCompletedCompanyFile
      }
    }
  }, [
    enableImportJobs,
    enableImportCandidates,
    enableImportCourses,
    enableImportCompanies
  ]) as unknown as DataImportType

  const listSourceImportType = useMemo(() => {
    return Object.values(dataImportType)
      .filter((item) => item.enable)
      .map((item) => item.data)
  }, [dataImportType])

  const [importTypeKey, setImportTypeKey] = useState<ImportTypeKey>()

  const importType = useMemo(() => {
    if (!importTypeKey) {
      return undefined
    }
    return dataImportType[importTypeKey]?.data as ISourceImportType
  }, [dataImportType, importTypeKey])

  const {
    importFileStatus,
    currentStep,
    nextStep,
    previousStep,
    backToFirstStep
  } = useImportingFileStatusHook({ importType })

  useEffect(() => {
    if (query?.object_kind) {
      setImportTypeKey(query?.object_kind as ImportTypeKey)
      return
    }

    const firstImportType = Object.values(dataImportType).find(
      (item) => item.enable
    )
    if (firstImportType) {
      setImportTypeKey(firstImportType.key)
    }
  }, [query])

  if (!importType || !importTypeKey) {
    // return example in this case lock
    console.log({ dataImportType })
    return null
    return (
      <ImportLayout
        listSourceImportType={listSourceImportType}
        importType={dataImportType['job'].data}
        suspend={suspend}
        importFileStatus={importFileStatus}
        actionUploadFile={{
          goToUploadFileStep: backToFirstStep,
          nextStep,
          previousStep
        }}
        currentStep={currentStep}
      />
    )
  }

  const CompletedFileComponent =
    dataImportType[importTypeKey]?.CompletedComponent

  return (
    <ImportLayout
      listSourceImportType={listSourceImportType}
      importType={importType}
      suspend={suspend}
      importFileStatus={importFileStatus}
      actionUploadFile={{
        goToUploadFileStep: backToFirstStep,
        nextStep,
        previousStep
      }}
      currentStep={currentStep}
      importDetail={(tab) => (
        <>
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.in_progress && (
            <ImportingFile />
          )}
          {importFileStatus === ENUM_IMPORT_FILE_STATUS.failed && (
            <ImportFailedFile
              importType={importType}
              onTryAgain={backToFirstStep}
              tab={tab}
              onBackToHistoryTabList={() =>
                router.push(
                  `${configuration.path.settings.import}?tab=history&object_kind=${router.query['object_kind']}`
                )
              }
            />
          )}
          {[
            ENUM_IMPORT_FILE_STATUS.completed,
            ENUM_IMPORT_FILE_STATUS.partial
          ].includes(importFileStatus || '') && (
            <CompletedFileComponent
              importType={importType}
              onTryAgain={backToFirstStep}
              tab={tab || ''}
              onBackToHistoryTabList={() =>
                router.push(
                  `${configuration.path.settings.import}?tab=history&object_kind=${router.query['object_kind']}`
                )
              }
            />
          )}
        </>
      )}
    />
  )
}

const ImportManagementWrapper = (props: any) => {
  const { loading, isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()

  const featureNameRepresent = useMemo(() => {
    const planFeatures = [
      PLAN_FEATURE_KEYS.import_job,
      PLAN_FEATURE_KEYS.import_candidate,
      PLAN_FEATURE_KEYS.learning_management_system,
      PLAN_FEATURE_KEYS.company
    ]

    const featureEnabledAndUnLock = planFeatures.find((featureName) => {
      return isFeatureEnabled(featureName) && isUnLockFeature(featureName)
    })
    const featureEnabled = planFeatures.find((featureName) =>
      isFeatureEnabled(featureName)
    )
    const featureUnLock = planFeatures.find((featureName) =>
      isUnLockFeature(featureName)
    )
    console.log({
      featureEnabledAndUnLock,
      featureEnabled,
      featureUnLock
    })
    return featureEnabledAndUnLock || featureUnLock || featureEnabled
  }, [loading])

  //  Using PLAN_FEATURE_KEYS.import_job as default if no feature show lock screen.
  const featureNameImport = featureNameRepresent || PLAN_FEATURE_KEYS.import_job

  console.log({ featureNameImport })

  const WrappedComponent = useMemo(
    () =>
      withSubscriptionPlanLockFearture(
        ImportManagementContainer,
        featureNameImport as FeatureName,
        {
          classNameHeightOfView: 'min-h-0 h-full'
        }
      ),
    [featureNameImport]
  )

  if (loading)
    return (
      <div className="flex size-full items-center justify-center">
        <IconWrapper
          name="Loader2"
          size={40}
          className="animate-spin text-gray-400 dark:text-gray-400"
        />
      </div>
    )
  return <WrappedComponent {...props} />
}

export default withPermissionSettingProvider(
  {
    checkAccessPermission: canAccessSetting,
    keyModule: [PERMISSIONS_LIST.tenant_setting.keyModule]
  },
  ImportManagementWrapper
)
