import { Editor as CoreEditor } from '@tiptap/core'
import {
  FC,
  useCallback,
  useContext,
  useRef,
  useState,
  useLayoutEffect
} from 'react'
import { Controller, UseFormReset } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { MultiValue } from 'react-select'
import pathConfiguration from 'src/configuration/path'
import { ZodType, ZodTypeDef } from 'zod'
import configuration from '~/configuration'
import { IFormAction } from '~/core/@types/global'
import { openAlert } from '~/core/ui/AlertDialog'
import { AsyncMultipleSearchWithSelect } from '~/core/ui/AsyncMultipleSearchWithSelect'
import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { Button } from '~/core/ui/Button'
import { Container } from '~/core/ui/Container'
import { Divider } from '~/core/ui/Divider'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { TypographyH5 } from '~/core/ui/Heading'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Input } from '~/core/ui/Input'
import { MultipleSelect } from '~/core/ui/MultipleSelect'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { RichEditor } from '~/core/ui/RichEditor'
import { ISelectOption } from '~/core/ui/Select'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import {
  createPreventPositiveChange,
  formatAddressLocation
} from '~/core/utilities/common'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { adminCanAction } from '~/core/utilities/permission'
import { JobCreatePermissionContext } from '~/features/jobs/create'
import { JobEditPermissionContext } from '~/features/jobs/[id]/edit'
import { CompanyDetailType } from '~/lib/features/agency/companies/types/company-detail'
import MutationAIWriterJobDescription from '~/lib/features/jobs/graphql/mutation-AI-writer-job-description'
import useJobs from '~/lib/features/jobs/hooks/use-jobs'
import { IJobForm } from '~/lib/features/jobs/types'
import usePermissionCompany from '~/lib/features/permissions/hooks/use-permission-company'
import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import useDepartment from '~/lib/features/settings/departments/hooks/useDepartment'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import useTenantSettingJobFieldsHook from '~/lib/features/settings/profile-fields/hooks/use-tenant-setting-job-field-hook'
import {
  mappingTypeToFieldKind,
  renderKeyCustomFieldForm
} from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import { CustomFieldFormType } from '~/lib/features/settings/profile-fields/types/custom-field'
import QueryAddTagMutation from '~/lib/features/settings/tags/graphql/submit-add-tag-mutation'
import { TAG_KIND } from '~/lib/features/settings/tags/utilities/enum'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import LocationModal from '../Agency/Companies/[id]/LocationModal'
import CustomField, { CustomFieldComponentType } from '../CustomField'
import DiscardChangeView from '../DiscardChangeView/DiscardChangeView'
import useSubscriptionPlan from '../Subscription/useSubscriptionPlan'
import JobHiringView from './JobHiringView'
import JobTitleFormView from './JobTitleFormView'
import useToastStore from '~/lib/store/toast'
import { TypographyText } from '~/core/ui/Text'
import { SuggestionInlineChips } from '~/core/ui/SuggestionChips'
import useSkillSettingsStore from '~/lib/features/settings/skills/store'

const JobForm: FC<{
  isCompanyKind: boolean
  onUpdateCompanyLocation?: (data: CompanyDetailType) => Promise<any>
  isEdit: boolean
  isLoading: boolean
  onFinishCallback: (data: IJobForm, formAction: IFormAction) => Promise<void>
  defaultValue?: IJobForm
  onCancelAction?: () => void
  schema: ZodType<any, ZodTypeDef, any>
}> = ({
  isCompanyKind = false,
  onUpdateCompanyLocation,
  isEdit = false,
  isLoading = false,
  onFinishCallback,
  defaultValue,
  onCancelAction,
  schema
}) => {
  const { t } = useTranslation()
  const { user, currentRole } = useBoundStore()
  const { setToast } = useToastStore()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { allowAddNewSkill } = useSkillSettingsStore()
  const [openAIWriter, setOpenAIWriter] = useState(false)
  const [openAISuggestSkills, setOpenAISuggestSkills] = useState(false)
  const [isGettingSuggestSkills, setIsGettingSuggestSkills] =
    useState<boolean>(false)
  const [showMoreSkills, setShowMoreSkills] = useState<boolean>(false)
  const [isShowMoreClicked, setIsShowMoreClicked] = useState<boolean>(false)
  const [aiSuggestedSkills, setAISuggestedSkills] = useState<ISelectOption[]>(
    []
  )
  const [currentSkills, setCurrentSkills] = useState<ISelectOption[]>([])
  const skillSuggestionRef = useRef<HTMLDivElement | null>(null)

  const toggleAIWriter = () => {
    setOpenAIWriter(!openAIWriter)
  }
  const [textGenerate, setTextGenerate] = useState('')
  const {
    promiseCompanyListOptions,
    promiseLocationOptions,
    promiseCompanyLocationOptions,
    promiseJobCategoriesOptions,
    promiseSkillsOptions,
    promiseTalentPoolsOptions,
    promiseTagsOptions,
    promiseTitleOptions,
    jobCurrency,
    jobEmployment,
    jobRemoteStatus,
    jobTypeOfSalary,
    jobLevel,
    jobEducation,
    languageList
  } = useJobs()

  const { promiseDepartmentMultiLevelOptions } = useDepartment()
  const { data: customFieldViewData } = useCustomFieldSettingByUser({
    objectKind: 'job'
  })
  const { isShowSystemField } = useTenantSettingJobFieldsHook()
  const [valueAIWriter, setValueAIWriter] = useState<{
    language?: string
    toneType?: string
  }>({
    language: user.language || 'en',
    toneType: 'formal'
  })
  const isFullAction = adminCanAction(currentRole?.code)
  const [openLocationModal, setOpenLocationModal] = useState(false)
  const resetFormRef = useRef<UseFormReset<IJobForm>>()

  const { actionCompany } = usePermissionCompany()
  const { actionLocation, actionTag } = usePermissionSetting()
  const { trigger: triggerAddNewTag, isLoading: isLoadingAddNewTag } =
    useSubmitCommon(QueryAddTagMutation)

  const JobPermissionContext = useContext(
    isEdit ? JobEditPermissionContext : JobCreatePermissionContext
  )
  const { create: createPermission, update: updatePermission } =
    JobPermissionContext

  const { trigger: triggerAIWriterJobDescription, isLoading: loadingGenerate } =
    useSubmitCommon(MutationAIWriterJobDescription)

  const richEditorRef = useRef<CoreEditor>()

  const isShowEquivalentsSkills =
    isFeatureEnabled(PLAN_FEATURE_KEYS.skill_management) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.skill_management)

  // ---------- LIFECYCLE ----------
  const onSubmitAIWriterJobDescription = useCallback(
    async (data: IJobForm, formAction: IFormAction) => {
      if (loadingGenerate) {
        return
      }
      triggerAIWriterJobDescription({
        jobId: isEdit ? Number(defaultValue?.id) : undefined,
        title: data.title,
        description: textGenerate,
        toneType: valueAIWriter.toneType,
        skills:
          (data.skills || []).length > 0
            ? (data.skills || []).map((item) => item.value)
            : undefined,
        language: valueAIWriter.language,
        jobLevel: data.jobLevel || undefined,
        locationIds: (data.locationIds || [])
          .map((item) => Number(item.value))
          .filter((i) => i !== null && i !== undefined),
        salaryFrom: Number(data.salaryFrom) || undefined,
        salaryTo: Number(data.salaryTo) || undefined,
        currency: data.currency || undefined,
        typeOfSalary: data.typeOfSalary || undefined,
        education: data.education || undefined,
        remoteStatus: data.remoteStatus || undefined
      }).then((result) => {
        if (result.error) {
          const parseErrors = JSON.parse(JSON.stringify(result.error))
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            callbackHandleStatusError422: (keys) => {
              if (keys.length === 0) {
                return setToast({
                  open: true,
                  type: 'error',
                  title: parseErrors.graphQLErrors[0].message
                })
              }
            }
          })
        }

        const { jobDescription } = result.data.jobDescriptionAiWriter
        if (jobDescription) {
          setTextGenerate(jobDescription)
        }
        return true
      })
    },
    [loadingGenerate, valueAIWriter.toneType, valueAIWriter.language]
  )

  const onSuggestAISkills = async (
    data: IJobForm,
    formAction?: IFormAction
  ) => {
    if (!data.title && !data.description) {
      setAISuggestedSkills([])
      setCurrentSkills([])
      return
    }
    setIsGettingSuggestSkills(true)

    const suggestedSkills = await promiseSkillsOptions({
      search: '',
      page: 1,
      limit: 1000,
      jobTitle: data.title,
      jobDescription: data.description
    })

    const suggestedSkillsList = filterSuggestedSkills(
      suggestedSkills?.collection,
      (data?.skills as ISelectOption[]) || []
    )

    setAISuggestedSkills(suggestedSkills?.collection)
    setCurrentSkills(suggestedSkillsList)
    setIsGettingSuggestSkills(false)
  }

  const checkSkillInList = (
    skills: ISelectOption[],
    skillName: string
  ): boolean => {
    return !(skills || []).some((item) => item.value === skillName)
  }

  const filterSuggestedSkills = (
    suggestedSkills: ISelectOption[],
    selectedSkills: ISelectOption[]
  ): ISelectOption[] => {
    return suggestedSkills.filter((skill) =>
      checkSkillInList(selectedSkills, skill.value)
    )
  }

  useLayoutEffect(() => {
    if (isShowMoreClicked) {
      return
    }
    if (
      skillSuggestionRef.current &&
      skillSuggestionRef.current.clientHeight >= 99
    ) {
      skillSuggestionRef.current.style.maxHeight = '99px'
      setShowMoreSkills(true)
    } else {
      setShowMoreSkills(false)
    }
  }, [skillSuggestionRef, aiSuggestedSkills, currentSkills])

  return (
    <DynamicImportForm
      id="job-form"
      className="mx-auto w-full max-w-[776px]"
      schema={schema}
      onSubmit={async (data, formAction) => {
        const funcResetForm = resetFormRef?.current
          ? resetFormRef?.current
          : () => {}
        onFinishCallback(data, formAction).then(() => {
          funcResetForm(undefined, { keepValues: true, keepDirty: false })
        })
      }}
      defaultValue={defaultValue}
      noUseSubmit>
      {(formAction) => {
        resetFormRef.current = formAction.reset as UseFormReset<IJobForm>
        const rendered = () => {
          return (
            <div className="mb-5">
              <div className="flex space-x-4">
                <div className="flex-1">
                  <Controller
                    control={formAction.control}
                    name="headcount"
                    render={({ field: { onChange, value } }) => (
                      <FormControlItem
                        destructive={
                          formAction.formState.errors &&
                          !!formAction.formState.errors.headcount
                        }
                        destructiveText={
                          formAction.formState.errors &&
                          (formAction.formState.errors.headcount
                            ?.message as string)
                        }
                        label={`${t('job:formLabelNumberOfHeadcount')}`}
                        labelRequired>
                        <Input
                          destructive={
                            formAction.formState.errors &&
                            !!formAction.formState.errors.headcount
                          }
                          inputType="number"
                          size="md"
                          {...createPreventPositiveChange((value) => {
                            onChange(value !== '0' ? value : '')
                          })}
                          value={value}
                          placeholder={`${t(
                            'job:formPlaceholderNumberOfHeadcount'
                          )}`}
                        />
                      </FormControlItem>
                    )}
                  />
                </div>

                <div className="flex-1">
                  <Controller
                    control={formAction.control}
                    name="referenceId"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <FormControlItem
                          destructive={
                            formAction.formState.errors &&
                            !!formAction.formState.errors.referenceId
                          }
                          destructiveText={
                            formAction.formState.errors &&
                            (formAction.formState.errors.referenceId
                              ?.message as string)
                          }
                          label={`${t('job:form_label_reference_id')}`}
                          helpIcon={
                            <Tooltip
                              content={`${t(
                                'job:form_help_text_reference_id'
                              )}`}
                              classNameAsChild="ml-1">
                              <IconWrapper
                                name="HelpCircle"
                                size={14}
                                className="text-gray-400"
                              />
                            </Tooltip>
                          }>
                          <Input
                            size="md"
                            onChange={onChange}
                            value={value}
                            placeholder={`${t(
                              'job:form_placeholder_job_reference_Id'
                            )}`}
                            destructive={
                              formAction.formState.errors &&
                              !!formAction.formState.errors.referenceId
                            }
                          />
                        </FormControlItem>
                      )
                    }}
                  />
                </div>
              </div>
            </div>
          )
        }
        return (
          <>
            <DiscardChangeView isDirty={formAction.formState.isDirty} />
            <Container overrideClass="max-w-[776px]">
              <Divider className="mt-5" />
              <div className="mt-5">
                <TypographyH5 className="text-gray-900">
                  {t('job:formTitleJobDetails')}
                </TypographyH5>
                <div className="mb-5 mt-3">
                  <JobTitleFormView
                    control={formAction.control}
                    formState={formAction.formState}
                  />
                </div>
                {isCompanyKind === true ? (
                  <>
                    {rendered()}
                    <div className="mb-5">
                      <Controller
                        control={formAction.control}
                        name="companyId"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem
                              label={`${t('job:formLabelCompany')}`}
                              labelRequired
                              destructive={
                                formAction.formState.errors &&
                                !!formAction.formState.errors.companyId
                              }
                              destructiveText={
                                formAction.formState.errors &&
                                (formAction.formState.errors.companyId
                                  ?.message as string)
                              }
                              helpText={
                                <If condition={actionCompany.create}>
                                  <div className="flex">
                                    <span className="text-sm font-normal text-gray-600">
                                      {t('job:formHelpLabelCompany')}
                                    </span>
                                  </div>
                                </If>
                              }>
                              <AsyncSingleSearchWithSelect
                                isDisabled={isCompanyKind && isEdit}
                                promiseOptions={promiseCompanyListOptions}
                                size="md"
                                onChange={(newValue) => {
                                  onChange(newValue)
                                  formAction.setValue(
                                    'locationIds',
                                    [] as never
                                  )
                                }}
                                placeholder={`${t('label:placeholder:select')}`}
                                value={value}
                                menuPlacement="bottom"
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.companyId
                                }
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                    <div className="mb-5">
                      <Controller
                        control={formAction.control}
                        name="locationIds"
                        render={({ field: { onChange, value } }) => (
                          <>
                            <FormControlItem
                              destructive={
                                formAction.formState.errors &&
                                !!formAction.formState.errors.locationIds
                              }
                              destructiveText={
                                formAction.formState.errors &&
                                (formAction.formState.errors.locationIds
                                  ?.message as string)
                              }
                              labelRequired
                              label={`${t('job:formLabelLocation')}`}>
                              <AsyncMultipleSearchWithSelect
                                isClearable={false}
                                isDisabled={
                                  !formAction.getValues()?.companyId?.value
                                }
                                promiseOptions={(params) => {
                                  return promiseCompanyLocationOptions({
                                    ...params,
                                    companyId: Number(
                                      formAction.getValues()?.companyId?.value
                                    )
                                  })
                                }}
                                size="md"
                                onChange={onChange}
                                placeholder={`${t('label:placeholder:select')}`}
                                value={value}
                                configSelectOption={{
                                  option: 'checkbox',
                                  supportingText: ['name', 'description'],
                                  classNameOption: {
                                    multiValue: 'truncate'
                                  }
                                }}
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                className="inline-grid w-full"
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.locationIds
                                }
                              />
                            </FormControlItem>

                            <div className="mt-1 flex">
                              <span className="text-sm font-normal text-gray-600">
                                {t('job:formHelpLabelLocation')}
                              </span>
                              <div className="mx-1">
                                <Tooltip
                                  content={
                                    !formAction.getValues()?.companyId?.value
                                      ? t('job:formHelpLabel01Location')
                                      : undefined
                                  }>
                                  <TextButton
                                    underline={false}
                                    isDisabled={
                                      !formAction.getValues()?.companyId?.value
                                    }
                                    type="primary"
                                    size="md"
                                    label={t('job:formHelpButtonLocation')}
                                    onClick={() => setOpenLocationModal(true)}
                                  />
                                </Tooltip>
                              </div>
                              <span className="text-sm font-normal text-gray-600">
                                {t('job:formHelpLabel02Location')}
                              </span>
                            </div>
                          </>
                        )}
                      />
                    </div>
                  </>
                ) : (
                  <>
                    {rendered()}
                    <div className="mb-5">
                      <div className="flex space-x-4">
                        <div className="flex-1">
                          <Controller
                            control={formAction.control}
                            name="departmentId"
                            render={({ field: { onChange, value } }) => {
                              return (
                                <FormControlItem
                                  destructive={
                                    formAction.formState.errors &&
                                    !!formAction.formState.errors.departmentId
                                  }
                                  destructiveText={
                                    formAction.formState.errors &&
                                    (formAction.formState.errors.departmentId
                                      ?.message as string)
                                  }
                                  label={`${t('job:formLabelDepartment')}`}
                                  labelRequired>
                                  <AsyncSingleSearchWithSelect
                                    promiseOptions={
                                      promiseDepartmentMultiLevelOptions
                                    }
                                    size="md"
                                    onChange={(newValue) =>
                                      onChange(newValue ? [newValue] : [])
                                    }
                                    placeholder={`${t(
                                      'label:placeholder:select'
                                    )}`}
                                    value={value}
                                    destructive={
                                      formAction.formState.errors &&
                                      !!formAction.formState.errors.departmentId
                                    }
                                    classNameOverride={{
                                      loadingMessage: `${t('label:loading')}`,
                                      noOptionsMessage: `${t(
                                        'label:noOptions'
                                      )}`
                                    }}
                                  />
                                </FormControlItem>
                              )
                            }}
                          />
                        </div>
                        <div className="flex-1">
                          <Controller
                            control={formAction.control}
                            name="locationIds"
                            render={({ field: { onChange, value } }) => (
                              <FormControlItem
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.locationIds
                                }
                                destructiveText={
                                  formAction.formState.errors &&
                                  (formAction.formState.errors.locationIds
                                    ?.message as string)
                                }
                                helpText={
                                  <If condition={actionLocation}>
                                    {t('job:formHelpLabel03Location')}
                                    <a
                                      href={
                                        configuration.path.settings.locations
                                      }
                                      target="_blank">
                                      <span className="inline-block pl-1 text-sm font-medium text-primary-400 hover:text-primary-600">
                                        {t('job:formHelpButton01Location')}
                                      </span>
                                    </a>
                                  </If>
                                }
                                labelRequired
                                label={`${t('job:formLabelLocation')}`}>
                                <AsyncMultipleSearchWithSelect
                                  promiseOptions={promiseLocationOptions}
                                  isClearable={false}
                                  size="md"
                                  onChange={onChange}
                                  placeholder={`${t(
                                    'label:placeholder:select'
                                  )}`}
                                  value={value}
                                  configSelectOption={{
                                    option: 'checkbox',
                                    supportingText: [
                                      'name',
                                      'badge',
                                      'description'
                                    ],
                                    classNameOption: {
                                      multiValue: 'truncate'
                                    }
                                  }}
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                  className="inline-grid w-full"
                                  destructive={
                                    formAction.formState.errors &&
                                    !!formAction.formState.errors.locationIds
                                  }
                                />
                              </FormControlItem>
                            )}
                          />
                        </div>
                      </div>
                    </div>
                  </>
                )}
                <div className="mb-5">
                  <Controller
                    control={formAction.control}
                    name="taggedIds"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <FormControlItem
                          destructive={
                            formAction.formState.errors &&
                            !!formAction.formState.errors.taggedIds
                          }
                          destructiveText={
                            formAction.formState.errors &&
                            (formAction.formState.errors.taggedIds
                              ?.message as string)
                          }
                          label={`${t('job:tags')}`}>
                          <Tooltip
                            open={
                              !!formAction.formState.errors?.taggedIds?.message
                            }
                            content={
                              formAction.formState.errors?.taggedIds?.message
                            }
                            position="top"
                            align="end"
                            classNameConfig={{
                              content: formAction.formState.errors?.taggedIds
                                ?.message
                                ? ''
                                : 'hidden'
                            }}>
                            <AsyncMultipleSearchWithSelect
                              isClearable={false}
                              promiseOptions={promiseTagsOptions}
                              creatable={actionTag}
                              configSelectOption={{
                                option: 'checkbox',
                                supportingText: [
                                  'name',
                                  'badge',
                                  'description'
                                ],
                                classNameOption: {
                                  multiValue: 'truncate'
                                }
                              }}
                              size="md"
                              onChange={(value) => {
                                const newValueArrWithoutCreatedOption = (
                                  value as MultiValue<ISelectOption>
                                ).filter((option) => !option.__isNew__)
                                const newestOption = (
                                  value as MultiValue<ISelectOption>
                                )?.[
                                  (value as MultiValue<ISelectOption>).length -
                                    1
                                ]
                                if (!!newestOption?.__isNew__) {
                                  triggerAddNewTag({
                                    name: newestOption.label,
                                    kind: TAG_KIND.job
                                  }).then((result) => {
                                    if (result.error) {
                                      return catchErrorFromGraphQL({
                                        error: result.error,
                                        page: pathConfiguration.jobs.create,
                                        setToast
                                      })
                                    }

                                    const newTag = result.data?.tagsCreate?.tag
                                    return onChange([
                                      ...newValueArrWithoutCreatedOption,
                                      {
                                        value: newTag.id,
                                        supportingObj: {
                                          name: newTag.name
                                        }
                                      }
                                    ])
                                  })
                                } else return onChange(value)
                              }}
                              onInputChange={(inputChange) => {
                                if ((inputChange || '').length > 0)
                                  if ((inputChange || '').length > 30) {
                                    formAction.setError('taggedIds', {
                                      type: 'invalid_input_search',
                                      message: `${t(
                                        'form:field_max_number_required',
                                        { number: 30 }
                                      )}`
                                    })
                                  } else {
                                    formAction.clearErrors()
                                  }
                              }}
                              isValidNewOption={(inputChange) =>
                                (inputChange || '').length <= 30
                              }
                              placeholder={`${t('label:placeholder:select')}`}
                              value={value}
                              destructive={
                                formAction.formState.errors &&
                                !!formAction.formState.errors.taggedIds
                              }
                              classNameOverride={{
                                loadingMessage: `${t('label:loading')}`,
                                noOptionsMessage: `${t('label:noOptions')}`
                              }}
                            />
                          </Tooltip>
                        </FormControlItem>
                      )
                    }}
                  />
                </div>

                <div className="mb-5">
                  <Controller
                    control={formAction.control}
                    name="description"
                    render={({ field: { onChange, value } }) => (
                      <FormControlItem
                        destructive={
                          formAction.formState.errors &&
                          !!formAction.formState.errors.description
                        }
                        destructiveText={
                          formAction.formState.errors &&
                          (formAction.formState.errors.description
                            ?.message as string)
                        }
                        labelRequired
                        label={`${t('job:formLabelDescription')}`}>
                        <RichEditor
                          toggleAIWriter={toggleAIWriter}
                          openAIWriter={openAIWriter}
                          extraToolbar={{
                            AIWriterJob: {
                              show:
                                isFeatureEnabled(PLAN_FEATURE_KEYS.ai_writer) &&
                                isUnLockFeature(PLAN_FEATURE_KEYS.ai_writer),
                              isReplace: !!formAction.getValues().description,
                              loadingGenerate: loadingGenerate,
                              valueAIWriter: valueAIWriter,
                              setValueAIWriter: setValueAIWriter,
                              textGenerate: textGenerate,
                              isDisabled: !formAction.getValues().title,
                              onSubmitAIWriter: () =>
                                onSubmitAIWriterJobDescription(
                                  formAction.getValues(),
                                  formAction
                                ),
                              onSubmitAddJD: () => {
                                if (richEditorRef.current) {
                                  formAction.setValue(
                                    'description',
                                    textGenerate as never
                                  )
                                  formAction.trigger(['description'])
                                  richEditorRef.current.commands.setContent(
                                    textGenerate
                                  )
                                }
                                toggleAIWriter()
                                setToast({
                                  open: true,
                                  type: 'success',
                                  title: !!formAction.getValues().description
                                    ? `${t(
                                        'notification:jobs:replace_jd_success'
                                      )}`
                                    : `${t('notification:jobs:add_jd_success')}`
                                })
                              }
                            }
                          }}
                          editorRef={(editor: CoreEditor) => {
                            richEditorRef.current = editor
                          }}
                          size="sm"
                          onChange={onChange}
                          content={value}
                          placeholder={`${t('job:formPlaceholderDescription')}`}
                          destructive={
                            formAction.formState.errors &&
                            !!formAction.formState.errors.description
                          }
                        />
                      </FormControlItem>
                    )}
                  />
                </div>

                <div className="mb-5">
                  <Controller
                    control={formAction.control}
                    name="pitch"
                    render={({ field: { onChange, value } }) => (
                      <FormControlItem
                        destructive={
                          formAction.formState.errors &&
                          !!formAction.formState.errors.pitch
                        }
                        destructiveText={
                          formAction.formState.errors &&
                          (formAction.formState.errors.pitch?.message as string)
                        }
                        label={`${t('job:formLabelPitch')}`}>
                        <RichEditor
                          size="sm"
                          limit={500}
                          onChange={onChange}
                          content={value}
                          placeholder={`${t('job:formPlaceholderPitch')}`}
                          destructive={
                            formAction.formState.errors &&
                            !!formAction.formState.errors.pitch
                          }
                        />
                      </FormControlItem>
                    )}
                  />
                </div>
              </div>
              <div className="mt-8">
                <TypographyH5 className="text-gray-900">
                  {t('job:formTitleEmploymentDetails')}
                </TypographyH5>
                <p className="mt-[4px] text-sm font-normal text-gray-600">
                  {t('job:formTitleDescriptionEmploymentDetails')}
                </p>
                <div className="mb-5 mt-3">
                  <div className="flex space-x-4">
                    <If condition={isShowSystemField('job_level')}>
                      <div className="flex-1">
                        <Controller
                          control={formAction.control}
                          name="jobLevel"
                          render={({ field: { onChange, value } }) => {
                            const filter = jobLevel.filter(
                              (item: ISelectOption) => item.value === value
                            )
                            return (
                              <FormControlItem
                                label={`${t('job:formLabelLevel')}`}>
                                <NativeSelect
                                  size="md"
                                  onChange={(newValue) => {
                                    onChange(
                                      (newValue as ISelectOption)?.value || ''
                                    )
                                  }}
                                  placeholder={`${t(
                                    'label:placeholder:select'
                                  )}`}
                                  value={filter}
                                  options={jobLevel}
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      </div>
                    </If>
                    <div className="flex-1">
                      <Controller
                        control={formAction.control}
                        name="education"
                        render={({ field: { onChange, value } }) => {
                          const filter = jobEducation.filter(
                            (item: ISelectOption) => item.value === value
                          )
                          return (
                            <FormControlItem
                              label={`${t('job:formLabelEducation')}`}>
                              <NativeSelect
                                size="md"
                                onChange={(newValue) => {
                                  onChange(
                                    (newValue as ISelectOption)?.value || ''
                                  )
                                }}
                                placeholder={`${t(
                                  'job:job_form:education_level_placeholder'
                                )}`}
                                value={filter}
                                options={jobEducation}
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div className={openAISuggestSkills ? 'mb-3' : 'mb-5'}>
                  <Controller
                    control={formAction.control}
                    name="skills"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <FormControlItem
                          helpText={
                            <span className="text-sm font-normal text-gray-600">
                              {t('common:aiSuggestsJobDescription')}
                              <TextButton
                                className="inline"
                                underline={false}
                                type="primary"
                                size="md"
                                label={t('common:aiSuggests')}
                                onClick={() => {
                                  setOpenAISuggestSkills(true)
                                  onSuggestAISkills(formAction.getValues())
                                }}
                              />
                            </span>
                          }
                          label={`${t('job:formLabelSkills')}`}
                          helpIcon={
                            isShowEquivalentsSkills ? (
                              <Tooltip
                                mode="icon"
                                align="start"
                                content={`${t('tooltip:equivalentSkills')}`}
                                classNameAsChild="ml-1">
                                <IconWrapper
                                  size={14}
                                  name="HelpCircle"
                                  className="text-gray-400"
                                />
                              </Tooltip>
                            ) : null
                          }>
                          <Tooltip
                            open={
                              !!formAction.formState.errors?.skills?.message
                            }
                            content={
                              formAction.formState.errors?.skills?.message
                            }
                            position="top"
                            align="end"
                            classNameConfig={{
                              content: formAction.formState.errors?.skills
                                ?.message
                                ? ''
                                : 'hidden'
                            }}>
                            <AsyncMultipleSearchWithSelect
                              promiseOptions={promiseSkillsOptions}
                              isClearable={false}
                              size="md"
                              showDropdownIndicator={false}
                              onInputChange={(inputChange) => {
                                if ((inputChange || '').length > 0)
                                  if ((inputChange || '').length > 30) {
                                    formAction.setError('skills', {
                                      type: 'invalid_input_search',
                                      message: `${t(
                                        'form:field_max_number_required',
                                        { number: 30 }
                                      )}`
                                    })
                                  } else {
                                    formAction.clearErrors()
                                  }
                              }}
                              isValidNewOption={(inputChange, options) =>
                                (inputChange || '').length <= 30 &&
                                (options || []).length == 0
                              }
                              callbackClearSearchData={() => {
                                formAction.clearErrors()
                              }}
                              onChange={(value) => {
                                const newValue = JSON.parse(
                                  JSON.stringify(value)
                                ) as ISelectOption[]
                                const filterValue = newValue.filter(
                                  (item: ISelectOption) =>
                                    !item.__isNew__ ||
                                    (item.__isNew__ && item.value.length <= 30)
                                )
                                const updatedAISuggestSkills =
                                  filterSuggestedSkills(
                                    aiSuggestedSkills,
                                    newValue
                                  )
                                setCurrentSkills(updatedAISuggestSkills)

                                return onChange(filterValue)
                              }}
                              creatable={allowAddNewSkill}
                              placeholder={`${t(
                                'job:job_form:skill_placeholder'
                              )}`}
                              value={value}
                              configSelectOption={{
                                option: 'checkbox',
                                supportingText: isShowEquivalentsSkills
                                  ? ['name', 'description']
                                  : ['name']
                              }}
                              destructive={
                                formAction.formState.errors &&
                                !!formAction.formState.errors?.skills
                              }
                            />
                          </Tooltip>
                        </FormControlItem>
                      )
                    }}
                  />
                </div>
                {openAISuggestSkills && (
                  <If
                    condition={!isGettingSuggestSkills}
                    fallback={
                      <IconWrapper
                        className="mb-5 animate-spin text-gray-500 dark:text-gray-400"
                        name="Loader2"
                        size={16}
                      />
                    }>
                    <div className="mb-5">
                      {currentSkills.length !== 0 ? (
                        <div className="relative">
                          <div
                            ref={skillSuggestionRef}
                            className="-mt-2 flex max-h-full flex-wrap overflow-hidden p-[1px]">
                            <SuggestionInlineChips
                              className="rounded-xl border-gray-100 bg-gray-100 hover:bg-gray-300"
                              classNameChip="font-medium bg-transparent rounded-xl border-0 hover:bg-gray-300 px-[2px] pr-[10px] pl-[8px]"
                              classNameChipText="rounded-none  text-gray-600 text-sm p-0"
                              size="md"
                              source={currentSkills.map(
                                (skill: ISelectOption) => ({
                                  label: String(skill.value),
                                  maxLength: 30,
                                  onClick: () => {
                                    const selectedSkills =
                                      formAction.getValues('skills') || []

                                    const newValue = {
                                      value: skill.value,
                                      supportingObj: { name: skill.value }
                                    } as ISelectOption
                                    const filterValue = (
                                      [newValue] as ISelectOption[]
                                    ).filter(
                                      (item) =>
                                        !item.__isNew__ ||
                                        item.value.length <= 30
                                    )

                                    formAction.setValue('skills', [
                                      ...selectedSkills,
                                      ...filterValue
                                    ] as never)

                                    const updatedAISuggestSkills =
                                      filterSuggestedSkills(aiSuggestedSkills, [
                                        ...selectedSkills,
                                        ...filterValue
                                      ])
                                    setCurrentSkills(updatedAISuggestSkills)
                                  }
                                })
                              )}
                              type="default"
                              startIconMenus="Plus"
                              startIconMenusClassName="text-gray-500"
                            />
                          </div>
                          <If condition={showMoreSkills}>
                            <div className="mx-1 mt-2 flex-row">
                              <TextButton
                                underline={false}
                                type="primary"
                                size="md"
                                label={t('button:showMore')}
                                onClick={() => {
                                  if (skillSuggestionRef.current) {
                                    setShowMoreSkills(false)
                                    skillSuggestionRef.current.style.maxHeight =
                                      'unset'
                                  }
                                  setIsShowMoreClicked(true)
                                }}
                              />
                            </div>
                          </If>
                        </div>
                      ) : (
                        <TypographyText className="whitespace-nowrap text-sm font-normal text-gray-600">
                          {t('common:noSkillSuggests')}
                        </TypographyText>
                      )}
                    </div>
                  </If>
                )}
                <div
                  className={cn(
                    'mb-5 grid gap-x-4',
                    isShowSystemField('remote_status') ? 'grid-cols-2' : ''
                  )}>
                  <div>
                    <Controller
                      control={formAction.control}
                      name="languages"
                      render={({ field: { onChange, value } }) => (
                        <FormControlItem
                          label={`${t('job:detail:summaryInfo:languages')}`}>
                          <MultipleSelect
                            windowMenuList={{
                              width: 380,
                              height: 32
                            }}
                            isClearable={false}
                            size="md"
                            creatable={false}
                            onChange={(value) => {
                              const formatValue = (
                                (value || []) as MultiValue<ISelectOption>
                              ).map((item) =>
                                !item.__isNew__
                                  ? item
                                  : {
                                      ...item,
                                      supportingObj: {
                                        name: item.label
                                      }
                                    }
                              )
                              onChange(formatValue)
                            }}
                            options={languageList}
                            showDropdownIndicator={false}
                            placeholder={`${t(
                              'job:job_form:language_placeholder'
                            )}`}
                            value={value}
                            configSelectOption={{
                              option: 'checkbox',
                              supportingText: ['name']
                            }}
                            classNameOverride={{
                              loadingMessage: `${t('label:loading')}`,
                              noOptionsMessage: `${t('label:noOptions')}`
                            }}
                          />
                        </FormControlItem>
                      )}
                    />
                  </div>
                  <If condition={isShowSystemField('remote_status')}>
                    <div className="flex-1">
                      <Controller
                        control={formAction.control}
                        name="remoteStatus"
                        render={({ field: { onChange, value } }) => {
                          const filter = jobRemoteStatus.filter(
                            (item: ISelectOption) => item.value === value
                          )
                          return (
                            <FormControlItem
                              destructive={
                                formAction.formState.errors &&
                                !!formAction.formState.errors.remoteStatus
                              }
                              destructiveText={
                                formAction.formState.errors &&
                                (formAction.formState.errors.remoteStatus
                                  ?.message as string)
                              }
                              label={`${t('job:formLabelRemoteStatus')}`}>
                              <NativeSelect
                                showDropdownIndicator={true}
                                isClearable={false}
                                size="md"
                                onChange={(newValue) =>
                                  onChange(
                                    (newValue as ISelectOption)?.value || ''
                                  )
                                }
                                placeholder={`${t(
                                  'job:formPlaceholderRemoteStatus'
                                )}`}
                                value={filter}
                                options={jobRemoteStatus}
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.remoteStatus
                                }
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                  </If>
                </div>

                <If condition={isShowSystemField('job_talent_pool_ids')}>
                  <div className="mb-5">
                    <Controller
                      control={formAction.control}
                      name="jobTalentPoolIds"
                      render={({ field: { onChange, value } }) => (
                        <FormControlItem
                          label={`${t('job:detail:summaryInfo:talent_pools')}`}>
                          <AsyncMultipleSearchWithSelect
                            promiseOptions={promiseTalentPoolsOptions}
                            isClearable={false}
                            size="md"
                            showDropdownIndicator={false}
                            onChange={onChange}
                            placeholder={`${t(
                              'job:job_form:talent_pools_placeholder'
                            )}`}
                            value={value}
                            configSelectOption={{
                              option: 'checkbox',
                              supportingText: ['name']
                            }}
                          />
                        </FormControlItem>
                      )}
                    />
                  </div>
                </If>

                <div className="mb-5">
                  <div className="flex space-x-4">
                    <div className="flex-1">
                      <Controller
                        control={formAction.control}
                        name="jobCategoryId"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem
                              label={`${t('job:formLabelCategory')}`}>
                              <AsyncSingleSearchWithSelect
                                promiseOptions={promiseJobCategoriesOptions}
                                size="md"
                                onChange={onChange}
                                placeholder={`${t(
                                  'job:job_form:job_category_placeholder'
                                )}`}
                                value={value}
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                    <If condition={isShowSystemField('employment_type')}>
                      <div className="flex-1">
                        <Controller
                          control={formAction.control}
                          name="employmentType"
                          render={({ field: { onChange, value } }) => {
                            const filter = jobEmployment.filter(
                              (item: ISelectOption) => item.value === value
                            )
                            return (
                              <FormControlItem
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.employmentType
                                }
                                destructiveText={
                                  formAction.formState.errors &&
                                  (formAction.formState.errors.employmentType
                                    ?.message as string)
                                }
                                label={`${t('job:formLabelEmployment')}`}>
                                <NativeSelect
                                  size="md"
                                  onChange={(newValue) => {
                                    onChange(
                                      (newValue as ISelectOption)?.value || ''
                                    )
                                  }}
                                  placeholder={`${t(
                                    'label:placeholder:select'
                                  )}`}
                                  value={filter}
                                  options={jobEmployment}
                                  destructive={
                                    formAction.formState.errors &&
                                    !!formAction.formState.errors.employmentType
                                  }
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      </div>
                    </If>
                  </div>
                </div>
                <If condition={isShowSystemField('salary')}>
                  <div className="mb-5">
                    <div className="flex space-x-4">
                      <div className="flex-1">
                        <Controller
                          control={formAction.control}
                          name="typeOfSalary"
                          render={({ field: { onChange, value } }) => {
                            const filter = jobTypeOfSalary.filter(
                              (item: ISelectOption) => item.value === value
                            )
                            return (
                              <FormControlItem
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.typeOfSalary
                                }
                                destructiveText={
                                  formAction.formState.errors &&
                                  (formAction.formState.errors.typeOfSalary
                                    ?.message as string)
                                }
                                label={`${t('job:formLabelSalary')}`}>
                                <NativeSelect
                                  isClearable={false}
                                  size="md"
                                  onChange={(newValue) =>
                                    onChange(
                                      (newValue as ISelectOption)?.value || ''
                                    )
                                  }
                                  placeholder={`${t(
                                    'label:placeholder:select'
                                  )}`}
                                  value={filter}
                                  options={jobTypeOfSalary}
                                  destructive={
                                    formAction.formState.errors &&
                                    !!formAction.formState.errors.typeOfSalary
                                  }
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      </div>
                      <div className="flex-1">
                        <Controller
                          control={formAction.control}
                          name="currency"
                          render={({ field: { onChange, value } }) => {
                            const filter = jobCurrency.filter(
                              (item: ISelectOption) => item.value === value
                            )
                            return (
                              <FormControlItem
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.currency
                                }
                                destructiveText={
                                  formAction.formState.errors &&
                                  (formAction.formState.errors.currency
                                    ?.message as string)
                                }
                                label={`${t('job:formLabelCurrency')}`}>
                                <NativeSelect
                                  isClearable={false}
                                  size="md"
                                  onChange={(newValue) =>
                                    onChange(
                                      (newValue as ISelectOption)?.value || ''
                                    )
                                  }
                                  placeholder={`${t(
                                    'label:placeholder:select'
                                  )}`}
                                  value={filter}
                                  options={jobCurrency}
                                  destructive={
                                    formAction.formState.errors &&
                                    !!formAction.formState.errors.currency
                                  }
                                  classNameOverride={{
                                    loadingMessage: `${t('label:loading')}`,
                                    noOptionsMessage: `${t('label:noOptions')}`
                                  }}
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      </div>
                      <div className="flex-1">
                        <Controller
                          control={formAction.control}
                          name="salaryFrom"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.salaryFrom
                                }
                                destructiveText={
                                  formAction.formState.errors &&
                                  (formAction.formState.errors.salaryFrom
                                    ?.message as string)
                                }
                                label={`${t('job:formLabelSalaryFrom')}`}>
                                <Input
                                  {...createPreventPositiveChange((value) => {
                                    onChange(value)
                                    formAction.trigger(['salaryTo'])
                                  })}
                                  inputType="number"
                                  size="md"
                                  value={value}
                                  placeholder={`${t(
                                    'job:formPlaceholderSalaryFrom'
                                  )}`}
                                  destructive={
                                    formAction.formState.errors &&
                                    !!formAction.formState.errors.salaryFrom
                                  }
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      </div>
                      <div className="flex-1">
                        <Controller
                          control={formAction.control}
                          name="salaryTo"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem
                                destructive={
                                  formAction.formState.errors &&
                                  !!formAction.formState.errors.salaryTo
                                }
                                destructiveText={
                                  formAction.formState.errors &&
                                  (formAction.formState.errors.salaryTo
                                    ?.message as string)
                                }
                                label={`${t('job:formLabelSalaryTo')}`}>
                                <Input
                                  inputType="number"
                                  size="md"
                                  {...createPreventPositiveChange((value) => {
                                    onChange(value !== '0' ? value : '')
                                    formAction.trigger(['salaryFrom'])
                                  })}
                                  value={value}
                                  placeholder={`${t(
                                    'job:formPlaceholderSalaryTo'
                                  )}`}
                                  destructive={
                                    formAction.formState.errors &&
                                    !!formAction.formState.errors.salaryTo
                                  }
                                />
                              </FormControlItem>
                            )
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </If>
              </div>

              {customFieldViewData.length > 0 && (
                <div className="grid gap-y-5">
                  <Controller
                    control={formAction.control}
                    name="customFields"
                    defaultValue={{}}
                    render={({ field: { onChange, value } }) => {
                      return (
                        <>
                          {customFieldViewData.map((customField) => (
                            <div
                              className="max-w-[776px]"
                              key={`additional-field-${customField.id}`}>
                              <CustomField
                                type={
                                  customField.type as CustomFieldComponentType['type']
                                }
                                display="default"
                                name={customField.name}
                                label={customField.label}
                                error={formAction.formState.errors.customFields}
                                value={
                                  Object.values(
                                    (value || {}) as CustomFieldFormType
                                  ).find(
                                    (item) =>
                                      String(item.id) === String(customField.id)
                                  )?.value || ''
                                }
                                onChange={(fieldValue) => {
                                  const data = customFieldViewData.reduce(
                                    (result, item) => {
                                      return {
                                        ...result,
                                        [renderKeyCustomFieldForm({
                                          fieldKind: mappingTypeToFieldKind(
                                            item.type
                                          ),
                                          id: item.id
                                        })]: {
                                          ...item,
                                          value: ''
                                        }
                                      }
                                    },
                                    {}
                                  )

                                  onChange({
                                    ...(Object.keys(value || {}).length > 0
                                      ? value // rewrite when edit job
                                      : data), // For case create job

                                    [customField.name]: {
                                      ...customField,
                                      value: fieldValue
                                    }
                                  })
                                }}
                                extraProps={{
                                  options: customField.selectOptions
                                }}
                              />
                            </div>
                          ))}
                        </>
                      )
                    }}
                  />
                </div>
              )}

              <div className="mb-5 mt-8">
                <Controller
                  control={formAction.control}
                  name="hiringProcess"
                  render={({ field: { onChange, value } }) => (
                    <JobHiringView
                      jobData={defaultValue}
                      jobApplicants={defaultValue?.applicants}
                      isEdit={isEdit}
                      onChange={(value, options) => {
                        if (options?.preventDirty) {
                          formAction.setValue('hiringProcess', value as never)
                          formAction.reset(undefined, {
                            keepValues: true,
                            keepDirty: false,
                            keepDefaultValues: false
                          })
                        } else {
                          onChange(value)
                        }
                      }}
                      value={value}
                      formState={formAction.formState}
                      jobStatus={defaultValue?.status}
                    />
                  )}
                />
              </div>
            </Container>

            <div className="sticky bottom-0 left-0 right-0 z-10 bg-white">
              <Divider />

              <div className="mx-auto max-w-[776px] py-[13px]">
                <div className="flex items-center justify-between">
                  <Button
                    htmlType="button"
                    size="md"
                    type="secondary"
                    label={`${t('button:cancel')}`}
                    isDisabled={isLoading}
                    isLoading={isLoading}
                    onClick={() => {
                      if (formAction.formState.isDirty) {
                        openAlert({
                          isPreventAutoFocusDialog: false,
                          className: 'w-[480px]',
                          title: `${t(
                            'common:modal:discard_unsaved_changes_title'
                          )}`,
                          description: `${t(
                            'common:modal:discard_unsaved_changes_description'
                          )}`,
                          actions: [
                            {
                              label: `${t('button:keepEditing')}`,
                              type: 'secondary',
                              size: 'sm'
                            },
                            {
                              label: `${t('button:discard')}`,
                              type: 'destructive',
                              size: 'sm',
                              onClick: async (e) => {
                                formAction.reset()
                                setTimeout(() => {
                                  onCancelAction && onCancelAction()
                                }, 500)
                              }
                            }
                          ]
                        })
                      } else {
                        onCancelAction && onCancelAction()
                      }
                    }}
                  />

                  <div className="flex space-x-4">
                    {!isEdit ? (
                      <Button
                        size="md"
                        isDisabled={isLoading}
                        isLoading={isLoading}
                        type="tertiary"
                        label={`${t('button:saveDraft')}`}
                        onClick={() => {
                          formAction.setValue('status', 'draft' as never)
                          return formAction.submit && formAction.submit()
                        }}
                      />
                    ) : null}

                    <If condition={createPermission || updatePermission}>
                      <Button
                        size="md"
                        isDisabled={isLoading}
                        isLoading={isLoading}
                        htmlType="submit"
                        label={
                          isEdit
                            ? `${t('button:save')}`
                            : `${t('button:publish')}`
                        }
                      />
                    </If>
                  </div>
                </div>
              </div>
            </div>

            {isCompanyKind === true ? (
              <LocationModal
                companyId={Number(formAction.getValues()?.companyId?.value)}
                openAddLocation={openLocationModal}
                onOpenChange={setOpenLocationModal}
                callback={(data) => {
                  if (data) {
                    const companyLocations =
                      data?.companiesUpdate?.company?.companyLocations || []
                    const sorted = companyLocations.sort(
                      (a: { id: string }, b: { id: string }) =>
                        Number(b.id) - Number(a.id)
                    )?.[0]

                    formAction.setValue('locationIds', [
                      ...(formAction.getValues()?.locationIds || []),
                      {
                        value: sorted?.id,
                        supportingObj: {
                          name: formatAddressLocation({
                            location: {
                              address: sorted.address,
                              city: sorted.city,
                              state: sorted.state,
                              country: sorted.country
                            }
                          })
                        }
                      }
                    ] as never)
                  }
                }}
                onSubmit={onUpdateCompanyLocation}
              />
            ) : null}
          </>
        )
      }}
    </DynamicImportForm>
  )
}

export default JobForm
