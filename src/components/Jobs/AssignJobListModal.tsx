import { InfiniteData } from '@tanstack/react-query'
import { FC, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import InfiniteScroll from '~/components/List/InfiniteScroll'
import configuration from '~/configuration'
import { Button } from '~/core/ui/Button'
import { DebouncedInput } from '~/core/ui/DebouncedInput'
import Empty from '~/core/ui/Empty'
import { ScrollArea } from '~/core/ui/ScrollArea'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { IApplicableJobs } from '~/lib/features/calendar/types'

const AssignJobListModal: FC<{
  setOpen: (param: boolean) => void
  filter: {
    page: number
    limit: number
    search: string
  }
  setFilter: (param: { page: number; limit: number; search: string }) => void
  reload?: (applicantId: string) => void
  clickedCallback?: (jobId?: string) => void
  data: InfiniteData<any> | undefined
  fetchNextPage: () => Promise<any>
  hasNextPage?: boolean
  onAssign: (val: string) => void
}> = ({
  filter,
  setFilter,
  clickedCallback,
  data,
  fetchNextPage,
  hasNextPage,
  onAssign
}) => {
  const { t } = useTranslation()
  const scrollingWrapper = useRef<HTMLDivElement>(null)

  const [totalCount, setTotalCount] = useState<number>(0)
  useEffect(() => {
    const count = data?.pages?.[0]?.jobsList?.collection?.length || 0
    setTotalCount(count)
  }, [data])

  return (
    <>
      <DebouncedInput
        autoFocus
        placeholder={`${t('assignJobs:searchByJobTitle')}`}
        forceOnChangeCallBack={filter}
        value={filter.search}
        onChange={(value) =>
          setFilter({
            ...filter,
            search: String(value)
          })
        }
        onClear={() =>
          setFilter({
            ...filter,
            search: ''
          })
        }
      />

      <ScrollArea
        scrollRef={scrollingWrapper}
        className="-mx-5 h-auto  space-y-3 px-5"
        viewportClassName={
          totalCount === 0
            ? '[&>div:first-child]:h-full h-full '
            : 'max-h-[484px]'
        }>
        {totalCount === 0 ? (
          <div className="mt-3 flex  h-full items-center justify-center">
            <Empty
              onClick={() => {
                if (!filter.search) {
                  window.open(configuration.path.jobs.create)
                }
              }}
              type={filter.search ? 'empty-search' : 'empty-data'}
              title={
                filter.search
                  ? `${t('assignJobs:emptySearch:title')}`
                  : `${t('assignJobs:emptyData:title')}`
              }
              description={
                filter.search
                  ? `${t('assignJobs:emptySearch:description')}`
                  : `${t('assignJobs:emptyData:description')}`
              }
              buttonTitle={filter.search ? '' : `${t('assignJobs:newJob')}`}
            />
          </div>
        ) : (
          <InfiniteScroll
            wrapperRef={scrollingWrapper}
            loadData={fetchNextPage}
            haveNext={!!hasNextPage}
            className={
              totalCount === 0
                ? 'flex h-full  max-h-[496px] items-center justify-center'
                : ''
            }>
            {data?.pages.map((page) =>
              page.jobsList.collection.map((item: IApplicableJobs) => {
                return (
                  <div
                    key={item.id}
                    className="border-b border-solid border-gray-100 py-3 pr-1 last:border-none last:pb-0">
                    <div className="flex items-center justify-between space-x-6">
                      <div className="flex-1 space-y-1">
                        <div className="flex">
                          <Tooltip content={item.title}>
                            <TypographyText className="line-clamp-1 cursor-pointer text-sm font-medium text-gray-900 hover:underline">
                              <a
                                href={`${configuration.path.jobs.detail(
                                  Number(item.id)
                                )}?tabs=details`}
                                target="_blank">
                                {item.title}
                              </a>
                            </TypographyText>
                          </Tooltip>
                        </div>
                        <div className="flex items-center">
                          <div className="line-clamp-1 flex max-w-[50%] flex-none">
                            {item.department?.name ? (
                              <TypographyText className="line-clamp-1 break-all text-sm text-gray-500">
                                {item?.department?.name}
                              </TypographyText>
                            ) : null}
                            {item.company?.permittedFields?.name?.value ? (
                              <TypographyText className="line-clamp-1 break-all text-sm text-gray-500">
                                {item?.company?.permittedFields?.name?.value}
                              </TypographyText>
                            ) : null}
                          </div>
                          <div className="flex items-center">
                            {(item.department?.name ||
                              item.company?.permittedFields?.name?.value) &&
                              item.jobLocations.length > 0 && (
                                <span className="mx-2 h-0.5 w-0.5 flex-none rounded bg-gray-400" />
                              )}
                            <div className="line-clamp-1 break-all">
                              {item.jobLocations.length > 0 &&
                                (item.jobLocations.length === 1 ? (
                                  <TypographyText className="text-sm text-gray-500">
                                    {[
                                      item.jobLocations[0].state,
                                      item.jobLocations[0].country
                                    ]
                                      .filter((item) => !!item)
                                      .join(', ')}
                                  </TypographyText>
                                ) : item.jobLocations.length > 1 ? (
                                  <TypographyText className="text-sm text-gray-500">
                                    {t('assignJobs:countLocations', {
                                      count: item.jobLocations.length
                                    })}
                                  </TypographyText>
                                ) : null)}
                            </div>
                          </div>
                        </div>
                      </div>
                      <Button
                        size="xs"
                        type="tertiary"
                        label={`${t('assignJobs:assign')}`}
                        iconMenus="Plus"
                        onClick={() => {
                          if (clickedCallback) {
                            clickedCallback(item.id)
                          } else {
                            onAssign(item.id)
                          }
                        }}
                      />
                    </div>
                  </div>
                )
              })
            )}
          </InfiniteScroll>
        )}
      </ScrollArea>
    </>
  )
}

export default AssignJobListModal
