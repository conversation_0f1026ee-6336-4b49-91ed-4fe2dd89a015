import Link from 'next/link'
import { useRouter } from 'next/router'
import { ReactNode, useMemo, useRef, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import pathConfiguration from 'src/configuration/path'
import useSwitchRole from 'src/hooks/authorization/use-switch-role'
import useStaticData from 'src/hooks/data/use-static-data'
import ChangeJobStageWithModalView from '~/components/ChangeJobStageWithModal/ChangeJobStageWithModalView'
import ShareJobModal from '~/components/Jobs/ShareJobModal'
import configuration from '~/configuration'
import { PUBLIC_APP_URL } from '~/core/constants/env'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import { AvatarGroup } from '~/core/ui/AvatarGroup'
import { Badge } from '~/core/ui/Badge'
import { But<PERSON> } from '~/core/ui/Button'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { IDotColorProps } from '~/core/ui/Dot'
import { DropdownMenu, DropdownMenuMenuItem } from '~/core/ui/DropdownMenu'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { ISelectOption } from '~/core/ui/Select'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn, uuidV4 } from '~/core/ui/utils'
import { formatAddressLocation } from '~/core/utilities/common'
import { defaultFormatDate } from '~/core/utilities/format-date'
import {
  adminAndMemberCanAction,
  adminCanAction
} from '~/core/utilities/permission'
import CandidatesTabContent from '~/features/jobs/[id]/tabs/candidates/CandidatesTabContent'
import useCareerSettingStore from '~/lib/features/careers/[id]/store'
import { combineDomainCareerPage } from '~/lib/features/careers/[id]/utilities'
import QueryTenantJobDetailMini from '~/lib/features/jobs/graphql/query-job-detail-mini'
import useShareJobLogic from '~/lib/features/jobs/hooks/use-share-job-logic'
import { IJobManagementItemQuery } from '~/lib/features/jobs/types'

import BookMarkedSVG from '~/components/CareerHub/[id]/BookMarkedSVG'
import BookMarkSVG from '~/components/CareerHub/[id]/BookMarkSVG'
import { Checkbox } from '~/core/ui/Checkbox'
import QueryAgencyTenantJobDetailMini from '~/lib/features/jobs/graphql/agency-query-job-detail-mini'
import { MutationSavedJobHF } from '~/lib/features/jobs/graphql/save-job-mutation'
import {
  authorizingJobActionMenu,
  authorizingJobStageChange
} from '~/lib/features/jobs/utilities/common'
import {
  JOB_ACTION,
  JOB_APPLICANT_STATUS,
  JOB_DOT_STATUS,
  JOB_STATUS_ENUM
} from '~/lib/features/jobs/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import useAsyncAction from '~/lib/hooks/use-async-action'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import { useUserCheckKindOf } from '~/lib/hooks/use-user-check-kind'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'
import { InfiniteData, QueryObserverResult } from '@tanstack/react-query'
import { ClipboardIdIcon } from '~/core/ui/FillIcons'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'

interface AsyncActionType<F> {
  loading: boolean
  executed: F
}

export type IJobCard = IJobManagementItemQuery

const Show = (props: { if: boolean; children: ReactNode }) =>
  props.if ? <>{props.children}</> : <></>

const JobCard = ({
  isCompanyKind,
  job,
  deleteAction,
  updateAction,
  callback,
  enablingReferral,
  jobIdJobPipeline,
  setJobIdJobPipeline
}: {
  isCompanyKind: boolean
  enablingReferral: boolean
  job: IJobCard
  deleteAction: AsyncActionType<(id: number) => Promise<any>>
  updateAction: AsyncActionType<(id: number, status: string) => Promise<any>>
  callback?: () => Promise<QueryObserverResult<InfiniteData<any>, {}>>
  jobIdJobPipeline?: string
  setJobIdJobPipeline: (val?: string) => void
}) => {
  const router = useRouter()
  const { careerPageSetting } = useCareerSettingStore()
  const [changeStatus, changingStatus] = useAsyncAction(
    updateAction.executed,
    []
  )
  const { userIsAsClient } = useUserCheckKindOf()
  const [openShareJobModal, setOpenShareJobModal] = useState<boolean>(false)
  const { isFeatureFlagOn } = useSubscriptionPlan()
  const {
    user,
    currentRole,
    bulkValues,
    setBulkValues,
    setBulkSelectedAll,
    refetchMyList,
    refetchMyDelete
  } = useBoundStore()
  const { setToast } = useToastStore()
  const { t, i18n } = useTranslation()
  // const [openJobPipeLine, setOpenJobPipeline] = useState(false)
  const openJobPipeLine = useMemo(
    () => jobIdJobPipeline === job.id,
    [jobIdJobPipeline, job]
  )
  const infoRef = useRef<HTMLDivElement | null>(null)
  const calcMaxWidthInfo =
    Number(infoRef.current?.clientWidth) > 960 ? 'max-w-[168px]' : ''
  const switchRole = useSwitchRole()
  const { actionJob } = usePermissionJob()
  const isBookMarked =
    (job?.listSavedJobMemberIds || []).filter(
      (id) => Number(id) === Number(user.id)
    ).length > 0
  const { trigger: bookmarkAPI, isLoading: bookMarkLoading } =
    useSubmitCommon(MutationSavedJobHF)
  const { trigger: triggerFetchDetail, data } = useQueryGraphQL({
    query: isCompanyKind
      ? QueryAgencyTenantJobDetailMini
      : QueryTenantJobDetailMini,
    variables: {
      id: Number(job.id),
      status: JOB_APPLICANT_STATUS.inprocessing
    },
    shouldPause: true
  })
  const stageTypes = useStaticData({
    keyName: 'agency_stageTypes',
    locale: i18n.language
  })

  const jobStagesWithCondition = useMemo(() => {
    let initDataStages = job?.jobStages || []
    if (isCompanyKind && userIsAsClient()) {
      return initDataStages.filter((i) => i?.clientShared)
    }
    return initDataStages
  }, [job?.jobStages])
  const { valueShareJobWithCondition } = useShareJobLogic()
  const checkConditionShareJob = useMemo(() => {
    return valueShareJobWithCondition({
      enablingReferral: enablingReferral || false,
      enablingCareerSite:
        user.currentTenant?.careerSiteSettings?.enablingCareerSiteSetting ||
        false,
      enableJobReferral: job.jobReferable || false,
      jobStatus: job?.status || ''
    })
  }, [enablingReferral, job, user, valueShareJobWithCondition])

  const onConfirmChangeStatus = (value: ISelectOption) => {
    let defaultMsg = t('common:modal:change_job_status_description', {
      title: job.title,
      name: value.supportingObj?.name
    })
    let description = `${defaultMsg} ${
      JOB_STATUS_ENUM.archived === value.value
        ? `${t('common:modal:change_job_status_description_1')}`
        : `${t('common:modal:change_job_status_description_2')}`
    }`
    return openAlert({
      isPreventAutoFocusDialog: false,
      className: 'w-[480px]',
      title: `${t('common:modal:change_job_status_title')}`,
      description,
      actions: [
        {
          label: `${t('button:cancel')}`,
          type: 'secondary',
          size: 'sm'
        },
        {
          isCallAPI: true,
          label: `${t('button:changeStatusAnyway')}`,
          type: 'destructive',
          size: 'sm',
          onClick: async () =>
            await changeStatus(parseInt(job.id), value?.value as string).then(
              () => {
                setToast({
                  open: true,
                  type: 'success',
                  title: t('notification:jobs:jobCard:changesSaved') || ''
                })
              }
            )
        }
      ]
    })
  }

  const listActions = [
    checkConditionShareJob?.jobList?.viewJob && {
      actionID: JOB_ACTION.VIEW,
      label: t('label:optionJobAction:viewJob') || '',
      icon: 'ExternalLink',
      onClick: () => {
        if (!!careerPageSetting) {
          router.push(
            `${combineDomainCareerPage(
              careerPageSetting
            )}${configuration.path.jobs.careerJobView(
              job.tenant.slug,
              job.slug
            )}`
          )
        }
      }
    },
    {
      actionID: JOB_ACTION.EDIT,
      label: t('label:optionJobAction:editJob') || '',
      icon: 'Edit3',
      onClick: () => {
        router.push(configuration.path.jobs.edit(parseInt(job.id)))
      }
    },
    checkConditionShareJob?.jobList?.moreShare && {
      actionID: JOB_ACTION.SHARE_JOB,
      label: t('label:optionJobAction:shareJob') || '',
      icon: 'Share2',
      onClick: () => {
        setOpenShareJobModal(true)
      }
    },
    {
      actionID: JOB_ACTION.DUPLICATE_JOB,
      label: t('label:optionJobAction:duplicate') || '',
      icon: 'Files',
      onClick: () => {
        router.push(configuration.path.jobs.duplicateJob(job.id))
      }
    },
    adminCanAction(currentRole?.code) ||
    user.ownTenant ||
    job.status === 'draft'
      ? {
          actionID: JOB_ACTION.DELETE,
          label: t('label:optionJobAction:deleteJob:title') || '',
          icon: 'Trash2',
          color: 'red',
          onClick: () => {
            openAlert({
              className: 'w-[480px]',
              title: t('label:optionJobAction:deleteJob:title') || '',
              description: job.title ? (
                <Trans
                  i18nKey="label:optionJobAction:deleteJob:descriptionDeleted"
                  values={{
                    jobTitle: job.title
                  }}>
                  <span className="font-medium text-gray-900" />
                </Trans>
              ) : (
                ''
              ),
              actions: [
                {
                  label: t('button:cancel') || '',
                  type: 'secondary',
                  size: 'sm'
                },
                {
                  isCallAPI: true,
                  label: t('button:delete') || '',
                  type: 'destructive',
                  size: 'sm',
                  onClick: async () => {
                    await deleteAction.executed(parseInt(job.id)).then(() => {
                      setToast({
                        open: true,
                        type: 'success',
                        title: t('notification:jobs:jobCard:deleteJob') || ''
                      })
                    })
                  }
                }
              ]
            })
          }
        }
      : undefined
  ]

  const onCheckedChange = (e: { target: { checked: boolean } }) => {
    const { checked } = e.target
    let newList = [...(bulkValues || [])]
    if (checked) {
      newList = [...newList, job.id]
    } else {
      newList = newList.filter((i) => i !== job.id)
    }
    setBulkValues(newList)
    setBulkSelectedAll(false)
  }

  return (
    <div className="group relative w-full">
      <If condition={adminAndMemberCanAction(currentRole?.code)}>
        <Checkbox
          className={`absolute left-[2px] group-hover:flex ${
            bulkValues?.includes(job.id) ? 'flex' : 'hidden'
          }`}
          isChecked={bulkValues?.includes(job.id)}
          onCheckedChange={onCheckedChange}
          size="sm"
        />
      </If>
      <Link
        href={
          job.status === 'draft'
            ? configuration.path.jobs.edit(parseInt(job.id))
            : configuration.path.jobs.detail(parseInt(job.id))
        }
        className={cn(
          `block border-[1px] border-gray-100 bg-white`,
          openJobPipeLine ? 'rounded-t-lg shadow-md' : 'rounded-lg'
        )}>
        <div className="flex items-center pb-3 pl-4 pr-4 pt-3.5">
          <div className="mr-4 flex-1">
            <div
              className="flex items-center"
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
              }}>
              <Tooltip content={job?.title}>
                <div
                  className="line-clamp-1 text-base font-medium text-gray-900 hover:underline"
                  onClick={(e) => {
                    e.preventDefault()
                    e.stopPropagation()
                    window.open(
                      job.status === 'draft'
                        ? configuration.path.jobs.edit(parseInt(job.id))
                        : configuration.path.jobs.detail(parseInt(job.id)),
                      '_blank'
                    )
                  }}>
                  {job.title}
                </div>
              </Tooltip>
              {(job?.statistic?.newApplicantCount || 0) > 0 && (
                <div className="ml-2">
                  <Badge color="green" size="md" radius="circular">
                    {job?.statistic?.newApplicantCount} {t('label:new')}
                  </Badge>
                </div>
              )}
            </div>

            <div className="mt-1 flex min-h-[20px] items-center space-x-1.5">
              {isCompanyKind === true ? (
                <>
                  <Show if={!!job?.permittedFields?.publicId?.value}>
                    <div className="flex min-w-[50px] items-center break-all">
                      <div className="mr-1.5 flex-none">
                        <ClipboardIdIcon />
                      </div>
                      <Tooltip
                        content={`ID: ${job?.permittedFields?.publicId?.value}`}>
                        <TypographyText className="line-clamp-1 text-sm text-gray-600">
                          {job?.permittedFields?.publicId?.value}
                        </TypographyText>
                      </Tooltip>
                    </div>
                    <div className="ml-2 mr-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
                  </Show>
                  <div className="flex items-center space-x-1.5" ref={infoRef}>
                    <Show
                      if={
                        !!job?.company?.permittedFields?.name?.value ||
                        !!user?.currentTenant?.name
                      }>
                      <IconWrapper
                        size={12}
                        name="Building"
                        className="flex-none text-gray-600"
                      />

                      {!!job?.company?.permittedFields?.name?.value ? (
                        <Link
                          href={configuration.path.agency.companyDetail(
                            Number(job?.company?.id)
                          )}>
                          <Tooltip
                            content={
                              job?.company?.permittedFields?.name?.value
                            }>
                            <TypographyText
                              className={cn(
                                'line-clamp-1 text-sm font-medium text-gray-800 hover:underline',
                                calcMaxWidthInfo
                              )}>
                              {job?.company?.permittedFields?.name?.value}
                            </TypographyText>
                          </Tooltip>
                        </Link>
                      ) : (
                        <Tooltip content={user?.currentTenant?.name}>
                          <TypographyText
                            className={cn(
                              'line-clamp-1 text-sm font-medium text-gray-800 hover:underline',
                              calcMaxWidthInfo
                            )}>
                            {user?.currentTenant?.name}
                          </TypographyText>
                        </Tooltip>
                      )}
                    </Show>
                    <Show
                      if={
                        job.jobLocations &&
                        job.jobLocations.length > 0 &&
                        !!job?.jobLocations?.[0]?.country
                      }>
                      <IconWrapper
                        size={12}
                        name="MapPin"
                        className="flex-none text-gray-600"
                      />
                      {job.jobLocations?.length ? (
                        <>
                          {job.jobLocations?.length > 1 ? (
                            <Tooltip
                              align="start"
                              content={
                                <>
                                  {job.jobLocations.map(
                                    (item, index: number) => (
                                      <div key={index}>
                                        {formatAddressLocation({
                                          location: {
                                            state: item.state,
                                            country: item.country
                                          }
                                        })}
                                      </div>
                                    )
                                  )}
                                </>
                              }>
                              <TypographyText
                                className={cn(
                                  'line-clamp-1 text-sm text-gray-600',
                                  calcMaxWidthInfo
                                )}>
                                {t('job:jobCard:countLocation', {
                                  countLocation: job.jobLocations?.length
                                })}
                              </TypographyText>
                            </Tooltip>
                          ) : (
                            <Tooltip
                              content={formatAddressLocation({
                                location: {
                                  state: job.jobLocations?.[0]?.state,
                                  country: job.jobLocations?.[0]?.country
                                }
                              })}>
                              <TypographyText
                                className={cn(
                                  'line-clamp-1 text-sm text-gray-600',
                                  calcMaxWidthInfo
                                )}>
                                {formatAddressLocation({
                                  location: {
                                    state: job.jobLocations?.[0]?.state,
                                    country: job.jobLocations?.[0]?.country
                                  }
                                })}
                              </TypographyText>
                            </Tooltip>
                          )}
                        </>
                      ) : null}
                      <div className="ml-2 mr-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
                    </Show>
                    <Show if={!!job.headcount}>
                      <IconWrapper
                        size={12}
                        name="Users"
                        className="flex-none text-gray-600"
                      />
                      <Tooltip content={t('tooltip:headcount')}>
                        <TypographyText className="line-clamp-1 text-sm text-gray-700">
                          {job.headcount}
                        </TypographyText>
                      </Tooltip>
                    </Show>
                    <Show if={(job?.tags?.length || 0) > 0}>
                      <div className="ml-2 mr-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
                      <IconWrapper
                        size={12}
                        name="Tag"
                        className="flex-none text-gray-600"
                      />
                      <Tooltip
                        content={job?.tags
                          ?.map((item) => item.name)
                          .join(', ')}>
                        <TypographyText
                          className={cn(
                            'line-clamp-1 break-all text-sm text-gray-700',
                            calcMaxWidthInfo
                          )}>
                          {job?.tags?.map((item) => item.name).join(', ')}
                        </TypographyText>
                      </Tooltip>
                    </Show>
                  </div>
                </>
              ) : (
                <>
                  <Show if={!!job?.permittedFields?.publicId?.value}>
                    <div className="flex min-w-[50px] items-center break-all">
                      <div className="mr-1.5 flex-none">
                        <ClipboardIdIcon />
                      </div>
                      <Tooltip
                        content={`ID: ${job?.permittedFields?.publicId?.value}`}>
                        <TypographyText className="line-clamp-1 text-sm text-gray-600">
                          {job?.permittedFields?.publicId?.value}
                        </TypographyText>
                      </Tooltip>
                    </div>
                    <div className="ml-2 mr-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
                  </Show>
                  <div className="flex items-center space-x-1.5" ref={infoRef}>
                    <Show
                      if={
                        (!!job.permittedFields?.company?.value?.name ||
                          !!user?.currentTenant?.name) &&
                        !!isFeatureFlagOn(PLAN_FEATURE_KEYS['company'])
                      }>
                      <IconWrapper
                        size={12}
                        name="Building"
                        className="flex-none text-gray-600"
                      />
                      {!!job?.permittedFields?.company?.value?.name ? (
                        <Link
                          href={configuration.path.agency.companyDetail(
                            Number(job?.permittedFields?.['company']?.value?.id)
                          )}>
                          <Tooltip
                            content={
                              job?.permittedFields?.company?.value?.name
                            }>
                            <TypographyText
                              className={cn(
                                'line-clamp-1 text-sm font-medium text-gray-800 hover:underline',
                                calcMaxWidthInfo
                              )}>
                              {job?.permittedFields?.company?.value?.name}
                            </TypographyText>
                          </Tooltip>
                        </Link>
                      ) : (
                        <Tooltip content={user?.currentTenant?.name}>
                          <TypographyText
                            className={cn(
                              'line-clamp-1 text-sm font-medium text-gray-800 hover:underline',
                              calcMaxWidthInfo
                            )}>
                            {user?.currentTenant?.name}
                          </TypographyText>
                        </Tooltip>
                      )}
                    </Show>
                    <Show
                      if={
                        job.jobLocations &&
                        job.jobLocations.length > 0 &&
                        !!job?.jobLocations?.[0]?.country
                      }>
                      <div className="flex items-center space-x-2 break-all">
                        <IconWrapper
                          size={12}
                          name="MapPin"
                          className="flex-none text-gray-600"
                        />

                        {job.jobLocations?.length ? (
                          <>
                            {job.jobLocations?.length > 1 ? (
                              <Tooltip
                                align="start"
                                content={
                                  <>
                                    {job.jobLocations.map(
                                      (item, index: number) => (
                                        <div key={index}>
                                          {formatAddressLocation({
                                            location: {
                                              state: item.state,
                                              country: item.country
                                            }
                                          })}
                                        </div>
                                      )
                                    )}
                                  </>
                                }>
                                <TypographyText
                                  className={cn(
                                    'line-clamp-1 text-sm text-gray-700',
                                    calcMaxWidthInfo
                                  )}>
                                  {t('job:jobCard:countLocation', {
                                    countLocation: job.jobLocations?.length
                                  })}
                                </TypographyText>
                              </Tooltip>
                            ) : (
                              <Tooltip
                                content={formatAddressLocation({
                                  location: {
                                    state: job.jobLocations?.[0]?.state,
                                    country: job.jobLocations?.[0]?.country
                                  }
                                })}>
                                <TypographyText
                                  className={cn(
                                    'line-clamp-1 text-sm text-gray-700',
                                    calcMaxWidthInfo
                                  )}>
                                  {formatAddressLocation({
                                    location: {
                                      state: job.jobLocations?.[0]?.state,
                                      country: job.jobLocations?.[0]?.country
                                    }
                                  })}
                                </TypographyText>
                              </Tooltip>
                            )}
                          </>
                        ) : null}
                      </div>
                      <div className="ml-2 mr-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
                    </Show>
                    <Show if={!!job.department?.name}>
                      <div className="flex items-center break-all">
                        <IconWrapper
                          size={12}
                          name="Network"
                          className="mr-2 flex-none text-gray-600"
                        />
                        <Tooltip content={job.department?.name}>
                          <TypographyText
                            className={cn(
                              'line-clamp-1 text-sm text-gray-700',
                              calcMaxWidthInfo
                            )}>
                            {job.department?.name}
                          </TypographyText>
                        </Tooltip>
                      </div>
                      <div className="ml-2 mr-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
                    </Show>
                    <Show if={!!job.headcount}>
                      <div className="flex items-center">
                        <IconWrapper
                          size={12}
                          name="Users"
                          className="flex-none text-gray-600"
                        />
                        <TypographyText className="ml-1 line-clamp-1 text-sm text-gray-700">
                          {job.headcount}
                        </TypographyText>
                      </div>
                    </Show>

                    <Show if={(job?.tags?.length || 0) > 0}>
                      <div className="ml-2 mr-2 h-[2px] w-[2px] flex-none rounded-full bg-gray-400" />
                      <IconWrapper
                        size={12}
                        name="Tag"
                        className="flex-none text-gray-600"
                      />
                      <Tooltip
                        content={job?.tags
                          ?.map((item) => item.name)
                          .join(', ')}>
                        <TypographyText
                          className={cn(
                            'line-clamp-1 text-sm text-gray-700',
                            calcMaxWidthInfo
                          )}>
                          {job?.tags
                            ?.map((item) => item.name)
                            .slice(0, 2)
                            .join(', ')}
                          {(job?.tags?.length || 0) > 2 &&
                            `, + ${(job?.tags?.length || 0) - 2}`}
                        </TypographyText>
                      </Tooltip>
                    </Show>
                  </div>
                </>
              )}
            </div>
          </div>

          <div>
            <div
              onClick={(e) => e.preventDefault()}
              className="flex items-center justify-end">
              <ComboboxSelect
                closeOnSelect
                dropdownMenuClassName="!w-[356px] right-0"
                hideDropdownIcon={!actionJob.update}
                menuOptionAlign="end"
                menuOptionSide="bottom"
                value={
                  {
                    value: job.status,
                    dot: JOB_DOT_STATUS(job.status || 'gray') as IDotColorProps,
                    supportingObj: {
                      name: {
                        internal: t('label:job_status:internal'),
                        publish: t('label:job_status:publish'),
                        archived: t('label:job_status:archived'),
                        draft: t('label:job_status:draft')
                      }[job.status]
                    }
                  } as ISelectOption
                }
                size="sm"
                isClearable={false}
                isSearchable={false}
                isLoading={changingStatus}
                isDisabled={!actionJob.update}
                onChange={(option) => {
                  const singleValue = option as ISelectOption
                  if (singleValue?.value !== job?.status)
                    if (job?.status === JOB_STATUS_ENUM.publish) {
                      onConfirmChangeStatus(singleValue)
                    } else {
                      changeStatus(
                        parseInt(job.id),
                        singleValue?.value as string
                      ).then(() => {
                        setToast({
                          open: true,
                          type: 'success',
                          title:
                            t('notification:jobs:jobCard:changesSaved') || ''
                        })
                      })
                    }
                }}
                options={authorizingJobStageChange(job.status, [
                  {
                    value: JOB_STATUS_ENUM.internal,
                    supportingObj: {
                      name: t('label:job_status:internal'),
                      description: t('label:optionJobStatus:internal') || ''
                    }
                  },
                  {
                    value: JOB_STATUS_ENUM.publish,
                    supportingObj: {
                      name: t('label:job_status:publish'),
                      description: t('label:optionJobStatus:publish') || ''
                    }
                  },
                  {
                    value: JOB_STATUS_ENUM.archived,
                    supportingObj: {
                      name: t('label:job_status:archived'),
                      description: t('label:optionJobStatus:archived') || ''
                    }
                  }
                ])}
              />

              <div className="ml-2">
                <Tooltip
                  content={
                    openJobPipeLine
                      ? t('tooltip:collapse_view')
                      : t('tooltip:expand_view')
                  }>
                  <Button
                    type="secondary"
                    size="xs"
                    iconMenus={openJobPipeLine ? 'Minimize2' : 'Maximize2'}
                    onClick={(e) => {
                      e.preventDefault()
                      setJobIdJobPipeline(
                        jobIdJobPipeline !== job.id ? job.id : undefined
                      )
                      if (!openJobPipeLine) {
                        triggerFetchDetail()
                      }
                    }}
                  />
                </Tooltip>
              </div>
              {[JOB_STATUS_ENUM.publish, JOB_STATUS_ENUM.internal].includes(
                job.status
              ) ? (
                <div className="ml-2">
                  <Tooltip
                    content={
                      checkConditionShareJob?.jobList?.shareIcon
                        ? t('label:optionJobAction:shareJob')
                        : t('label:optionJobAction:cannotShareJob')
                    }>
                    <Button
                      isDisabled={!checkConditionShareJob?.jobList?.shareIcon}
                      type="secondary"
                      size="xs"
                      iconMenus="Share2"
                      onClick={(e) => setOpenShareJobModal(true)}
                    />
                  </Tooltip>
                </div>
              ) : null}
              {!userIsAsClient() && (
                <div className="ml-2">
                  <Button
                    type="secondary"
                    size="xs"
                    onClick={() => {
                      bookmarkAPI({
                        id: Number(job.id),
                        saveJob: isBookMarked ? false : true
                      }).then((result) => {
                        callback &&
                          callback().then(() => {
                            setToast({
                              open: true,
                              type: 'success',
                              title: !isBookMarked
                                ? t('notification:jobs:jobCard:jobSaved')
                                : t('notification:jobs:jobCard:unJobSaved')
                            })
                          })
                      })
                    }}
                    className="cursor-pointer">
                    <div className="relative flex items-center justify-center">
                      {isBookMarked ? (
                        <BookMarkedSVG className="h-3 w-3" />
                      ) : (
                        <BookMarkSVG className="h-3 w-3" />
                      )}
                    </div>
                  </Button>
                </div>
              )}
              {switchRole({
                default: (
                  <div className="ml-2">
                    <DropdownMenu
                      side="bottom"
                      align="end"
                      menuClassName="w-auto"
                      menu={authorizingJobActionMenu(
                        job.status,
                        listActions.filter(
                          (item) => item
                        ) as (DropdownMenuMenuItem & {
                          actionID: JOB_ACTION
                        })[]
                      )}
                      trigger={(openDropdown) => (
                        <Button
                          type="secondary"
                          showFocusRing={openDropdown}
                          size="xs"
                          iconMenus="MoreHorizontal"
                        />
                      )}></DropdownMenu>
                  </div>
                ),
                limited_member:
                  job.status !== JOB_STATUS_ENUM.internal ? (
                    <div className="ml-2">
                      <Button
                        onClick={() => {
                          if (!!careerPageSetting) {
                            router.push(
                              `${combineDomainCareerPage(
                                careerPageSetting
                              )}${configuration.path.jobs.careerJobView(
                                job.tenant.slug,
                                job.slug
                              )}`
                            )
                          }
                        }}
                        icon="trailing"
                        iconMenus="ExternalLink"
                        label={t('button:viewJob') || ''}
                        type="secondary"
                        size="xs"
                        className="whitespace-pre"
                      />
                    </div>
                  ) : (
                    <div />
                  )
              })}
            </div>

            <div className="mt-2 flex items-center justify-end">
              <div className="flex items-center">
                {job?.createdAt ? (
                  <Tooltip content={t('job:jobCard:createdDate')}>
                    <TypographyText className="text-xs text-gray-600">
                      {defaultFormatDate(new Date(job.createdAt))}
                    </TypographyText>
                  </Tooltip>
                ) : null}
                <Show if={!!job.statistic?.viewCount}>
                  <div className="ml-2 mr-2 h-[2px] w-[2px] rounded-full bg-gray-400" />
                  <TypographyText className="text-xs text-gray-600">
                    {t('job:jobCard:countViews', {
                      count: job.statistic?.viewCount
                    })}
                  </TypographyText>
                </Show>
              </div>
              <If condition={(job?.jobRecruiters || []).length > 0}>
                <div className="ml-2 mr-2 h-[2px] w-[2px] rounded-full bg-gray-400" />
              </If>
              <div>
                <AvatarGroup
                  size="xs"
                  tooltipAlignAvatarCount="end"
                  source={(job?.jobRecruiters || []).map((item) => ({
                    id: parseInt(item.id),
                    alt: item.user?.fullName || item.user?.email,
                    tooltip: item.user?.fullName || item.user?.email,
                    src: item.user?.avatarVariants?.thumb?.url,
                    defaultColour: item.user?.defaultColour
                  }))}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="border-b border-gray-100"></div>
        <div
          className="flex cursor-pointer justify-between pb-4 pt-3"
          onClick={(e) => {
            e.preventDefault()
            setJobIdJobPipeline(
              jobIdJobPipeline !== job.id ? job.id : undefined
            )
            if (!openJobPipeLine) {
              triggerFetchDetail()
            }
          }}>
          {jobStagesWithCondition.map((item, index) => {
            const countJobStage =
              Array.isArray(job?.statistic?.applicantsCount) &&
              job?.statistic?.applicantsCount?.find(
                (fi) => fi.job_stage_id === Number(item.id)
              )?.count
            const countColumn = job?.jobStages?.length

            return (
              <div
                key={index}
                style={{
                  width: `calc(100% * (1 / ${countColumn}))`
                }}
                className="flex flex-col items-center justify-center border-r border-gray-100 px-1.5 last:border-r-0">
                <Tooltip content={t('job:jobCard:view_detail')}>
                  <TypographyText className="text-lg font-semibold text-gray-900">
                    {countJobStage || '-'}
                  </TypographyText>
                </Tooltip>
                <Tooltip position="bottom" content={item.stageLabel}>
                  <TypographyText className="line-clamp-1 w-full break-all text-center text-xs text-[#374151]">
                    {item.stageLabel}
                  </TypographyText>
                </Tooltip>
              </div>
            )
          })}
        </div>
      </Link>
      <div className="rounded-b-md bg-gray-100">
        {data?.jobsShow?.jobStages?.length && openJobPipeLine && (
          <ChangeJobStageWithModalView jobTitle={data?.jobsShow?.title || ''}>
            {({
              setOpenMarkAsHired,
              setApplicantCurrent,
              openHiringSuccessModel
            }) => {
              const mergedOptions = {
                setOpenMarkAsHired,
                setApplicantCurrent,
                openHiringSuccessModel
              }

              // eslint-disable-next-line react-hooks/rules-of-hooks
              const getUUidV4 = useMemo(
                () => uuidV4(),
                [data?.jobsShow, refetchMyList, refetchMyDelete]
              )
              return (
                <CandidatesTabContent
                  getUUidV4={getUUidV4}
                  jobStatus={data?.jobsShow.status}
                  jobStages={jobStagesWithCondition}
                  stageTypes={stageTypes}
                  isDragDisabled={job?.status === JOB_STATUS_ENUM.archived}
                  jobId={job.id}
                  jobTitle={data?.jobsShow?.title}
                  placeViewPipeline="jobList"
                  callback={callback}
                  companyName={
                    data?.jobsShow?.permittedFields?.company?.value?.name
                  }
                  {...mergedOptions}
                />
              )
            }}
          </ChangeJobStageWithModalView>
        )}
      </div>
      <ShareJobModal
        shareInternal={checkConditionShareJob?.shareInternal}
        sharePublic={checkConditionShareJob?.sharePublic}
        urlReferral={`${PUBLIC_APP_URL}${pathConfiguration.careerHub.jobDetail({
          tenantSlug: job?.tenant?.slug,
          jobId: job?.id.toString()
        })}`}
        open={openShareJobModal}
        setOpen={setOpenShareJobModal}
        url={
          careerPageSetting
            ? `${combineDomainCareerPage(
                careerPageSetting
              )}${pathConfiguration?.careers.jobDetail({
                tenantSlug: job?.tenant?.slug,
                jobId: job?.slug
              })}?utm_medium=internal_social_share`
            : ''
        }
      />
    </div>
  )
}

export default JobCard
