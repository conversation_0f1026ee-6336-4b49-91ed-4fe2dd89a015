import { Editor as CoreEditor } from '@tiptap/core'
import { useCallback, useContext, useRef, useState } from 'react'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { TypographyH5 } from '~/core/ui/Heading'
import { RichEditor } from '~/core/ui/RichEditor'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import MutationSummaryAIWriter from '~/lib/features/candidates/graphql/mutation-sumary-AI-writer'
import { IAIWriterForm } from '~/lib/features/candidates/types'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useResumes from '~/lib/features/settings/profiles/edit/hooks/use-resumes'
import { mappingPersonalSummary } from '~/lib/features/settings/profiles/edit/mapping'
import schemaSummaryProfileForm from '~/lib/features/settings/profiles/edit/schema/summary-profile-form'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'
import { ISectionCustomFieldParamType } from '~/lib/features/settings/profiles/edit/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'
import { EditCVFormContext } from '../ResumesEditorContentView'

const SummaryGroupView = ({
  section,
  profileId,
  summaryValue
}: {
  section: ISectionCustomFieldParamType
  profileId?: string | number
  summaryValue?: string
}) => {
  const { t } = useTranslation()
  const { resumeData } = useResumeStore()
  const { handleFormChange, debouncedFormChange, noDebouncedFormChange } =
    useContext(EditCVFormContext)
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { triggerUpdateProfile } = useResumes({ isCompanyKind })
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { setToast } = useToastStore()
  const [openAIWriter, setOpenAIWriter] = useState(false)
  const toggleAIWriter = () => {
    setOpenAIWriter(!openAIWriter)
  }
  const richEditorRef = useRef<CoreEditor>()
  const [textGenerate, setTextGenerate] = useState('')
  const [languageGenerate, setLanguageGenerate] = useState('')
  const skillString = JSON.parse(
    JSON.stringify(resumeData.permittedFields?.skills?.value || [])
  )
  const formatSkills = skillString.map((item: string) => {
    return {
      value: item,
      supportingObj: {
        name: item
      }
    }
  })

  const { trigger: triggerSummaryAIWriter, isLoading: loadingGenerate } =
    useSubmitCommon(MutationSummaryAIWriter)
  const onFinishCallback = useCallback(
    async (data: IAIWriterForm, formAction: IFormAction) => {
      if (loadingGenerate) {
        return
      }
      triggerSummaryAIWriter({
        profileId: Number(profileId),
        headline: data.headline,
        skills: data.skills.map((item) => item.value),
        language: data.language
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            setToast
          })
        }

        const { profileSummary } = result.data.summaryAiWriter
        if (profileSummary) {
          setTextGenerate(profileSummary)
          setLanguageGenerate(data?.language || '')
        }
        return true
      })
    },
    [profileId, loadingGenerate]
  )
  const onRegenerate = () => {
    triggerSummaryAIWriter({
      profileId: Number(profileId),
      language: languageGenerate,
      summary: textGenerate
    }).then((result) => {
      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast
        })
      }

      const { profileSummary } = result.data.summaryAiWriter
      if (profileSummary) {
        setTextGenerate(profileSummary)
      }
      return true
    })
  }
  const onSubmitAddSummary = (onChange: (...event: any[]) => void) => {
    noDebouncedFormChange({
      fieldName: 'summary',
      callback: () => {
        triggerUpdateProfile({
          id: profileId,
          summary: textGenerate,
          addActionLogsRelatedToAi: {
            summaryAiGenerated: !summaryValue,
            summaryAiRegenerated: !!summaryValue
          }
        }).then((result) => {
          if (result) {
            handleFormChange({
              fieldName: 'summary',
              value: textGenerate,
              onChange
            })
            if (richEditorRef.current) {
              richEditorRef.current.commands.setContent(textGenerate)
            }
            toggleAIWriter()
            setTextGenerate('')
            setLanguageGenerate('')
            setToast({
              open: true,
              type: 'success',
              title: !!summaryValue
                ? `${t('notification:candidates:replace_summary_success')}`
                : `${t('notification:candidates:add_summary_success')}`
            })
          }
        })
      }
    })
  }
  return (
    <div>
      <TypographyH5 className="font-medium text-gray-900">
        {section.name || t('candidates:tabs:candidateOverview:summary:heading')}
      </TypographyH5>

      <DynamicImportForm
        isShowDebug={false}
        id="personal-summary-form"
        className="w-full"
        schema={schemaSummaryProfileForm(t)}
        defaultValue={mappingPersonalSummary(resumeData)}
        noUseSubmit>
        {({ formState, control, trigger: triggerValidate }) => {
          return (
            <div className="mt-4">
              <Controller
                control={control}
                name="summary"
                defaultValue=""
                render={({ field: { onChange, value } }) => (
                  <FormControlItem
                    destructive={formState.errors && !!formState.errors.summary}
                    destructiveText={
                      formState.errors && formState.errors.summary?.message
                    }>
                    <RichEditor
                      extraToolbar={{
                        AIWriter: {
                          show:
                            isFeatureEnabled(PLAN_FEATURE_KEYS.ai_writer) &&
                            isUnLockFeature(PLAN_FEATURE_KEYS.ai_writer),
                          isReplace: !!value,
                          onSubmitAIWriter: onFinishCallback,
                          loadingGenerate: loadingGenerate,
                          textGenerate: textGenerate,
                          defaultValue: {
                            headline:
                              resumeData?.permittedFields?.headline?.value ||
                              '',
                            profileId: Number(profileId),
                            skills: formatSkills
                          },
                          onRegenerate: onRegenerate,
                          onSubmitAddSummary: () => onSubmitAddSummary(onChange)
                        }
                      }}
                      editorRef={(editor: CoreEditor) => {
                        richEditorRef.current = editor
                      }}
                      toggleAIWriter={toggleAIWriter}
                      openAIWriter={openAIWriter}
                      limit={300000}
                      showCount={false}
                      size="sm"
                      content={value}
                      onChange={(newValue) => {
                        handleFormChange({
                          fieldName: 'summary',
                          value: newValue,
                          onChange
                        })
                        debouncedFormChange({
                          fieldName: 'summary',
                          value: newValue,
                          triggerValidate
                        })
                      }}
                      destructive={
                        formState.errors && !!formState.errors.summary
                      }
                      placeholder={`${t(
                        'candidates:tabs:candidateOverview:summary:addSummary'
                      )}`}
                      className="w-full max-w-full"
                      classNameWrapper="max-h-[75vh] min-h-[219px] w-full max-w-full overflow-y-auto"
                    />
                  </FormControlItem>
                )}
              />
            </div>
          )
        }}
      </DynamicImportForm>
    </div>
  )
}

export default SummaryGroupView
