import { useContext, useEffect, useState } from 'react'
import { Controller, UseFormTrigger, useWatch } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { Checkbox } from '~/core/ui/Checkbox'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { Input } from '~/core/ui/Input'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { RichEditor } from '~/core/ui/RichEditor'
import { ISelectOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { getMonths, getYears } from '~/core/utilities/common'
import { monthFormatDate, yearFormatDate } from '~/core/utilities/format-date'
import useCandidateProfile from '~/lib/features/candidates/hooks/use-query-candidate'
import { schemaWorkExpProfile } from '~/lib/features/candidates/schema/validation-work-exp'
import useResumes from '~/lib/features/settings/profiles/edit/hooks/use-resumes'
import { useResumeStore } from '~/lib/features/settings/profiles/edit/store'
import { IWorkExperienceParamType } from '~/lib/features/settings/profiles/edit/types'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useToastStore from '~/lib/store/toast'
import { EditCVFormContext } from '../ResumesEditorContentView'

const WorkExperienceFormView = ({
  index,
  defaultValue
}: {
  index: number
  defaultValue?: IWorkExperienceParamType
}) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const { resumeData } = useResumeStore()
  const {
    profileId,
    employeeId,
    handleFormChange,
    debouncedFormChange,
    noDebouncedFormChange
  } = useContext(EditCVFormContext)
  const { promiseCountryStateOptions } = useCandidateProfile({})
  const [locationMenuOpen, setLocationMenuOpen] = useState<boolean>(false)
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const { triggerUpdateProfile } = useResumes({ isCompanyKind })

  // ---------- FUNCTION ---------- //

  const handleUpdate = ({
    onChange,
    newValue,
    triggerValidate,
    fieldName,
    isDebouncedFormChange = true
  }: {
    onChange: (...event: any[]) => void
    newValue: unknown
    triggerValidate: UseFormTrigger<IWorkExperienceParamType>
    fieldName: string
    isDebouncedFormChange?: boolean
  }) => {
    const cloneList = JSON.parse(
      JSON.stringify(resumeData?.permittedFields?.workExperiences?.value || [])
    )

    cloneList[index][fieldName] = newValue
    onChange(newValue)
    handleFormChange({
      fieldName: 'workExperiences',
      value: cloneList
    })

    const actions = isDebouncedFormChange
      ? debouncedFormChange
      : noDebouncedFormChange

    actions({
      fieldName: undefined,
      triggerValidate,
      triggerValidateCallback: (test: boolean) => {
        if (!test) {
          cloneList[index].validate = {
            ...cloneList[index].validate,
            [fieldName]: test
          }
        } else {
          cloneList[index].validate = undefined
        }

        handleFormChange({
          fieldName: 'workExperiences',
          value: cloneList
        })
      },
      callback: () => {
        triggerUpdateProfile({
          id: profileId,
          employeeId,
          workExperiences: [
            {
              id: cloneList[index]?.id ? cloneList[index].id : undefined,
              title: cloneList[index].title,
              company: cloneList[index].company,
              fromMonth: cloneList[index].fromMonth,
              fromYear: cloneList[index].fromYear,
              toMonth: cloneList[index].toMonth,
              toYear: cloneList[index].toYear,
              location: cloneList[index]?.location?.value,
              countryStateId: Number(cloneList[index]?.location?.id),
              description: cloneList[index].description,
              currentWorking: cloneList[index].currentWorking || false,
              position: cloneList[index].position,
              _destroy: false
            }
          ]
        }).then((result: any) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              setToast
            })

            return true
          }

          const { profilesUpdate } = result.data
          if (profilesUpdate.profile && cloneList[index].id == 0) {
            const workExperiences =
              profilesUpdate?.profile?.permittedFields?.workExperiences
                ?.value || []
            cloneList[index].id = workExperiences[workExperiences.length - 1].id

            handleFormChange({
              fieldName: 'workExperiences',
              value: cloneList
            })
          }

          return true
        })
      }
    })
  }

  // ---------- RENDER ---------- //

  return (
    <DynamicImportForm
      isShowDebug={false}
      id="work-exp-form"
      className="w-full"
      defaultValue={defaultValue}
      schema={schemaWorkExpProfile(t)}
      noUseSubmit>
      {({ formState, control, trigger: triggerValidate }) => {
        // eslint-disable-next-line react-hooks/rules-of-hooks
        useEffect(() => {
          if (Object.values(defaultValue?.validate || {}).includes(false)) {
            setTimeout(() => {
              triggerValidate()
            }, 500)
          }
        }, [])

        return (
          <>
            <div className="mb-4">
              <Controller
                control={control}
                name="title"
                defaultValue={defaultValue?.title || ''}
                render={({ field: { onChange, value } }) => (
                  <FormControlItem
                    labelRequired
                    label={`${t(
                      'candidates:tabs:candidateOverview:workExperiences:jobTitleLabel'
                    )}`}
                    destructive={formState.errors && !!formState.errors?.title}
                    destructiveText={
                      formState.errors &&
                      (formState.errors?.title?.message as string)
                    }>
                    <Input
                      placeholder={`${t(
                        'candidates:tabs:candidateOverview:workExperiences:jobTitlePlaceholder'
                      )}`}
                      size="sm"
                      onChange={(newValue) => {
                        handleUpdate({
                          newValue,
                          onChange,
                          triggerValidate,
                          fieldName: 'title'
                        })
                      }}
                      value={value}
                      destructive={
                        formState.errors && !!formState.errors?.title
                      }
                    />
                  </FormControlItem>
                )}
              />
            </div>

            <div className="mb-4 flex justify-between">
              <div className="mr-4 w-full">
                <Controller
                  control={control}
                  name="company"
                  defaultValue={defaultValue?.company || ''}
                  render={({ field: { onChange, value } }) => (
                    <FormControlItem
                      labelRequired
                      label={`${t(
                        'candidates:tabs:candidateOverview:workExperiences:companyNameLabel'
                      )}`}
                      destructive={
                        formState.errors && !!formState.errors?.company
                      }
                      destructiveText={
                        formState.errors &&
                        (formState.errors?.company?.message as string)
                      }>
                      <Input
                        placeholder={`${t(
                          'candidates:tabs:candidateOverview:workExperiences:companyNamePlaceholder'
                        )}`}
                        size="sm"
                        onChange={(newValue) => {
                          handleUpdate({
                            newValue,
                            onChange,
                            triggerValidate,
                            fieldName: 'company'
                          })
                        }}
                        value={value}
                        destructive={
                          formState.errors && !!formState.errors?.company
                        }
                      />
                    </FormControlItem>
                  )}
                />
              </div>
              <div className="w-full">
                <Controller
                  control={control}
                  name="location"
                  defaultValue={defaultValue?.location}
                  render={({ field: { onChange, value } }) => {
                    return (
                      <FormControlItem
                        label={`${t(
                          'candidates:tabs:candidateOverview:workExperiences:locationLabel'
                        )}`}>
                        <AsyncSingleSearchWithSelect
                          loadAsyncWhenOpen={false}
                          promiseOptions={promiseCountryStateOptions}
                          size="sm"
                          onChange={(newValue) => {
                            handleUpdate({
                              newValue,
                              onChange,
                              triggerValidate,
                              fieldName: 'location',
                              isDebouncedFormChange: false
                            })
                            setLocationMenuOpen(false)
                          }}
                          configSelectOption={{
                            supportingText: ['location']
                          }}
                          placeholder={`${t(
                            'candidates:tabs:candidateOverview:workExperiences:locationPlaceholder'
                          )}`}
                          value={value}
                          menuIsOpen={locationMenuOpen}
                          onInputChange={(search) => {
                            if (search.length > 0) setLocationMenuOpen(true)
                            else setLocationMenuOpen(false)
                          }}
                          classNameOverride={{
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                        />
                      </FormControlItem>
                    )
                  }}
                />
              </div>
            </div>

            <div className="mb-4 flex justify-between">
              <div className="mr-4 w-full">
                <FormControlItem
                  labelRequired
                  label={`${t(
                    'candidates:tabs:candidateOverview:workExperiences:from'
                  )}`}
                  destructive={formState.errors && !!formState.errors?.fromYear}
                  destructiveText={
                    formState.errors &&
                    (formState.errors?.fromYear?.message as string)
                  }>
                  <div className="flex w-full items-center space-x-1">
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="fromMonth"
                        defaultValue={defaultValue?.fromMonth || ''}
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem
                              destructive={
                                formState.errors &&
                                !!formState.errors?.fromMonth
                              }
                              destructiveText={
                                formState.errors &&
                                (formState.errors?.fromMonth?.message as string)
                              }>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:month')}`}
                                value={
                                  value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                onChange={(data) => {
                                  const newValue =
                                    (data as ISelectOption)?.value || ''
                                  handleUpdate({
                                    newValue,
                                    onChange,
                                    triggerValidate,
                                    fieldName: 'fromMonth',
                                    isDebouncedFormChange: false
                                  })
                                }}
                                destructive={
                                  formState.errors &&
                                  !!formState.errors?.fromMonth
                                }
                                options={getMonths()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="fromYear"
                        defaultValue={defaultValue?.fromYear || ''}
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:year')}`}
                                value={
                                  value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                onChange={(data) => {
                                  const newValue =
                                    (data as ISelectOption)?.value || ''
                                  handleUpdate({
                                    newValue,
                                    onChange,
                                    triggerValidate,
                                    fieldName: 'fromYear',
                                    isDebouncedFormChange: false
                                  })
                                  triggerValidate('toYear')
                                }}
                                destructive={
                                  formState.errors &&
                                  !!formState.errors?.fromYear
                                }
                                options={getYears()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                  </div>
                </FormControlItem>
              </div>

              <div className="w-full">
                <FormControlItem
                  labelRequired
                  label={`${t(
                    'candidates:tabs:candidateOverview:workExperiences:to'
                  )}`}
                  destructive={formState.errors && !!formState.errors?.toYear}
                  destructiveText={
                    formState.errors &&
                    (formState.errors?.toYear?.message as string)
                  }>
                  <div className="flex w-full items-center space-x-1">
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="toMonth"
                        defaultValue={defaultValue?.toMonth || ''}
                        render={({ field: { onChange, value } }) => {
                          // eslint-disable-next-line react-hooks/rules-of-hooks
                          const currentWorking = useWatch({
                            control,
                            name: 'currentWorking'
                          })
                          return (
                            <FormControlItem>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:month')}`}
                                isDisabled={currentWorking}
                                value={
                                  currentWorking
                                    ? [
                                        {
                                          value: monthFormatDate(
                                            new Date()
                                          ).toString(),
                                          supportingObj: {
                                            name: monthFormatDate(
                                              new Date()
                                            ).toString()
                                          }
                                        }
                                      ]
                                    : value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                onChange={(data) => {
                                  const newValue =
                                    (data as ISelectOption)?.value || ''
                                  handleUpdate({
                                    newValue,
                                    onChange,
                                    triggerValidate,
                                    fieldName: 'toMonth',
                                    isDebouncedFormChange: false
                                  })
                                }}
                                destructive={
                                  formState.errors &&
                                  !!formState.errors?.toMonth
                                }
                                options={getMonths()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="toYear"
                        defaultValue={defaultValue?.toYear || ''}
                        render={({ field: { onChange, value } }) => {
                          // eslint-disable-next-line react-hooks/rules-of-hooks
                          const currentWorking = useWatch({
                            control,
                            name: 'currentWorking'
                          })
                          return (
                            <FormControlItem>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:year')}`}
                                isDisabled={currentWorking}
                                value={
                                  currentWorking
                                    ? [
                                        {
                                          value: yearFormatDate(
                                            new Date()
                                          ).toString(),
                                          supportingObj: {
                                            name: yearFormatDate(
                                              new Date()
                                            ).toString()
                                          }
                                        }
                                      ]
                                    : value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                onChange={(data) => {
                                  const newValue =
                                    (data as ISelectOption)?.value || ''
                                  handleUpdate({
                                    newValue,
                                    onChange,
                                    triggerValidate,
                                    fieldName: 'toYear',
                                    isDebouncedFormChange: false
                                  })
                                }}
                                destructive={
                                  formState.errors && !!formState.errors?.toYear
                                }
                                options={getYears()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                  </div>
                </FormControlItem>
              </div>
            </div>

            <div className="mb-4">
              <Controller
                control={control}
                name="currentWorking"
                defaultValue={defaultValue?.currentWorking || false}
                render={({ field: { onChange, value } }) => {
                  return (
                    <FormControlItem
                      destructive={
                        formState.errors && !!formState.errors.currentWorking
                      }
                      destructiveText={
                        formState.errors &&
                        (formState.errors.currentWorking?.message as string)
                      }>
                      <Checkbox
                        size="sm"
                        isChecked={value}
                        onCheckedChange={(e) => {
                          const newValue = e.target.checked
                          handleUpdate({
                            newValue,
                            onChange,
                            triggerValidate,
                            fieldName: 'currentWorking',
                            isDebouncedFormChange: false
                          })
                          triggerValidate('toYear')
                        }}
                        text={`${t(
                          'candidates:tabs:candidateOverview:workExperiences:currentWorkingHere'
                        )}`}
                      />
                    </FormControlItem>
                  )
                }}
              />
            </div>

            <Controller
              control={control}
              name="description"
              defaultValue={defaultValue?.description || ''}
              render={({ field: { onChange, value } }) => (
                <FormControlItem
                  label={`${t(
                    'candidates:tabs:candidateOverview:workExperiences:description'
                  )}`}
                  destructive={
                    formState.errors && !!formState.errors?.description
                  }
                  destructiveText={
                    formState.errors &&
                    (formState.errors?.description?.message as string)
                  }>
                  <RichEditor
                    isNoDebounceOnChange
                    size="sm"
                    className="w-full max-w-full"
                    onChange={(newValue) => {
                      handleUpdate({
                        newValue,
                        onChange,
                        triggerValidate,
                        fieldName: 'description'
                      })
                    }}
                    content={value}
                    limit={10000}
                    showCount={false}
                    placeholder={`${t(
                      'candidates:tabs:candidateOverview:workExperiences:descriptionPlaceholder'
                    )}`}
                    destructive={
                      formState.errors && !!formState.errors?.description
                    }
                    classNameWrapper="max-h-[75vh] min-h-[166px] w-full max-w-full overflow-y-auto"
                  />
                </FormControlItem>
              )}
            />
          </>
        )
      }}
    </DynamicImportForm>
  )
}

export default WorkExperienceFormView
