import { FC, ReactNode, useCallback, useContext, useState } from 'react'
import {
  Controller,
  FieldError,
  FieldPath,
  UseFormTrigger
} from 'react-hook-form'
import { Trans, useTranslation } from 'react-i18next'
import useEnumsData from 'src/hooks/data/use-enums-data'
import { UseQueryExecute } from 'urql'
import CustomField, { CustomFieldComponentType } from '~/components/CustomField'
import HTMLDisplay from '~/components/HTMLDisplay'
import configuration from '~/configuration'
import {
  FCC,
  ILogoAndAvatarVariants,
  IParamsTableInfinity
} from '~/core/@types/global'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import { Avatar } from '~/core/ui/Avatar'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { ChangeLogFillIcon } from '~/core/ui/FillIcons'
import { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import IconWrapper, { LucideIconName } from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import {
  InlineEditingEditor,
  InlineEditingInput,
  InlineEditingInputLink
} from '~/core/ui/InlineEditing'
import {
  InlineEditingNoActionsAsyncMultipleSearchWithSelect,
  InlineEditingNoActionsAsyncSingleSearchWithSelect,
  InlineEditingNoActionsNativeSelect
} from '~/core/ui/InlineEditingNoActions'
import { SuggestionChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { getFoundedYears } from '~/core/utilities/common'
import { defaultFormatDate, fullFormatDate } from '~/core/utilities/format-date'
import { CompaniesDetailPermissionContext } from '~/features/agency/companies/[id]'
import QueryCompanyActivities from '~/lib/features/agency/companies/graphql/query-company-activities'
import useCompanyInfoHook from '~/lib/features/agency/companies/hooks/company-info-hook'
import useQueryMembersList from '~/lib/features/agency/companies/hooks/use-query-members-list'
import { schemaUpdateCompanyProfile } from '~/lib/features/agency/companies/schema/update-company-profile-schema'
import {
  CompanyDetailResponseType,
  CompanyDetailType
} from '~/lib/features/agency/companies/types/company-detail'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import { CustomFieldFormType } from '~/lib/features/settings/profile-fields/types/custom-field'
import { useInfinityQuerySearch } from '~/lib/hooks/use-infinity-query-search'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'
import HistoryModal from './HistoryModal'
import LocationView from './LocationView'

const CompanyInformationRow: FCC<{
  icon?: LucideIconName
  title?: ReactNode
}> = ({ icon, title, children }) => {
  return (
    <>
      <div className="min-w-0 py-1.5">
        <div className="flex items-center space-x-2">
          <IconWrapper
            size={16}
            name={icon}
            className="hidden flex-none text-gray-600 desktop:block"
          />
          <TypographyText className="text-sm text-gray-700">
            {title}
          </TypographyText>
        </div>
      </div>
      <div className="min-w-0 pl-[1px]">{children}</div>
    </>
  )
}

const UserInfoAndDate: FC<{
  user?: {
    fullName?: string
    avatarVariant?: ILogoAndAvatarVariants
    defaultColor?: string
  }
  date?: string
}> = ({ user, date }) => (
  <div className="flex items-center px-2 py-1.5">
    {!!user?.fullName && (
      <>
        <Avatar
          size="xs"
          color={user?.defaultColor}
          src={user?.avatarVariant?.thumb?.url}
          alt={user?.fullName}
        />
        <Tooltip content={user?.fullName}>
          <TypographyText className="ml-2 line-clamp-1 text-sm text-gray-900">
            {user?.fullName}
          </TypographyText>
        </Tooltip>
      </>
    )}

    {!!user?.fullName && !!date && (
      <div className="mx-1.5 h-0.5 w-0.5 flex-none rounded-md bg-gray-400" />
    )}
    <TypographyText className="flex-none text-sm text-gray-600">
      {date && defaultFormatDate(new Date(date))}
    </TypographyText>
  </div>
)

const CompanyInformation: FC<{
  companyDetail?: CompanyDetailResponseType
  existedCompanyWithDomain?: CompanyDetailResponseType
  defaultValue?: CompanyDetailType
  onSubmit?: (data: CompanyDetailType, formAction?: IFormAction) => Promise<any>
  callbackSubmitLocation?: UseQueryExecute | (() => Promise<void>)
  submitPartialField: (
    fieldName: FieldPath<CompanyDetailType>,
    validate: UseFormTrigger<CompanyDetailType>,
    submit?: () => Promise<any>
  ) => Promise<boolean>
  isDrawer?: boolean
}> = ({
  companyDetail,
  defaultValue,
  onSubmit,
  callbackSubmitLocation,
  submitPartialField,
  isDrawer
}) => {
  const { t, i18n } = useTranslation()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const clientGraphQL = useGraphQLRequest({ language: user?.language })
  const permittedFields = companyDetail?.permittedFields
  const companySizeOptions = useEnumsData({
    enumType: 'AgencyCompanySize',
    locale: i18n.language
  })
  const [openHistoryModal, setOpenHistoryModal] = useState<boolean>(false)

  const companiesDetailPermission = useContext(CompaniesDetailPermissionContext)

  const { promisePublicIndustriesOptions } = useCompanyInfoHook()
  const { promiseMemberOptions } = useQueryMembersList({
    avatarSize: 'xs',
    exceptLimitedMember: true
  })
  const { data: customFieldViewData } = useCustomFieldSettingByUser({
    objectKind: 'company'
  })
  const fetchData = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientGraphQL
        .query(QueryCompanyActivities, {
          page,
          limit: configuration.defaultPageSize,
          ownerChanged: true,
          companyId: Number(companyDetail?.id)
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection =
            result?.data?.companyActivitiesList?.collection || []
          const metadata = result?.data?.companyActivitiesList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    [companyDetail?.id]
  )
  const {
    data: historyData,
    fetchNextPage,
    isFetching,
    isLoading,
    isFetchedAfterMount,
    refetch: refetchActivityOwnerList
  } = useInfinityQuerySearch({
    configuration,
    fetchData,
    queryKey: {},
    enabled: !!companyDetail?.id
  })
  return !companyDetail ? null : (
    <>
      <DynamicImportForm
        mode="onSubmit"
        id="company-information-form"
        className="w-full"
        defaultValue={defaultValue}
        schema={schemaUpdateCompanyProfile(t)}
        onSubmit={onSubmit}>
        {(formAction) => {
          const forceSubmit = () => {
            return onSubmit
              ? onSubmit(formAction.getValues(), formAction)
              : Promise.resolve()
          }
          return (
            <>
              <TypographyText className="mb-1 text-base font-medium text-gray-900">
                {t('company:company_information')}
              </TypographyText>
              <div className="mb-6 grid min-h-0 min-w-0 grid-cols-[132px_1fr]">
                <>
                  <If condition={permittedFields?.domain}>
                    <CompanyInformationRow
                      icon="Globe"
                      title={`${t('company:domain')}`}>
                      <Controller
                        control={formAction.control}
                        name="domain"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <InlineEditingInput
                                onChange={(newValue) => {
                                  onChange(newValue)
                                  return submitPartialField(
                                    'domain',
                                    formAction.trigger,
                                    forceSubmit
                                  )
                                }}
                                isDisabled={!companiesDetailPermission.update}
                                onCancelUpdate={onChange}
                                placeholder={`${t('company:not_available')}`}
                                size="sm"
                                className="-mx-px px-2"
                                value={value}
                                destructiveText={
                                  formAction.formState.errors?.domain
                                    ?.message ? (
                                    <div className="flex items-center space-x-1">
                                      <span>
                                        {
                                          formAction.formState.errors?.domain
                                            ?.message
                                        }
                                      </span>
                                      {!!(
                                        formAction.formState.errors
                                          ?.domain as FieldError & {
                                          extends: {
                                            existedCompanyId: number
                                          }
                                        }
                                      )?.extends?.existedCompanyId && (
                                        <a
                                          onClick={() => {
                                            window.open(
                                              configuration.path.agency.companyDetail(
                                                (
                                                  formAction.formState.errors
                                                    ?.domain as FieldError & {
                                                    extends: {
                                                      existedCompanyId: number
                                                    }
                                                  }
                                                )?.extends?.existedCompanyId
                                              ),
                                              'blank'
                                            )
                                          }}
                                          className="text-xs text-white underline hover:cursor-pointer">
                                          {t('company:view_company')}
                                        </a>
                                      )}
                                    </div>
                                  ) : (
                                    ''
                                  )
                                }
                                tooltipActionCancel={{
                                  title: `${t('button:cancel')}`
                                }}
                                tooltipActionSave={{
                                  title: `${t('button:saveEnter')}`
                                }}
                                tooltipElement={{
                                  title: formAction.formState.errors?.domain
                                    ?.message ? (
                                    <div className="flex items-center space-x-1">
                                      <span>
                                        {
                                          formAction.formState.errors?.domain
                                            ?.message
                                        }
                                      </span>
                                      <TextButton
                                        label={`${t('button:viewCompany')}`}
                                      />
                                    </div>
                                  ) : (
                                    ''
                                  )
                                }}>
                                <div className="px-2 py-1.5">
                                  {value ? (
                                    <Tooltip content={value} align="start">
                                      <div className="truncate text-sm text-gray-900">
                                        {value}
                                      </div>
                                    </Tooltip>
                                  ) : (
                                    <div className="line-clamp-1 text-sm text-gray-600">
                                      {t('company:not_available')}
                                    </div>
                                  )}
                                </div>
                              </InlineEditingInput>
                            </FormControlItem>
                          )
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <If condition={permittedFields?.links}>
                    <CompanyInformationRow
                      icon="Globe"
                      title={`${t('company:links')}`}>
                      <Controller
                        control={formAction.control}
                        name="links"
                        render={({ field: { onChange } }) => {
                          return (
                            <FormControlItem>
                              <InlineEditingInputLink
                                isDisabledLinks={
                                  !companiesDetailPermission.update
                                }
                                inlineMode={isDrawer ? 'drawer' : 'link'}
                                sourceGroupClassName="h-5"
                                sourceWrapperClassName="h-5"
                                linkAddButtonClassName="h-5"
                                onChange={(newValue) => {
                                  onChange({
                                    links: [newValue],
                                    _destroy: false
                                  })
                                  return submitPartialField(
                                    'links',
                                    formAction.trigger,
                                    forceSubmit
                                  )
                                }}
                                onCancelUpdate={(newValue) =>
                                  onChange({ value: newValue, _destroy: false })
                                }
                                placeholder={`${t(
                                  'candidates:tabs:candidateOverview:contactDetails:addLinks'
                                )}`}
                                size="xs"
                                onDeleteLink={(value) => {
                                  openAlert({
                                    isPreventAutoFocusDialog: false,
                                    className: 'w-[480px]',
                                    title: `${t(
                                      'candidates:tabs:candidateOverview:link:deleteTitle'
                                    )}`,
                                    description: (
                                      <Trans
                                        i18nKey="candidates:tabs:candidateOverview:link:deleteDescription"
                                        values={{
                                          link: value
                                        }}>
                                        <span className="font-medium text-gray-900" />
                                      </Trans>
                                    ),
                                    actions: [
                                      {
                                        label: `${t('button:cancel')}`,
                                        type: 'secondary',
                                        size: 'sm'
                                      },
                                      {
                                        isCallAPI: true,
                                        label: `${t('button:remove')}`,
                                        type: 'destructive',
                                        size: 'sm',
                                        onClick: async () => {
                                          onChange({
                                            links: [value],
                                            _destroy: true
                                          })
                                          return await submitPartialField(
                                            'links',
                                            formAction.trigger,
                                            forceSubmit
                                          )
                                        }
                                      }
                                    ]
                                  })
                                }}
                                source={permittedFields?.links.value}
                                tooltipActionCancel={{
                                  title: `${t('button:cancel')}`
                                }}
                                tooltipActionSave={{
                                  title: `${t('button:saveEnter')}`
                                }}
                                destructiveText={
                                  formAction.formState?.errors?.links
                                    ?.message as unknown as string
                                }
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <If condition={permittedFields?.companySize}>
                    <CompanyInformationRow
                      icon="Users"
                      title={`${t('company:company_size')}`}>
                      <Controller
                        control={formAction.control}
                        name="companySize"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <InlineEditingNoActionsNativeSelect
                                onChange={(newValue) => {
                                  onChange(newValue)
                                  return submitPartialField(
                                    'companySize',
                                    formAction.trigger,
                                    forceSubmit
                                  )
                                }}
                                onCancelUpdate={(newValue) => {
                                  onChange(newValue)
                                }}
                                isDisabled={!companiesDetailPermission.update}
                                isClearable={false}
                                options={companySizeOptions}
                                size="sm"
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                value={value}>
                                <div className="flex items-center px-2 py-1.5 text-sm">
                                  {value ? (
                                    <div className="text-gray-900">
                                      {value?.supportingObj?.name}
                                    </div>
                                  ) : (
                                    <div className="text-sm text-gray-600">
                                      {t('company:not_available')}
                                    </div>
                                  )}
                                </div>
                              </InlineEditingNoActionsNativeSelect>
                            </FormControlItem>
                          )
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <If condition={permittedFields?.companyLocations}>
                    <CompanyInformationRow
                      icon="MapPin"
                      title={`${t('company:location')}`}>
                      <LocationView
                        isShowAction={companiesDetailPermission.update}
                        companyId={Number(companyDetail?.id)}
                        data={defaultValue?.companyLocations || []}
                        callbackSubmitLocation={callbackSubmitLocation}
                        onUpdateProfile={(data) => {
                          submitPartialField(
                            'locations',
                            formAction.trigger,
                            () => {
                              return onSubmit
                                ? onSubmit(data)
                                : Promise.resolve()
                            }
                          )

                          return Promise.resolve()
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <If condition={permittedFields?.taxCode}>
                    <CompanyInformationRow
                      icon="FileSpreadsheet"
                      title={`${t('company:tax_code')}`}>
                      <Controller
                        control={formAction.control}
                        name="taxCode"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <InlineEditingInput
                                isDisabled={!companiesDetailPermission.update}
                                onChange={(newValue) => {
                                  onChange(newValue)
                                  return submitPartialField(
                                    'taxCode',
                                    formAction.trigger,
                                    forceSubmit
                                  )
                                }}
                                onCancelUpdate={onChange}
                                placeholder={`${t('company:not_available')}`}
                                size="sm"
                                className="-mx-px px-2"
                                value={value}
                                destructiveText={
                                  formAction.formState?.errors?.taxCode
                                    ?.message as string
                                }
                                tooltipActionCancel={{
                                  title: `${t('button:cancel')}`
                                }}
                                tooltipActionSave={{
                                  title: `${t('button:saveEnter')}`
                                }}
                                tooltipElement={{
                                  title: `${
                                    (formAction.formState.errors?.taxCode
                                      ?.message as string) || ''
                                  }`
                                }}>
                                <div className="px-2 py-1.5">
                                  {value ? (
                                    <Tooltip content={value} align="start">
                                      <div className="truncate text-sm text-gray-900">
                                        {value}
                                      </div>
                                    </Tooltip>
                                  ) : (
                                    <div className="line-clamp-1 text-sm text-gray-600">
                                      {t('company:not_available')}
                                    </div>
                                  )}
                                </div>
                              </InlineEditingInput>
                            </FormControlItem>
                          )
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <If condition={permittedFields?.foundedYear}>
                    <CompanyInformationRow
                      icon="HeartHandshake"
                      title={`${t('company:founded_year')}`}>
                      <Controller
                        control={formAction.control}
                        name="foundedYear"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <InlineEditingNoActionsNativeSelect
                                isDisabled={!companiesDetailPermission.update}
                                onChange={(newValue) => {
                                  onChange(newValue)
                                  return submitPartialField(
                                    'foundedYear',
                                    formAction.trigger,
                                    forceSubmit
                                  )
                                }}
                                onCancelUpdate={(newValue) =>
                                  onChange(newValue)
                                }
                                options={getFoundedYears()}
                                placeholder={t('company:select_year') as string}
                                size="sm"
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                value={
                                  value?.value !== 'null' ? value : undefined
                                }>
                                <div className="flex items-center px-2 py-1.5 text-sm">
                                  {!!value?.value && value.value !== 'null' ? (
                                    <div className="text-gray-900">
                                      {value?.supportingObj?.name}
                                    </div>
                                  ) : (
                                    <div className="text-gray-600">
                                      {t('company:select_year')}
                                    </div>
                                  )}
                                </div>
                              </InlineEditingNoActionsNativeSelect>
                            </FormControlItem>
                          )
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <If condition={permittedFields?.industries}>
                    <CompanyInformationRow
                      icon="Database"
                      title={`${t('company:industry')}`}>
                      <Controller
                        control={formAction.control}
                        name="industries"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <InlineEditingNoActionsAsyncMultipleSearchWithSelect
                                isDisabled={!companiesDetailPermission.update}
                                overlayEdited={true}
                                creatable={false}
                                onChange={(newValue) => {
                                  onChange(newValue)
                                  return submitPartialField(
                                    'industries',
                                    formAction.trigger,
                                    forceSubmit
                                  )
                                }}
                                configSelectOption={{
                                  option: 'checkbox'
                                }}
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                onCancelUpdate={onChange}
                                promiseOptions={promisePublicIndustriesOptions}
                                placeholder={
                                  t('company:add_industry') as string
                                }
                                size="sm"
                                value={value}>
                                <div className="flex items-center px-2 py-1.5 text-sm">
                                  {value && value.length > 0 ? (
                                    <div className="text-gray-900">
                                      <SuggestionChips
                                        size="sm"
                                        source={value.map((item) => ({
                                          label: item?.supportingObj?.name,
                                          maxLength: 30
                                        }))}
                                        type="default"
                                      />
                                    </div>
                                  ) : (
                                    <div className="text-gray-600">
                                      {t('company:add_industry')}
                                    </div>
                                  )}
                                </div>
                              </InlineEditingNoActionsAsyncMultipleSearchWithSelect>
                            </FormControlItem>
                          )
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <If condition={permittedFields?.owner}>
                    <CompanyInformationRow
                      icon="UserCog"
                      title={
                        <div className="flex items-center space-x-2">
                          <div>{t('company:owner')}</div>
                          {(historyData?.pages?.[0]?.meta?.totalRowCount || 0) >
                            0 && (
                            <>
                              <Tooltip
                                classNameConfig={{
                                  content: 'max-w-[340px]'
                                }}
                                content={
                                  <>
                                    {t('company:updated_at', {
                                      time: fullFormatDate(
                                        companyDetail?.updatedAt
                                          ? new Date(companyDetail?.updatedAt)
                                          : new Date()
                                      )
                                    })}{' '}
                                    -{' '}
                                    <a
                                      className="underline hover:cursor-pointer"
                                      onClick={() => setOpenHistoryModal(true)}>
                                      {t('company:history')}
                                    </a>
                                  </>
                                }>
                                <ChangeLogFillIcon />
                              </Tooltip>
                              <HistoryModal
                                open={openHistoryModal}
                                onCloseModal={setOpenHistoryModal}
                                data={historyData}
                                fetchNextPage={fetchNextPage}
                                isFetching={isFetching}
                                isLoading={isLoading}
                                isFetchedAfterMount={isFetchedAfterMount}
                              />
                            </>
                          )}
                        </div>
                      }>
                      <Controller
                        control={formAction.control}
                        name="owner"
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <InlineEditingNoActionsAsyncSingleSearchWithSelect
                                isDisabled={!companiesDetailPermission.update}
                                isClearable={false}
                                onChange={(newValue) => {
                                  onChange(newValue?.[0])
                                  return submitPartialField(
                                    'owner',
                                    formAction.trigger,
                                    forceSubmit
                                  ).then((test) => {
                                    refetchActivityOwnerList()
                                    return test
                                  })
                                }}
                                onCancelUpdate={(newValue) => {
                                  onChange(newValue?.[0])
                                }}
                                promiseOptions={promiseMemberOptions}
                                placeholder={t('company:search') as string}
                                size="sm"
                                configSelectOption={{
                                  avatar: true,
                                  supportingText: ['name', 'description']
                                }}
                                classNameOverride={{
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                value={value ? [value] : []}>
                                {value ? (
                                  <div className="flex items-center px-2 py-1.5">
                                    <div className="mr-2 flex-none">
                                      <Avatar
                                        color={
                                          value?.supportingObj?.defaultColour
                                        }
                                        size="xs"
                                        src={value?.avatarVariants?.thumb?.url}
                                        alt={value?.supportingObj?.name}
                                      />
                                    </div>
                                    <Tooltip
                                      content={value?.supportingObj?.name}
                                      classNameAsChild="truncate">
                                      <TypographyText className="truncate text-sm text-gray-900">
                                        {value?.supportingObj?.name}
                                      </TypographyText>
                                    </Tooltip>
                                  </div>
                                ) : (
                                  <div className="px-2 py-1.5 text-sm text-gray-600">
                                    {t('company:search')}
                                  </div>
                                )}
                              </InlineEditingNoActionsAsyncSingleSearchWithSelect>
                            </FormControlItem>
                          )
                        }}
                      />
                    </CompanyInformationRow>
                  </If>
                  <CompanyInformationRow
                    icon="UserPlus"
                    title={t('company:created_by')}>
                    <UserInfoAndDate
                      user={{
                        fullName: companyDetail.createdBy?.fullName,
                        defaultColor: companyDetail.createdBy?.defaultColour,
                        avatarVariant: companyDetail.createdBy?.avatarVariants
                      }}
                      date={companyDetail.createdAt}
                    />
                  </CompanyInformationRow>
                  <CompanyInformationRow
                    icon="UserCheck"
                    title={t('company:updated_by')}>
                    <UserInfoAndDate
                      user={{
                        fullName: companyDetail?.updatedBy?.fullName,
                        defaultColor: companyDetail?.updatedBy?.defaultColour,
                        avatarVariant: companyDetail?.updatedBy?.avatarVariants
                      }}
                      date={companyDetail?.updatedAt}
                    />
                  </CompanyInformationRow>
                  <Controller
                    control={formAction.control}
                    name="customFields"
                    render={({ field: { onChange, value } }) => {
                      return (
                        <>
                          {customFieldViewData.map(
                            (customField, customFieldIndex) => (
                              <CustomField
                                key={customFieldIndex}
                                type={
                                  customField.type as CustomFieldComponentType['type']
                                }
                                display="inline_editing"
                                viewDefaultPlaceholder="-"
                                name={customField.name}
                                label={customField.label}
                                error={formAction.formState.errors.customFields}
                                editingTriggerValidate={() => {
                                  return submitPartialField(
                                    'customFields',
                                    formAction.trigger,
                                    forceSubmit
                                  )
                                }}
                                value={
                                  Object.values(
                                    (value || {}) as CustomFieldFormType
                                  ).find(
                                    (item) =>
                                      String(item.id) === String(customField.id)
                                  )?.value || ''
                                }
                                onChange={(fieldValue) => {
                                  onChange({
                                    ...value,
                                    [customField.name]: {
                                      ...customField,
                                      value: fieldValue
                                    }
                                  })
                                  if (formAction.clearErrors) {
                                    formAction.clearErrors(
                                      `customFields.${customField.name}`
                                    )
                                  }
                                }}
                                callApiOnChange={true}
                                extraProps={{
                                  options: customField.selectOptions
                                }}
                                clientUserVisibility={
                                  customField.clientUserVisibility
                                }
                                classNameInlineEditting="min-w-[-webkit-fill-available]"
                              />
                            )
                          )}
                        </>
                      )
                    }}
                  />
                </>
              </div>
              <TypographyText className="mb-1 text-base font-medium text-gray-900">
                {t('company:description')}
              </TypographyText>
              <Controller
                control={formAction.control}
                name="description"
                render={({ field: { onChange, value } }) => {
                  return (
                    <FormControlItem>
                      <InlineEditingEditor
                        isDisabled={!companiesDetailPermission.update}
                        onChange={(newValue) => {
                          onChange(newValue)
                          return submitPartialField(
                            'description',
                            formAction.trigger,
                            forceSubmit
                          )
                        }}
                        onCancelUpdate={onChange}
                        localStorageId={`company-${companyDetail?.id}-description`}
                        className="min-w-auto min-h-[219px] max-w-full"
                        size="sm"
                        value={value}
                        tooltipActionCancel={{
                          title: `${t('button:cancel')}`
                        }}
                        tooltipActionSave={{
                          title: `${t('button:saveEnter')}`
                        }}
                        tooltipError={{
                          position: 'right',
                          align: 'start'
                        }}
                        destructiveText={
                          formAction.formState?.errors?.description
                            ?.message as string
                        }>
                        <div className="px-2 py-1.5">
                          {value ? (
                            <HTMLDisplay
                              content={value}
                              className="max-w-full text-sm text-gray-900"
                            />
                          ) : (
                            <div className="text-sm text-gray-600">
                              {t('company:not_available')}
                            </div>
                          )}
                        </div>
                      </InlineEditingEditor>
                    </FormControlItem>
                  )
                }}
              />
            </>
          )
        }}
      </DynamicImportForm>
    </>
  )
}

export default CompanyInformation
