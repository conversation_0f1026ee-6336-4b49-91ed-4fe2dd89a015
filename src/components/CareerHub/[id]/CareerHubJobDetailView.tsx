import { formatDistanceToNowStrict } from 'date-fns'
import { useTranslation } from 'next-i18next'
import { useRouter } from 'next/router'
import {
  Dispatch,
  FC,
  SetStateAction,
  useCallback,
  useMemo,
  useState,
  MouseEvent,
  useEffect
} from 'react'
import pathConfiguration from 'src/configuration/path'
import ApplicationFormModal from '~/components/Apply/[jobId]/components/ApplicationFormModal'
import TooltipWithButton from '~/components/Apply/[jobId]/components/TooltipWithButton'
import { ModalMatchedRankType } from '~/components/Candidates/Profile/components/Recommendation/MatchedRankDetail'
import MatchedRankDetailWrapper from '~/components/Candidates/Profile/components/Recommendation/MatchedRankDetailWrapper'
import CustomField, { CustomFieldComponentType } from '~/components/CustomField'
import HTMLDisplay from '~/components/HTMLDisplay'
import ShareJobModal from '~/components/Jobs/ShareJobModal'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { PUBLIC_APP_URL } from '~/core/constants/env'
import { CAREERS_LIST_URL } from '~/core/constants/url'
import { Button } from '~/core/ui/Button'
import { Container } from '~/core/ui/Container'
import { Divider } from '~/core/ui/Divider'
import { IFormAction } from '~/core/ui/Form'
import If from '~/core/ui/If'
import { SuggestionChips } from '~/core/ui/SuggestionChips'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { defaultFormatDate } from '~/core/utilities/format-date'
import schemaInternalApplicationForm from '~/lib/features/apply/jobId/schema/validation-internal-application-form'
import { ApplicationFormType, JobType } from '~/lib/features/apply/jobId/types'
import { renderSalary } from '~/lib/features/apply/jobId/utilities/helper-salary'
import useShareJobLogic from '~/lib/features/jobs/hooks/use-share-job-logic'
import {
  JOB_STATUS_ENUM,
  REFERRAL_REWARD_MONEY_VALUE,
  SYSTEM_JOB_FIELDS
} from '~/lib/features/jobs/utilities/enum'
import CreateInternalApplicationMutation from '~/lib/features/referrals/graphql/create-internal-application-mutation'
import useProfileEmployeeHook from '~/lib/features/referrals/hooks/use-profile-employee-hook'
import { mappingProfileEmployeeToApplicationForm } from '~/lib/features/referrals/mapping/mappingApplicationForm'
import useReferralStore from '~/lib/features/referrals/store'
import { ReferralFormType } from '~/lib/features/referrals/types'
import { DEPARTMENT_SHOW_TOP_LEVEL } from '~/lib/features/settings/careers/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useCustomFieldSettingByUser from '~/lib/features/settings/profile-fields/hooks/use-custom-field-setting-by-user'
import useTenantSettingJobFieldsHook from '~/lib/features/settings/profile-fields/hooks/use-tenant-setting-job-field-hook'
import useReferralSetting from '~/lib/features/settings/referrals/hooks/useReferralSetting'
import { convertMoneyNotRounded } from '~/lib/features/settings/referrals/utilities/enum'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import ReferralModal from './ReferralModal'
import useToastStore from '~/lib/store/toast'
import { Avatar } from '~/core/ui/Avatar'
import { CourseType } from '~/lib/features/career-hub/types'
import configuration from '~/configuration'
import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import QueryCoursesRecommended from '~/lib/features/career-hub/graphql/query-course-recommended'
import RecommendedCourses from '~/components/Course/RecommendCourseList'
import { InfiniteData } from '@tanstack/react-query'
import CourseDetailModal from '~/components/Course/CourseDetailModal'
import useLearningLibrary from '~/lib/features/career-hub/hook/use-learning-library'

const CareerHubJobDetailView: FC<{
  job: JobType
  openMatchedRank?: Dispatch<SetStateAction<ModalMatchedRankType>>
  refetch: () => void
}> = ({ job, openMatchedRank, refetch }) => {
  const [openApplicant, setOpenApplicant] = useState<boolean>(false)
  const [openReferralModal, setOpenReferralModal] = useState<boolean>(false)
  const [openShareModal, setOpenShareModal] = useState<boolean>(false)
  const [referralJob, setReferralJob] = useState<ReferralFormType>()
  const { user } = useBoundStore()
  const { employeeProfile, fetchEmployeeProfile } = useProfileEmployeeHook()
  const { isShowSystemFieldWithCareerSite } = useTenantSettingJobFieldsHook()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const router = useRouter()
  const isArchivedJob = useMemo(() => {
    return job?.status === JOB_STATUS_ENUM.archived
  }, [job])
  const { dataReferral } = useReferralSetting()
  const { valueShareJobWithCondition } = useShareJobLogic()
  const checkConditionShareJob = valueShareJobWithCondition({
    enablingReferral: dataReferral?.values?.enabling || false,
    enablingCareerSite:
      user.currentTenant?.careerSiteSettings?.enablingCareerSiteSetting ||
      false,
    enableJobReferral: job?.jobReferable || false,
    jobStatus: job?.status || ''
  })
  const careerSiteSettingsLanguages =
    user?.currentTenant?.careerSiteSettings?.languages
  const japaneseLanguageDefault = careerSiteSettingsLanguages?.ja?.default
  const referralPortal = dataReferral?.values?.referral_portal
  const careerPageSetting = job?.tenant?.careerSiteSettings

  const { t } = useTranslation()
  const uniqueLocations = useMemo<Array<string>>(() => {
    const jobLocation = (job?.jobLocations || []).map((location) =>
      [
        location?.city?.trim(),
        location?.state?.trim(),
        location?.country?.trim()
      ]
        .filter((item) => item)
        .join(', ')
    )
    return jobLocation.filter(
      (location, index) => jobLocation.indexOf(location) === index
    )
  }, [job?.jobLocations])
  const [showAll, setShowAll] = useState(false)
  const visibleRows = showAll ? uniqueLocations : uniqueLocations.slice(0, 2)

  const refetchMyReferralsList = useReferralStore(
    (state) => state.refetchMyReferralsList
  )

  const onOpenApplicationChange = useCallback((state: boolean) => {
    setOpenApplicant(state)
  }, [])

  const onClickApplyNow = useCallback(() => {
    onOpenApplicationChange(true)
  }, [onOpenApplicationChange])
  const onCloseReferralModal = () => setOpenReferralModal(false)
  const handleOpenReferralModal = useCallback(
    (job: JobType) => {
      setReferralJob({
        jobId: [
          {
            value: String(job?.id),
            supportingObj: {
              name: job?.title
            }
          }
        ]
      })
      setOpenReferralModal(true)
    },
    [setOpenReferralModal]
  )
  const callbackOnFinishReferralModal = useCallback(async () => {
    refetchMyReferralsList && refetchMyReferralsList()
  }, [refetchMyReferralsList])

  const { setToast } = useToastStore()
  const { trigger, isLoading } = useSubmitCommon(
    CreateInternalApplicationMutation
  )
  const { data: customFieldViewData } = useCustomFieldSettingByUser({
    objectKind: 'job',
    employeeId: Number(user?.id)
  })
  const onFinishApplicationForm = useCallback(
    async (data: ApplicationFormType, formAction: IFormAction) => {
      if (isLoading) {
        return
      }

      trigger({
        jobId: Number(job.id),
        ...data
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            formAction,
            setToast,
            callbackHandleStatusError422: (keys) => {
              keys.forEach((key) => {
                if (key.message.includes('terms_and_conditions')) {
                  setToast({
                    open: true,
                    type: 'error',
                    title: `${t('form:consent_data_processing')}`
                  })
                } else {
                  if (!key.field) {
                    setToast({
                      open: true,
                      type: 'error',
                      title: key.message
                    })
                  } else {
                    formAction.setError(key.field, {
                      type: 'custom',
                      message: key.message
                    })
                  }
                }
              })
            }
          })
        }
        const { internalApplicantsCreate } = result.data
        if (internalApplicantsCreate?.profile?.id) {
          //setToast
          setToast({
            open: true,
            type: 'success',
            title: `${t('careers:applied:applied_successfully')}`
          })

          setOpenApplicant(false)
          fetchEmployeeProfile()
          refetch()
        }
        return true
      })
    },
    [isLoading, job?.id, job?.tenant?.slug]
  )
  const rank = job?.recommendationMatchedFields?.filter(
    (r) => r.field === 'total'
  )[0]
  const permittedFields = job?.permittedFields
  const talentPoolString = JSON.parse(
    JSON.stringify(permittedFields?.talentPools?.value || [])
  )

  const suggestionChipTalentPools = talentPoolString.map(
    (item: { name: string }) => {
      return {
        label: item.name,
        maxLength: 30
      }
    }
  )

  const [courseList, setCoursesList] = useState<CourseType[]>([])
  const [openModal, setOpenModal] = useState<boolean>(false)
  const [courseId, setCourseId] = useState<Number | undefined>()
  const [filter, setFilter] = useState({
    page: 1,
    limit: configuration.defaultCoursePageSize
  })
  const jobId = String(router.query?.id || '')
  const {
    data: recommendedCoursesList,
    fetchNextPage,
    hasNextPage,
    isFetching
  } = useInfinityGraphPage({
    queryDocumentNote: QueryCoursesRecommended,
    getVariable: (page) => ({ ...filter, page, jobId: Number(jobId) }),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.recommendedCourses?.metadata?.totalCount,
      pageLength: groups[0]?.recommendedCourses?.collection?.length
    }),
    queryKey: ['course-recommend-listing-job-detail']
  })
  const isShowCompanyFeature =
    isFeatureEnabled(PLAN_FEATURE_KEYS.company) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.company)

  useEffect(() => {
    if (recommendedCoursesList) {
      const recommendedCourses = recommendedCoursesList.pages.flatMap(
        (page) => page?.recommendedCourses?.collection || []
      ) as CourseType[]
      setCoursesList(recommendedCourses)
    }
  }, [recommendedCoursesList])

  const handleSkillRedirect = useCallback((skill: string) => {
    const slug = String(router.query?.slug || '')
    const path = configuration.path.careerHub.library(slug, [skill])
    window.open(path, '_blank')
  }, [])

  const {
    providerOptions,
    levelOptions,
    typeOptions,
    languagesOptions,
    priceOptions
  } = useLearningLibrary({ disableGetCourse: true })

  return job ? (
    <>
      <div className="flex items-center justify-between pb-[32px]">
        <div className="flex max-w-[804px] items-center">
          <If
            condition={isShowSystemFieldWithCareerSite(
              SYSTEM_JOB_FIELDS.company
            )}>
            <div className="mr-2">
              <Avatar
                defaultAvatar={false}
                shape="rounded"
                src={
                  job?.permittedFields?.company?.value?.logoVariants?.thumb?.url
                }
                alt={job?.permittedFields?.company?.value?.name}
                size="xl"
                color="#FFFFFF"
              />
            </div>
          </If>
          <div>
            <div className="text-2xl font-semibold text-gray-900">
              {job?.title}
              {job.recommendationMatchedFields &&
                rank &&
                rank.total_rate > 0 && (
                  <span className="ml-2 inline-block align-middle">
                    <div>
                      <MatchedRankDetailWrapper
                        openMatchedRank={openMatchedRank}
                        job={job}
                        size="lg"
                      />
                    </div>
                  </span>
                )}
            </div>
            <If
              condition={
                isShowCompanyFeature &&
                isShowSystemFieldWithCareerSite(SYSTEM_JOB_FIELDS.company)
              }>
              <div className="text-base font-medium text-gray-900">
                {job?.permittedFields?.company?.value?.name ||
                  user?.currentTenant?.name}
              </div>
            </If>
          </div>
        </div>
        <div className="flex items-center justify-end space-x-4">
          {(referralPortal?.referral_only || referralPortal?.referral_job) && (
            <div>
              {job?.publicReferralUri ||
              checkConditionShareJob?.shareInternal ||
              checkConditionShareJob?.sharePublic ? (
                <div>
                  <Tooltip
                    position="left"
                    content={`${t(
                      'careerHub:open_jobs:share:disabled_button_tooltip'
                    )}`}
                    classNameConfig={{
                      content:
                        job?.status !== JOB_STATUS_ENUM.publish ? '' : 'hidden'
                    }}>
                    <Button
                      isDisabled={job?.status !== JOB_STATUS_ENUM.publish}
                      type="secondary"
                      iconMenus="Share2"
                      size="md"
                      onClick={() => setOpenShareModal(true)}
                    />
                  </Tooltip>
                </div>
              ) : null}
            </div>
          )}

          {(referralPortal?.referral_only || referralPortal?.referral_job) && (
            <div>
              <Button
                type={referralPortal?.referral_only ? 'primary' : 'secondary'}
                label={`${t('careerHub:detail:btn_refer')}`}
                size="md"
                className="min-w-[105px]"
                onClick={() => {
                  handleOpenReferralModal(job)
                }}
              />
            </div>
          )}

          {(referralPortal?.job_only || referralPortal?.referral_job) && (
            <div>
              <TooltipWithButton
                content={t('careerHub:tooltip:applied_tooltip_disable', {
                  date: `${defaultFormatDate(
                    new Date(job.currentUserAppliedAt)
                  )}`
                })}
                isShowTooltip={!!job.currentUserAppliedAt}>
                <div>
                  <Button
                    className="min-w-[124px] whitespace-pre"
                    isDisabled={!!job.currentUserAppliedAt}
                    label={`${t(
                      !!job.currentUserAppliedAt
                        ? 'careerHub:detail:btn_applied'
                        : 'careers:detail:applyNow'
                    )}`}
                    size="md"
                    onClick={() => onClickApplyNow()}
                  />
                </div>
              </TooltipWithButton>
            </div>
          )}
        </div>
      </div>

      <Container>
        <div className="flex justify-between space-x-12">
          <div className="w-[804px]">
            {job?.jobLocations?.length > 0 && (
              <div className="grid grid-cols-[200px_1fr]">
                <TypographyText className="space-x-2 py-1 text-sm text-gray-700">
                  {t('careers:detail:location')}
                </TypographyText>
                <TypographyText
                  tagName="div"
                  className="px-2 py-1 text-sm text-gray-900">
                  {visibleRows.map((location, index) => (
                    <div key={`job-location-${index}`}>{location}</div>
                  ))}
                  {uniqueLocations.length > 2 && !showAll && (
                    <TextButton
                      size="md"
                      onClick={() => setShowAll(true)}
                      label={t('button:seeMore')}
                    />
                  )}
                </TypographyText>
              </div>
            )}
            {(customFieldViewData || [])?.map(
              (customField, customFieldIndex) => {
                if (!customField.careerSiteVisibility) return null

                const customFieldData = (job.customFields || []).find(
                  (customFieldValue) =>
                    String(customFieldValue.customSettingId) ===
                    String(customField.id)
                )

                return (
                  <div
                    key={customFieldIndex}
                    className="grid grid-cols-[200px_1fr]">
                    <CustomField
                      showIconHorizontalView={false}
                      type={
                        customField.type as CustomFieldComponentType['type']
                      }
                      display="view_horizontal"
                      name={customField.name}
                      classNameText="text-sm"
                      sizeField="md"
                      label={customField.label}
                      error={{}}
                      value={
                        customFieldData?.fieldKind === 'array'
                          ? customFieldData?.selectedOptionKeys?.[0]
                          : customFieldData?.fieldKind === 'multiple'
                          ? customFieldData?.selectedOptionKeys
                          : customFieldData?.value
                      }
                      onChange={() => {}}
                      extraProps={{
                        options: customField.selectOptions
                      }}
                    />
                  </div>
                )
              }
            )}
            {(customFieldViewData || []).filter(
              (customField) => !!customField.careerSiteVisibility
            )?.length > 0 && <Divider className="mb-6 mt-4" />}

            {job?.pitch && (
              <div className="mb-6 tablet:mb-10">
                <TypographyText className="mb-2 text-base font-medium text-gray-900">
                  {t('careers:detail:whatMakeSpecial')}
                </TypographyText>
                <HTMLDisplay
                  content={job?.pitch}
                  className="text-sm text-gray-900"
                />
              </div>
            )}
            {job?.description && (
              <div className="mb-6">
                <TypographyText className="mb-2 text-base font-medium text-gray-900">
                  {t('careers:detail:jobDescription')}
                </TypographyText>
                <HTMLDisplay
                  isConvertLinkFromHTML
                  content={job?.description}
                  className="text-sm text-gray-900"
                />
              </div>
            )}
            {(referralPortal?.job_only || referralPortal?.referral_job) && (
              <div>
                <TooltipWithButton
                  content={t('careerHub:tooltip:applied_tooltip_disable', {
                    date: `${defaultFormatDate(
                      new Date(job.currentUserAppliedAt)
                    )}`
                  })}
                  isShowTooltip={!!job.currentUserAppliedAt}>
                  <div>
                    <Button
                      className="w-full whitespace-pre"
                      isDisabled={!!job.currentUserAppliedAt}
                      label={`${t(
                        !!job.currentUserAppliedAt
                          ? 'careerHub:detail:btn_applied'
                          : 'careers:detail:applyNow'
                      )}`}
                      size="sm"
                      onClick={() => onClickApplyNow()}
                    />
                  </div>
                </TooltipWithButton>
              </div>
            )}

            <div className="mt-8">
              <RecommendedCourses
                courseList={courseList}
                totalCount={
                  recommendedCoursesList?.pages[0].recommendedCourses.metadata
                    .totalCount || 0
                }
                hasNextPage={hasNextPage}
                isFetching={isFetching}
                setOpenModal={setOpenModal}
                setCourseId={setCourseId}
                fetchNextPage={fetchNextPage}
                handleSkillRedirect={handleSkillRedirect}
              />
            </div>
          </div>
          <div className="w-[309px]">
            <div className="mb-4 rounded border border-gray-300 p-4 tablet:p-6">
              {isShowSystemFieldWithCareerSite(SYSTEM_JOB_FIELDS['tags']) &&
                (job.tags || [])?.length > 0 && (
                  <div className="mb-6">
                    <TypographyText className="mb-1.5 text-xs uppercase text-gray-700">
                      {t('job:detail:summaryInfo:tags')}
                    </TypographyText>
                    <TypographyText
                      tagName="div"
                      className="text-sm font-medium text-gray-900">
                      <SuggestionChips
                        size="md"
                        source={
                          job.tags?.map((tag) => ({
                            label: tag.name
                          })) || []
                        }
                        type="default"
                        classNameChip="line-clamp-none h-auto items-start text-left"
                      />
                    </TypographyText>
                  </div>
                )}

              {isShowSystemFieldWithCareerSite(SYSTEM_JOB_FIELDS['salary']) && (
                <>
                  <TypographyText className="text-uppercase mb-0.5 text-xs text-gray-700">
                    {t('careers:detail:salary')}
                  </TypographyText>
                  <TypographyText className="mb-4 text-base font-medium text-gray-900 tablet:mb-6">
                    {renderSalary({
                      t,
                      salaryFrom: Number(
                        job?.permittedFields?.salaryFrom?.value
                      ),
                      salaryTo: Number(job?.permittedFields?.salaryTo?.value),
                      currency: job?.permittedFields?.currency?.value
                        ? String(job?.permittedFields?.currency?.value)
                        : '',
                      typeOfSalaryDescription: job?.permittedFields
                        ?.typeOfSalaryDescription?.value
                        ? String(
                            job?.permittedFields?.typeOfSalaryDescription?.value
                          )
                        : ''
                    })}
                  </TypographyText>
                </>
              )}

              {isShowSystemFieldWithCareerSite(
                SYSTEM_JOB_FIELDS['employment_type']
              ) &&
                job?.permittedFields?.employmentTypeDescription?.value && (
                  <>
                    <TypographyText className="text-uppercase mb-0.5 text-xs text-gray-700">
                      {t('careers:detail:employmentType')}
                    </TypographyText>
                    <TypographyText className="mb-4 text-base  font-medium text-gray-900 tablet:mb-6">
                      {job?.permittedFields.employmentTypeDescription?.value}
                    </TypographyText>
                  </>
                )}
              {job?.department?.name && (
                <>
                  <TypographyText className="text-uppercase mb-0.5 text-xs text-gray-700">
                    {t('careers:detail:department')}
                  </TypographyText>
                  <TypographyText className="mb-4 text-sm font-medium text-gray-900 tablet:mb-6">
                    {careerPageSetting?.department_visibility ===
                    DEPARTMENT_SHOW_TOP_LEVEL
                      ? job?.department?.parent?.name || job?.department?.name
                      : job?.department?.name}
                  </TypographyText>
                </>
              )}
              {job?.jobCategory && (
                <div className="mb-6">
                  <TypographyText className="mb-0.5 text-xs uppercase text-gray-700">
                    {t('job:detail:summaryInfo:category')}
                  </TypographyText>
                  <TypographyText className="text-sm font-medium text-gray-900">
                    {job?.jobCategory?.name}
                  </TypographyText>
                </div>
              )}
              {isShowSystemFieldWithCareerSite(
                SYSTEM_JOB_FIELDS['job_level']
              ) &&
                job?.permittedFields?.jobLevelDescription?.value && (
                  <div className="mb-6">
                    <TypographyText className="mb-0.5 text-xs uppercase text-gray-700">
                      {t('job:detail:summaryInfo:experienceLevel')}
                    </TypographyText>
                    <TypographyText className="text-base font-medium text-gray-900">
                      {job?.permittedFields.jobLevelDescription?.value}
                    </TypographyText>
                  </div>
                )}
              {job?.educationDescription && (
                <div className="mb-6">
                  <TypographyText className="mb-0.5 text-xs uppercase text-gray-700">
                    {t('job:detail:summaryInfo:educationLevel')}
                  </TypographyText>
                  <TypographyText className="text-sm font-medium text-gray-900">
                    {job?.educationDescription}
                  </TypographyText>
                </div>
              )}
              {(job.skills || [])?.length > 0 && (
                <div className="mb-6">
                  <TypographyText className="mb-1.5 text-xs uppercase text-gray-700">
                    {t('job:detail:summaryInfo:skills')}
                  </TypographyText>
                  <TypographyText
                    tagName="div"
                    className="text-sm font-medium text-gray-900">
                    <SuggestionChips
                      size="md"
                      source={
                        job.skills?.map((skill) => ({
                          label: skill,
                          maxLength: 30,
                          onClick: () => {
                            router.push(
                              `${pathConfiguration.careerHub.jobs(
                                user?.currentTenant?.slug || ''
                              )}?q=${skill}`
                            )
                          }
                        })) || []
                      }
                      type="default"
                      classNameChip="line-clamp-none h-auto items-start text-left"
                    />
                  </TypographyText>
                </div>
              )}
              {isShowSystemFieldWithCareerSite(
                SYSTEM_JOB_FIELDS['job_talent_pool_ids']
              ) &&
                job?.permittedFields?.talentPools?.value &&
                job?.permittedFields?.talentPools?.value?.length > 0 && (
                  <div className="mb-6">
                    <TypographyText className="mb-1.5 text-xs uppercase text-gray-700">
                      {t('job:detail:summaryInfo:talent_pools')}
                    </TypographyText>
                    <TypographyText
                      tagName="div"
                      className="text-sm font-medium text-gray-900">
                      <SuggestionChips
                        size="md"
                        source={suggestionChipTalentPools}
                        type="default"
                        classNameChip="line-clamp-none h-auto items-start text-left"
                      />
                    </TypographyText>
                  </div>
                )}
              {isShowSystemFieldWithCareerSite(
                SYSTEM_JOB_FIELDS['remote_status']
              ) &&
                job?.permittedFields?.remoteStatusDescription?.value && (
                  <div className="mb-6">
                    <TypographyText className="mb-0.5 text-xs uppercase text-gray-700">
                      {t('careerHub:detail:remote_status_label')}
                    </TypographyText>
                    <TypographyText className="text-sm font-medium text-gray-900">
                      {job?.permittedFields.remoteStatusDescription.value}
                    </TypographyText>
                  </div>
                )}
              {isShowSystemFieldWithCareerSite(
                SYSTEM_JOB_FIELDS['public_id']
              ) &&
                job?.permittedFields?.publicId?.value && (
                  <div className="mb-6">
                    <TypographyText className="mb-0.5 text-xs uppercase text-gray-600">
                      {t('careerHub:detail:job_id_label')}
                    </TypographyText>
                    <TypographyText className="text-sm font-medium text-gray-900">
                      {job?.permittedFields.publicId.value}
                    </TypographyText>
                  </div>
                )}
              <TypographyText className="text-uppercase mb-0.5 text-xs uppercase text-gray-700">
                {t('careers:detail:posted')}
              </TypographyText>
              {job?.createdAt ? (
                <Tooltip
                  align="start"
                  content={defaultFormatDate(new Date(job.createdAt))}>
                  <TypographyText className="text-sm font-medium text-gray-900">
                    {formatDistanceToNowStrict(new Date(job.createdAt))}
                  </TypographyText>
                </Tooltip>
              ) : null}
            </div>
            <If
              condition={
                job?.enablingReward &&
                (referralPortal?.job_only || referralPortal?.referral_job)
              }>
              <div className="mb-6">
                <div className="h-0.5 w-0.5 rounded-md bg-gray-400" />
                <div className="rounded-lg border-[1px] border-solid border-b-gray-100 px-6 py-3 text-gray-900">
                  <p className="mb-0.5 text-sm font-medium uppercase">
                    {t('careerHub:detail:reward_referral')}
                  </p>
                  <p className="text-sm font-normal">
                    {convertMoneyNotRounded(job?.rewardAmount)}{' '}
                    {job?.referralRewardType === REFERRAL_REWARD_MONEY_VALUE
                      ? job?.rewardCurrency
                      : job?.rewardGift}
                  </p>
                </div>
              </div>
            </If>
          </div>
        </div>
      </Container>

      {openApplicant && (
        <ApplicationFormModal
          job={job}
          view={
            isFeatureEnabled(PLAN_FEATURE_KEYS.application_form) &&
            isUnLockFeature(PLAN_FEATURE_KEYS.application_form)
              ? 'custom'
              : 'default'
          }
          open={openApplicant}
          setOpen={onOpenApplicationChange}
          isSubmitting={isLoading}
          onSubmit={onFinishApplicationForm}
          disableFields={['email']}
          defaultValue={mappingProfileEmployeeToApplicationForm({
            profile: employeeProfile,
            user
          })}
          schema={schemaInternalApplicationForm(t)}
        />
      )}
      <ShareJobModal
        shareInternal={checkConditionShareJob?.shareInternal}
        sharePublic={checkConditionShareJob?.sharePublic}
        urlReferral={`${PUBLIC_APP_URL}${pathConfiguration.careerHub.jobDetail({
          tenantSlug: job?.tenant?.slug,
          jobId: job?.id.toString()
        })}`}
        open={openShareModal}
        setOpen={setOpenShareModal}
        url={`${PUBLIC_APP_URL}${
          japaneseLanguageDefault ? '/ja' : ''
        }${CAREERS_LIST_URL}/${
          job?.publicReferralUri
        }&utm_medium=internal_social_share`}
      />
      <ReferralModal
        openReferralModal={openReferralModal}
        onClose={onCloseReferralModal}
        defaultValue={referralJob}
        callbackOnFinish={callbackOnFinishReferralModal}
      />

      <If condition={openModal && courseId}>
        <CourseDetailModal
          open={openModal}
          setOpen={setOpenModal}
          courseId={courseId}
          handleSkillRedirect={handleSkillRedirect}
          enumsData={{
            providerOptions,
            levelOptions,
            typeOptions,
            languagesOptions,
            priceOptions
          }}
        />
      </If>
    </>
  ) : null
}

export default CareerHubJobDetailView
