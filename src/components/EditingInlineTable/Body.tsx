import { ReactNode, useEffect, useState } from 'react'
import { createRoot } from 'react-dom/client'
import { Controller } from 'react-hook-form'
import { ZodType, ZodTypeDef } from 'zod'
import { But<PERSON> } from '~/core/ui/Button'
import ComboboxSelect, { ComboboxSelectProps } from '~/core/ui/ComboboxSelect'
import { DashedButton } from '~/core/ui/DashedButton'
import { Dialog } from '~/core/ui/Dialog'
import { DropdownMenu } from '~/core/ui/DropdownMenu'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { FormControlItem } from '~/core/ui/FormControlItem'
import IconWrapper, { LucideIconName } from '~/core/ui/IconWrapper'
import { Input } from '~/core/ui/Input'
import { InputRightElement } from '~/core/ui/InputElement'
import { InputGroup } from '~/core/ui/InputGroup'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { PhoneInput } from '~/core/ui/PhoneInput'
import { RichEditor, RichEditorProps } from '~/core/ui/RichEditor'
import {
  IPromiseSearchOption,
  ISelectOption,
  SelectProps
} from '~/core/ui/Select'
import {
  DatePickerValue,
  SingleDateWithYearOnlyPicker
} from '~/core/ui/SingleDateWithYearOnlyPicker'
import { Toggle } from '~/core/ui/Toggle'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn, delayMilliseconds } from '~/core/ui/utils'
import { isHTML } from '~/core/utilities/common'
import ComputeInputRightPadding from '~/features/placements/components/ComputeInputRightPadding'
import { useDetectCountryCodeFromTimeZone } from '~/lib/countries-mapping/hooks/use-detect-country-code-from-time-zone'

type IMode = 'editing' | 'onlyView'
type IModeComponent =
  | 'input'
  | 'inputArray'
  | 'number'
  | 'phone'
  | 'toggle'
  | 'inputRight'
  | 'select'
  | 'date'
  | 'richEditor'
  | 'link'

interface ISelectEditingInline extends SelectProps {
  promiseOptions?: (
    params: IPromiseSearchOption
  ) => Promise<{ metadata?: { totalCount: number }; collection: never[] }>
  forceUpdatePromiseOptions?: boolean
  loadAsyncWhenRender?: boolean
  loadAsyncWhenOpen?: boolean
}

interface IEditorEditingInline extends RichEditorProps {
  title: string
  cancel: string
  save: string
}

export const ENUMS_EDITING_INLINE_MODE: { [key: string]: IMode } = {
  editing: 'editing',
  onlyView: 'onlyView'
}

export const ENUMS_EDITING_INLINE_MODE_COMPONENT: {
  [key: string]: IModeComponent
} = {
  input: 'input',
  inputArray: 'inputArray',
  number: 'number',
  phone: 'phone',
  toggle: 'toggle',
  inputRight: 'inputRight',
  select: 'select',
  date: 'date',
  richEditor: 'richEditor',
  link: 'link'
}

interface EditingInlineTableBodyProps {
  bodyConfig: {
    mode: IMode
    content: ReactNode | string
    className?: string
  }
  editingConfig?: {
    id: string
    mode: IModeComponent
    select?: ComboboxSelectProps
    inputRight?: ISelectEditingInline
    editor?: IEditorEditingInline
    placeholder?: string
    isDisabled?: boolean
    isDisabledTooltip?: string
    configShow?: {
      top: number
      left: number
    }
    value: unknown
    onChange: (value: unknown, accessorKey: string) => void
    triggerUpdatedOnCreate?: boolean
    onCreate?: (
      value: unknown,
      callback?: (value: Array<{ id: string; index: number }>) => void
    ) => void
  }
  accessorKey: string
  actions?: Array<{
    name?: string
    iconMenus?: LucideIconName
    isDisabled?: boolean
    isDisabledTooltip?: string
    isLoading?: boolean
    onClick?: () => void
    subMenus?: Array<{
      name: string
      iconMenus?: LucideIconName
      isDisabled?: boolean
      isDisabledTooltip?: string
      isLoading?: boolean
      onClick?: () => void
    }>
  }>
  schema?: ZodType<any, ZodTypeDef, any>
}

const InlineTableComponent = (props: {
  schema?: ZodType<any, ZodTypeDef, any>
  itemRect?: DOMRect | undefined
  editingConfig?: {
    id: string
    mode: IModeComponent
    select?: ComboboxSelectProps
    inputRight?: ISelectEditingInline
    editor?: IEditorEditingInline
    placeholder?: string
    isDisabled?: boolean
    isDisabledTooltip?: string
    configShow?: {
      top: number
      left: number
    }
    value: unknown
    onChange: (value: unknown, accessorKey: string) => void
    triggerUpdatedOnCreate?: boolean
    onCreate?: (
      value: unknown,
      callback?: (value: Array<{ id: string; index: number }>) => void
    ) => void
  }
  accessorKey: never
  destroy: (valueState: unknown, options: { isTriggerUpdated: boolean }) => void
}) => {
  const { schema, itemRect, editingConfig, accessorKey, destroy } = props
  const { countryCode } = useDetectCountryCodeFromTimeZone()
  const [valueState, setValueState] = useState(editingConfig?.value)
  const [isDestructive, setDestructive] = useState(false)
  const [valueRightState, setValueRightState] = useState(
    editingConfig?.inputRight?.value
  )
  const [isTriggerUpdated, setTriggerUpdated] = useState(false)
  const tableEditing = document.getElementById('app-editing-table-hidden')
  const tableEditingX = tableEditing?.getBoundingClientRect().x || 0

  if (editingConfig?.mode === ENUMS_EDITING_INLINE_MODE_COMPONENT.richEditor) {
    return (
      <Dialog
        isPreventAutoFocusDialog
        size="md"
        label={editingConfig.editor?.title}
        open={true}
        onOpenChange={(open) =>
          destroy(editingConfig?.value, { isTriggerUpdated })
        }>
        <DynamicImportForm
          id="inline-editing-form"
          defaultValue={{
            [accessorKey]: editingConfig?.value as keyof unknown
          }}
          schema={schema}
          noUseSubmit>
          {({ formState, control }) => {
            // eslint-disable-next-line react-hooks/rules-of-hooks
            useEffect(() => {
              setDestructive(!!formState?.errors?.[accessorKey])
            }, [formState])

            return (
              <Controller
                control={control}
                name={accessorKey}
                render={({ field: { onChange, value } }) => {
                  return (
                    <>
                      <FormControlItem
                        destructive={!!formState?.errors?.[accessorKey]}
                        destructiveText={
                          formState?.errors?.[accessorKey]?.message as string
                        }>
                        <RichEditor
                          {...editingConfig?.editor}
                          placeholder={editingConfig?.placeholder}
                          onChange={onChange}
                          content={value as string}
                          destructive={!!formState?.errors?.[accessorKey]}
                        />
                      </FormControlItem>

                      <div className="flex items-center justify-end space-x-3 pt-6">
                        <Button
                          label={editingConfig.editor?.cancel}
                          type="secondary"
                          size="sm"
                          onClick={() =>
                            destroy(editingConfig?.value, { isTriggerUpdated })
                          }
                        />
                        <Button
                          label={editingConfig.editor?.save}
                          size="sm"
                          onClick={() => {
                            if (!isDestructive) {
                              destroy(value, { isTriggerUpdated })
                            }
                          }}
                        />
                      </div>
                    </>
                  )
                }}
              />
            )
          }}
        </DynamicImportForm>
      </Dialog>
    )
  }

  return (
    <>
      <div
        className="absolute z-20 w-[240px]"
        style={{
          top: Number(itemRect?.y || 0) + (editingConfig?.configShow?.top || 0),
          left:
            Number(itemRect?.x) < tableEditingX
              ? tableEditingX
              : Number(itemRect?.x || 0) +
                (editingConfig?.configShow?.left || 0)
        }}>
        <DynamicImportForm
          id="inline-editing-form"
          defaultValue={{
            [accessorKey]: editingConfig?.value as keyof unknown
          }}
          schema={schema}
          noUseSubmit>
          {({ formState, control }) => {
            // eslint-disable-next-line react-hooks/rules-of-hooks
            useEffect(() => {
              setDestructive(!!formState?.errors?.[accessorKey])
            }, [formState])

            return (
              <Controller
                control={control}
                name={accessorKey}
                render={({ field: { onChange, value } }) => {
                  return (
                    <Tooltip
                      classNameConfig={{
                        content: !formState?.errors?.[accessorKey]?.message
                          ? 'hidden'
                          : ''
                      }}
                      content={formState?.errors?.[accessorKey]?.message}
                      position="top"
                      align="end">
                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.input ? (
                        <Input
                          autoFocus
                          placeholder={editingConfig?.placeholder}
                          size="sm"
                          onChange={(newValue) => {
                            onChange(newValue)
                            setValueState(newValue)
                          }}
                          value={value as string | number}
                          onChangeEnter={() =>
                            destroy(
                              isDestructive ? editingConfig?.value : value,
                              { isTriggerUpdated }
                            )
                          }
                          destructive={!!formState?.errors?.[accessorKey]}
                        />
                      ) : null}

                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.inputArray ? (
                        <Input
                          autoFocus
                          placeholder={editingConfig?.placeholder}
                          size="sm"
                          onChange={(newValue) => {
                            onChange(newValue ? [newValue] : [])
                            setValueState(newValue ? [newValue] : [])
                          }}
                          value={
                            value &&
                            (value as unknown as Array<string>)?.length > 0
                              ? value?.[0]
                              : undefined
                          }
                          onChangeEnter={() =>
                            destroy(
                              isDestructive ? editingConfig?.value : value,
                              { isTriggerUpdated }
                            )
                          }
                          destructive={!!formState?.errors?.[accessorKey]}
                        />
                      ) : null}

                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.number ? (
                        <Input
                          autoFocus
                          inputType="number"
                          min={0}
                          placeholder={editingConfig?.placeholder}
                          size="sm"
                          onChange={(newValue: string | number) => {
                            if (/^\d*$/.test(newValue.toString())) {
                              onChange(newValue)
                              setValueState(newValue)
                            }
                          }}
                          value={value as string | number}
                          onChangeEnter={() =>
                            destroy(
                              isDestructive ? editingConfig?.value : value,
                              { isTriggerUpdated }
                            )
                          }
                          destructive={!!formState?.errors?.[accessorKey]}
                        />
                      ) : null}

                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.inputRight ? (
                        <InputGroup
                          size="sm"
                          className="flex"
                          configPadding={{ left: '', right: '' }}>
                          <ComputeInputRightPadding>
                            {({
                              inputStyle,
                              rightInputRef,
                              createOnPaddingChange
                            }) => (
                              <>
                                <Input
                                  style={inputStyle}
                                  autoFocus
                                  inputType="number"
                                  min={0}
                                  placeholder={editingConfig?.placeholder}
                                  size="sm"
                                  onChange={(newValue: string | number) => {
                                    if (/^\d*$/.test(newValue.toString())) {
                                      onChange(newValue)
                                      setValueState(newValue)
                                    }
                                  }}
                                  value={value}
                                  onChangeEnter={() =>
                                    destroy(
                                      {
                                        value: isDestructive
                                          ? editingConfig?.value
                                          : value,
                                        valueRight: valueRightState
                                      },
                                      { isTriggerUpdated }
                                    )
                                  }
                                  destructive={
                                    !!formState?.errors?.[accessorKey]
                                  }
                                  onBlur={() => {
                                    setTimeout(() => {
                                      const container =
                                        document.getElementById(
                                          'app-editing-table'
                                        )
                                      const itemID = container?.querySelector(
                                        "[id^='react-select-'][id$='-listbox']"
                                      )
                                      if (!itemID) {
                                        destroy(
                                          {
                                            value: isDestructive
                                              ? editingConfig?.value
                                              : value,
                                            valueRight: valueRightState
                                          },
                                          { isTriggerUpdated }
                                        )
                                      }
                                    }, 0)
                                  }}
                                />
                                <InputRightElement className="right-0 top-0">
                                  <div ref={rightInputRef}>
                                    <NativeSelect
                                      {...editingConfig.inputRight}
                                      // @ts-ignore
                                      onChange={createOnPaddingChange(
                                        (newValue: ISelectOption) => {
                                          setValueRightState(
                                            newValue as unknown as ISelectOption
                                          )
                                        }
                                      )}
                                      value={valueRightState}
                                    />
                                  </div>
                                </InputRightElement>
                              </>
                            )}
                          </ComputeInputRightPadding>
                        </InputGroup>
                      ) : null}

                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.phone ? (
                        <PhoneInput
                          countryCodeEditable
                          autoFocus
                          country={countryCode}
                          onChange={(newValue) => {
                            onChange(newValue ? [newValue] : [])
                            setValueState(newValue ? [newValue] : [])
                          }}
                          value={
                            value &&
                            (value as unknown as Array<string>)?.length > 0
                              ? value?.[0]
                              : undefined
                          }
                          placeholder={editingConfig?.placeholder}
                          onChangeEnter={() =>
                            destroy(
                              isDestructive ? editingConfig?.value : value,
                              { isTriggerUpdated }
                            )
                          }
                          destructive={!!formState?.errors?.[accessorKey]}
                        />
                      ) : null}

                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.select ? (
                        <ComboboxSelect
                          isClearable={!editingConfig?.select?.isMulti}
                          {...editingConfig?.select}
                          isShowAvatarSingleValue={
                            !!editingConfig?.select?.configSelectOption
                              ?.avatar && !editingConfig?.select?.isMulti
                          }
                          defaultOpenDropdown
                          menuOptionSide="bottom"
                          size="md"
                          dropdownMenuClassName="!w-[240px]"
                          containerMenuClassName="w-[240px]"
                          buttonClassName="w-[240px]"
                          placeholder={editingConfig?.placeholder}
                          //@ts-ignore
                          value={
                            editingConfig?.select?.isMulti
                              ? value
                              : Object.keys(value || {}).length
                              ? value
                              : null
                          }
                          onChange={(newValue, actionMeta) => {
                            if (newValue === undefined) {
                              const formatValue = editingConfig?.select?.isMulti
                                ? []
                                : {}

                              if (editingConfig?.select?.isMulti) {
                                onChange(formatValue)
                                setValueState(formatValue)
                              } else {
                                destroy(formatValue, { isTriggerUpdated })
                              }
                            } else {
                              const isArrayValue = Array.isArray(newValue)
                              const formatValue = isArrayValue
                                ? newValue.map((item) =>
                                    item.__isNew__
                                      ? {
                                          ...item,
                                          supportingObj: {
                                            name: item.label
                                          }
                                        }
                                      : item
                                  )
                                : newValue?.__isNew__
                                ? {
                                    ...newValue,
                                    supportingObj: {
                                      name: newValue.label
                                    }
                                  }
                                : newValue

                              if (editingConfig?.select?.isMulti) {
                                onChange(formatValue)
                                setValueState(formatValue)
                              } else {
                                destroy(formatValue, { isTriggerUpdated })
                              }

                              if (actionMeta?.action === 'create-option') {
                                if (editingConfig?.triggerUpdatedOnCreate) {
                                  setTriggerUpdated(true)
                                }

                                editingConfig.onCreate &&
                                  editingConfig.onCreate(
                                    formatValue,
                                    (newValueCallback) => {
                                      let arr = JSON.parse(
                                        JSON.stringify(formatValue)
                                      )

                                      for (
                                        let i = 0;
                                        i < newValueCallback.length;
                                        i++
                                      ) {
                                        const item = newValueCallback[i]
                                        delete arr[item.index].__isNew__ // Remove is new
                                        arr[item.index].value = item.id
                                      }

                                      onChange(arr)
                                      setValueState(arr)
                                    }
                                  )
                              }
                            }
                          }}
                          destructive={!!formState?.errors?.[accessorKey]}
                          searchPlaceholder={
                            editingConfig?.select?.searchPlaceholder
                          }
                          loadingMessage={editingConfig?.select?.loadingMessage}
                          noOptionsMessage={
                            editingConfig?.select?.noOptionsMessage
                          }
                          onBlur={() =>
                            destroy(
                              isDestructive ? editingConfig?.value : value,
                              { isTriggerUpdated }
                            )
                          }
                        />
                      ) : null}

                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.date ? (
                        <SingleDateWithYearOnlyPicker
                          type={
                            accessorKey === 'birthday' ? 'birthday' : 'default'
                          }
                          config={{
                            onChange: (newValue) => {
                              const formatValue = {
                                year: newValue?.year,
                                month: newValue?.month,
                                date: newValue?.date
                              }

                              onChange(formatValue)
                              setValueState(formatValue)
                            },
                            value: value as DatePickerValue,
                            showClearIndicator: true,
                            onClear: () => {
                              const formatValue = {
                                year: null,
                                month: null,
                                date: null
                              }

                              onChange(formatValue)
                              setValueState(formatValue)
                            }
                          }}
                          defaultTab={
                            !(value as DatePickerValue)?.month &&
                            !(value as DatePickerValue)?.date &&
                            !!(value as DatePickerValue)?.year
                              ? 'year'
                              : 'date'
                          }
                          placeholder={editingConfig?.placeholder}
                          forceOnBlurCallback={() =>
                            destroy(
                              isDestructive ? editingConfig?.value : value,
                              { isTriggerUpdated }
                            )
                          }
                        />
                      ) : null}

                      {editingConfig?.mode ===
                      ENUMS_EDITING_INLINE_MODE_COMPONENT.link ? (
                        <Input
                          autoFocus
                          placeholder={editingConfig?.placeholder}
                          size="sm"
                          onChange={(newValue) => {
                            onChange(newValue)
                            setValueState(newValue)
                          }}
                          value={value as string | number}
                          onChangeEnter={() =>
                            destroy(
                              isDestructive ? editingConfig?.value : value,
                              { isTriggerUpdated }
                            )
                          }
                          destructive={!!formState?.errors?.[accessorKey]}
                        />
                      ) : null}
                    </Tooltip>
                  )
                }}
              />
            )
          }}
        </DynamicImportForm>
      </div>

      {editingConfig?.mode &&
      [
        ENUMS_EDITING_INLINE_MODE_COMPONENT.input,
        ENUMS_EDITING_INLINE_MODE_COMPONENT.inputArray,
        ENUMS_EDITING_INLINE_MODE_COMPONENT.inputRight,
        ENUMS_EDITING_INLINE_MODE_COMPONENT.number,
        ENUMS_EDITING_INLINE_MODE_COMPONENT.phone,
        ENUMS_EDITING_INLINE_MODE_COMPONENT.date,
        ENUMS_EDITING_INLINE_MODE_COMPONENT.link
      ].includes(editingConfig.mode) ? (
        <div
          className="absolute bottom-0 left-0 right-0 top-0 z-[19]"
          onClick={() => {
            if (
              editingConfig?.mode ===
              ENUMS_EDITING_INLINE_MODE_COMPONENT.inputRight
            ) {
              destroy(
                {
                  value: isDestructive ? editingConfig?.value : valueState,
                  valueRight: valueRightState
                },
                { isTriggerUpdated }
              )
            } else {
              destroy(isDestructive ? editingConfig?.value : valueState, {
                isTriggerUpdated
              })
            }
          }}
        />
      ) : null}
    </>
  )
}

const EditingInlineTableBody = (props: EditingInlineTableBodyProps) => {
  const {
    bodyConfig = {
      mode: 'onlyView',
      className: undefined,
      content: ''
    },
    editingConfig,
    accessorKey,
    actions,
    schema
  } = props

  const handleClickEditingInline = async () => {
    let containerTable = document.getElementById('app-editing-table-hidden')
    if (containerTable?.style) {
      containerTable.style.overflow = 'hidden'
      await delayMilliseconds(100)
    }

    const idElement = document.getElementById(
      `${ENUMS_EDITING_INLINE_MODE.editing}-${accessorKey}-${editingConfig?.id}`
    )
    const itemRect = idElement?.getBoundingClientRect()
    const container = document.getElementById('app-editing-table')
    const root = createRoot(container!)

    if (
      editingConfig?.mode === ENUMS_EDITING_INLINE_MODE_COMPONENT.richEditor
    ) {
      root.render(
        <InlineTableComponent
          schema={schema}
          itemRect={itemRect}
          editingConfig={editingConfig}
          accessorKey={accessorKey as never}
          destroy={(newValue, { isTriggerUpdated }) => {
            if (newValue !== editingConfig?.value || isTriggerUpdated) {
              editingConfig?.onChange(newValue || '', accessorKey)
            }

            if (containerTable?.style) {
              containerTable.style.overflow = ''
            }
            setTimeout(() => root.unmount(), 0)
          }}
        />
      )
    } else {
      root.render(
        <InlineTableComponent
          schema={schema}
          itemRect={itemRect}
          editingConfig={editingConfig}
          accessorKey={accessorKey as never}
          destroy={(newValue, { isTriggerUpdated }) => {
            if (Array.isArray(newValue)) {
              if (
                [
                  ENUMS_EDITING_INLINE_MODE_COMPONENT.inputArray,
                  ENUMS_EDITING_INLINE_MODE_COMPONENT.phone
                ].includes(editingConfig?.mode as IModeComponent)
              ) {
                const formatNewValue = newValue as Array<unknown>
                const formatEditingConfig =
                  editingConfig?.value as Array<unknown>
                const compareValue =
                  formatNewValue?.[0] !== formatEditingConfig?.[0]

                if (compareValue || isTriggerUpdated) {
                  editingConfig?.onChange(newValue, accessorKey)
                }
              } else {
                const formatNewValue = newValue as Array<ISelectOption>
                const formatEditingConfig =
                  editingConfig?.value as Array<ISelectOption>
                const mappingNewValueIds = formatNewValue.map(
                  (item) => item.value
                )
                const mappingCurrentValueIds = formatEditingConfig.map(
                  (item) => item.value
                )

                const compareValue =
                  mappingNewValueIds.length !== mappingCurrentValueIds.length ||
                  mappingNewValueIds.some(
                    (id, index) => id !== mappingCurrentValueIds[index]
                  )

                if (compareValue || isTriggerUpdated) {
                  editingConfig?.onChange(newValue, accessorKey)
                }
              }
            } else {
              if (newValue && typeof newValue === 'object') {
                if (
                  editingConfig?.mode ===
                  ENUMS_EDITING_INLINE_MODE_COMPONENT.date
                ) {
                  const formatNewValue = newValue as {
                    date?: number
                    month?: number
                    year?: number
                  }
                  const formatEditingConfig = editingConfig?.value as {
                    date?: number
                    month?: number
                    year?: number
                  }
                  if (
                    formatNewValue?.date !== formatEditingConfig?.date ||
                    formatNewValue?.month !== formatEditingConfig?.month ||
                    formatNewValue?.year !== formatEditingConfig?.year ||
                    isTriggerUpdated
                  ) {
                    editingConfig?.onChange(newValue, accessorKey)
                  }
                } else {
                  const formatValueObject = newValue as {
                    valueRight: ISelectOption
                    value: ISelectOption
                  }

                  if (formatValueObject?.valueRight) {
                    const compareValue =
                      formatValueObject.value?.value !== editingConfig?.value
                    const compareValueRight =
                      // @ts-expect-error
                      formatValueObject.valueRight?.value !==
                      editingConfig?.inputRight?.value

                    if (compareValue || compareValueRight || isTriggerUpdated) {
                      editingConfig?.onChange(formatValueObject, accessorKey)
                    }
                  } else {
                    const formatNewValues = newValue as ISelectOption[]
                    if (formatNewValues?.length) {
                      const mappingNewValue = (formatNewValues || [])?.map(
                        (item: ISelectOption) => item.value
                      )
                      const mappingOldValue = (
                        (editingConfig?.value || []) as ISelectOption[]
                      )?.map((item: ISelectOption) => item.value)
                      const compareValue =
                        String(mappingNewValue) !== String(mappingOldValue)

                      if (compareValue || isTriggerUpdated) {
                        editingConfig?.onChange(newValue, accessorKey)
                      }
                    } else {
                      const formatNewValue = newValue as ISelectOption
                      const formatEditingConfig =
                        editingConfig?.value as ISelectOption
                      const compareValue =
                        formatNewValue.value !== formatEditingConfig?.value

                      if (compareValue || isTriggerUpdated) {
                        editingConfig?.onChange(newValue, accessorKey)
                      }
                    }
                  }
                }
              } else {
                if (newValue !== editingConfig?.value || isTriggerUpdated) {
                  editingConfig?.onChange(newValue || '', accessorKey)
                }
              }
            }

            if (containerTable?.style) {
              containerTable.style.overflow = ''
            }
            setTimeout(() => root.unmount(), 0)
          }}
        />
      )
    }
  }

  const renderActions = () => {
    if (actions?.length) {
      return (
        <div className="absolute bottom-0 right-[2px] top-1/2 z-10 hidden min-h-[28px] -translate-y-1/2 items-center rounded border border-solid border-gray-100 bg-white p-[2px] shadow-actions-new group-hover/inline-table:flex">
          {actions.map((menu, index) => (
            <div key={`action-${index}`} className="relative space-x-[2px]">
              {menu?.subMenus ? (
                <DropdownMenu
                  side="bottom"
                  align="end"
                  menu={menu?.subMenus.map((item, i) => ({
                    label: item.name,
                    icon: item.iconMenus,
                    disabled: item.isDisabled,
                    onClick: () => item.onClick
                  }))}
                  trigger={() => (
                    <div className="cursor-pointer p-1">
                      <IconWrapper
                        name="MoreHorizontal"
                        size={16}
                        className="text-gray-500 dark:text-gray-400"
                      />
                    </div>
                  )}
                />
              ) : (
                <div className="cursor-pointer p-1" onClick={menu.onClick}>
                  <Tooltip mode="icon" content={menu.name}>
                    <IconWrapper
                      name={menu.iconMenus}
                      size={16}
                      className="text-gray-500 dark:text-gray-400"
                    />
                  </Tooltip>
                </div>
              )}
            </div>
          ))}
        </div>
      )
    }

    return null
  }

  if (bodyConfig.mode === ENUMS_EDITING_INLINE_MODE.editing) {
    if (editingConfig?.mode === ENUMS_EDITING_INLINE_MODE_COMPONENT.toggle) {
      return (
        <div
          id={`${ENUMS_EDITING_INLINE_MODE.editing}-${accessorKey}-${editingConfig?.id}`}
          className="group/inline-table relative">
          <div className="overflow-hidden whitespace-nowrap py-[6px] pl-3">
            <Toggle
              size="sm"
              name={accessorKey}
              isChecked={!!editingConfig.value}
              classNameConfig={{
                classNameWrapper: 'flex'
              }}
              onCheckedChange={(checked) => {
                editingConfig?.onChange(checked, accessorKey)
              }}
            />
          </div>
        </div>
      )
    }

    if (editingConfig?.mode === ENUMS_EDITING_INLINE_MODE_COMPONENT.link) {
      return (
        <div
          id={`${ENUMS_EDITING_INLINE_MODE.editing}-${accessorKey}-${editingConfig?.id}`}
          className="group/inline-table relative">
          <div className="flex overflow-hidden whitespace-nowrap py-[6px] pl-3">
            <Tooltip
              content={editingConfig?.isDisabledTooltip}
              classNameAsChild="mr-1 min-h-[20px] min-w-[20px]">
              <DashedButton
                isDisabled={editingConfig?.isDisabled}
                size="sm"
                iconMenus="Plus"
                onClick={handleClickEditingInline}
              />
            </Tooltip>
            {bodyConfig.content}
          </div>
        </div>
      )
    }

    return (
      <div
        id={`${ENUMS_EDITING_INLINE_MODE.editing}-${accessorKey}-${editingConfig?.id}`}
        className="group/inline-table relative">
        {typeof bodyConfig.content === 'string' &&
        isHTML(bodyConfig.content) ? (
          <div
            className="overflow-hidden whitespace-nowrap py-[6px] pl-3 text-sm text-gray-900"
            onClick={() =>
              editingConfig?.mode ===
              ENUMS_EDITING_INLINE_MODE_COMPONENT.richEditor
                ? undefined
                : handleClickEditingInline()
            }
            dangerouslySetInnerHTML={{ __html: bodyConfig.content }}
          />
        ) : (
          <div
            className="overflow-hidden whitespace-nowrap py-[6px] pl-3 text-sm text-gray-900"
            onClick={() =>
              editingConfig?.mode ===
              ENUMS_EDITING_INLINE_MODE_COMPONENT.richEditor
                ? undefined
                : handleClickEditingInline()
            }>
            {bodyConfig.content}
          </div>
        )}

        {editingConfig?.mode ===
        ENUMS_EDITING_INLINE_MODE_COMPONENT.richEditor ? (
          <div className="absolute bottom-0 right-[2px] top-1/2 z-10 hidden min-h-[28px] -translate-y-1/2 items-center rounded border border-solid border-gray-100 bg-white p-[2px] shadow-actions-new group-hover/inline-table:flex">
            <div
              className="cursor-pointer p-1"
              onClick={handleClickEditingInline}>
              <IconWrapper
                name="Edit2"
                size={16}
                className="text-gray-500 dark:text-gray-400"
              />
            </div>
          </div>
        ) : null}

        {renderActions()}
      </div>
    )
  }

  return (
    <div className="group/inline-table relative">
      {typeof bodyConfig.content === 'string' && isHTML(bodyConfig.content) ? (
        <div
          className={cn(
            'overflow-hidden whitespace-nowrap py-[6px] pl-3 text-sm text-gray-900',
            bodyConfig?.className
          )}
          dangerouslySetInnerHTML={{ __html: bodyConfig.content }}
        />
      ) : (
        <div
          className={cn(
            'overflow-hidden whitespace-nowrap py-[6px] pl-3 text-sm text-gray-900',
            bodyConfig?.className
          )}>
          {bodyConfig.content}
        </div>
      )}

      {renderActions()}
    </div>
  )
}

export default EditingInlineTableBody
