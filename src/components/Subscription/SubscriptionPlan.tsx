import { useRouter } from 'next/router'
import { ComponentType, useEffect, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import pathConfiguration from 'src/configuration/path'
import configuration from '~/configuration'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useBoundStore from '~/lib/store'
import LockFeaturePage from './LockFeaturePage'
import { FeatureName } from './subscription'
import useSubscriptionPlan from './useSubscriptionPlan'

const PAGES_SHOW_UPGRADE: string[] = [
  configuration.path.settings.requisitions,
  configuration.path.settings.careerhub,
  `${configuration.path.jobs.list}/[id]`,
  `${configuration.path.candidates.list}/[id]`
]

const PAGES_SHOW_FEATURES: string[] = [
  configuration.path.requisitions.list,
  configuration.path.referral.list
]

export const withSubscriptionPlanLockFearture = <T extends object>(
  Component: ComponentType<T>,
  featureName: FeatureName,
  extraProps?: {
    classNameHeightOfView?: string
  }
) => {
  const WithSubscriptionPlanLockFearture = (props: T) => {
    const { t } = useTranslation()
    const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
    const { user } = useBoundStore()
    const enable = isFeatureEnabled(featureName)
    const isUnLock = isUnLockFeature(featureName) || false
    const router = useRouter()

    const content = useMemo(() => {
      let msg = ''
      if (featureName === PLAN_FEATURE_KEYS.job_board) {
        msg = user.ownTenant
          ? `${t('settings:plan:planContent01')}`
          : `${t('settings:plan:planContent02')}`
      }
      if (featureName === PLAN_FEATURE_KEYS.referral) {
        msg = `${t('settings:plan:planContent03')}`
      }
      if (featureName === PLAN_FEATURE_KEYS.requisition) {
        msg = `${t('settings:plan:planContent04')}`
      }
      if (featureName === PLAN_FEATURE_KEYS.recommendation) {
        msg = router.asPath.includes(pathConfiguration.candidates.list)
          ? `${t('settings:plan:planContent06')}`
          : `${t('settings:plan:planContent05')}`
      }
      if (
        [
          PLAN_FEATURE_KEYS.import_job,
          PLAN_FEATURE_KEYS.import_candidate,
          PLAN_FEATURE_KEYS.learning_management_system
        ].includes(featureName)
      ) {
        msg = `${t('settings:plan:planContent07')}`
      }
      if (featureName === PLAN_FEATURE_KEYS.security_setting) {
        msg = `${t('settings:plan:planContent08')}`
      }
      if (featureName === PLAN_FEATURE_KEYS.audit_logs) {
        msg = `${t('settings:plan:planContent09')}`
      }
      return msg
    }, [])

    useEffect(() => {
      // Handle the case when the user is not the owner, and the feature is turned off at the admin side.
      if (
        ![
          PLAN_FEATURE_KEYS.job_board,
          PLAN_FEATURE_KEYS.recommendation
        ].includes(featureName)
      ) {
        // Handle for case owner - will redirect to 404 page if doesn't match these pages defined
        if (enable === false) {
          router?.push(configuration.path.error404)
          return
        }

        if (enable && !isUnLock && !user?.ownTenant) {
          router?.push(configuration.path.error404)
        }

        // handle for features
        if (
          enable &&
          !isUnLock &&
          PAGES_SHOW_FEATURES.some((url) => router?.pathname.startsWith(url))
        ) {
          router?.push(configuration.path.error404)
        }
      }
    }, [enable])

    if (
      ![PLAN_FEATURE_KEYS.job_board, PLAN_FEATURE_KEYS.recommendation].includes(
        featureName
      )
    ) {
      // condition show in app = false
      // expect: redirect 404
      if (!enable) {
        return null
      }
      // condition show in app = true && unlocked = false && isOwner = false
      // expect: redirect 404
      if (enable && !isUnLock && !user?.ownTenant) {
        return null
      }
      // condition show in app = true && unlocked = false && url includes [requisition, referrals]
      // expect: redirect 404
      if (
        enable &&
        !isUnLock &&
        PAGES_SHOW_FEATURES.some((url) => router?.pathname.startsWith(url))
      ) {
        return null
      }
    }

    if (enable && !isUnLock)
      return (
        <div className="grid">
          <div style={{ gridColumn: 1, gridRow: 1 }}>
            <Component {...(props as T)} suspend={true} />
          </div>

          <div
            style={{
              gridColumn: 1,
              gridRow: 1,
              zIndex: 2
            }}>
            <div
              style={{
                background:
                  'linear-gradient(180deg, rgba(255, 255, 255, 0.62) 0%, #FFF 68.16%)',
                height: '100%'
              }}></div>
          </div>
          <div
            style={{
              gridColumn: 1,
              gridRow: 1,
              zIndex: 3
            }}>
            <LockFeaturePage
              featureName={featureName}
              content={content}
              isHiddenBtn={!user.ownTenant}
              classNameHeightOfView={extraProps?.classNameHeightOfView}
            />
          </div>
        </div>
      )
    return <Component {...(props as T)} suspend={false} />
  }
  return WithSubscriptionPlanLockFearture
}

export const withSubscriptionDataPlanSuspend = <T extends object>(
  Component: ComponentType<T>
) => {
  const WithSubscriptionPlanLockFearture = (props: T) => {
    const { subscriptionDataCache } = useSubscriptionPlan()
    return subscriptionDataCache ? <Component {...(props as T)} /> : <></>
  }
  return WithSubscriptionPlanLockFearture
}

const SubscriptionPlan = {
  // Manager: SubscriptioPlanManager
  // Allowance: SubscriptionPlanAllowance
}
export default SubscriptionPlan
