import { ReactElement, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import useEnumsData from 'src/hooks/data/use-enums-data'
import useStaticData from 'src/hooks/data/use-static-data'
import { FieldSettingType } from '~/components/DisplayConfig'
import Filter, { FilterDataHolderContext } from '~/components/Filter/Filter'
import { IJobCard } from '~/components/Jobs/Listing/JobCard'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { IUserInformation } from '~/core/@types/global'
import { AGENCY_TENANT, LIST_ACTION_FILTERS } from '~/core/constants/enum'
import useQueryGraphQL from '~/core/middleware/use-query-graphQL'
import { Button } from '~/core/ui/Button'
import { CloseButton } from '~/core/ui/CloseButton'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { Dot } from '~/core/ui/Dot'
import { DropdownMenu } from '~/core/ui/DropdownMenu'
import If from '~/core/ui/If'
import { Input } from '~/core/ui/Input'
import { InputRightElement } from '~/core/ui/InputElement'
import { InputGroup } from '~/core/ui/InputGroup'
import { ISelectOption } from '~/core/ui/Select'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { ICompanyPermittedFields } from '~/lib/features/agency/companies/types'
import QueryCompanyJobsFilter from '~/lib/features/candidates/graphql/query-company-jobs-filter'
import QueryJobListFilter from '~/lib/features/candidates/graphql/query-jobs-filter'
import QueryProfileFiltersList from '~/lib/features/candidates/graphql/query-profile-filters-list'
import QuerySkillsList from '~/lib/features/candidates/graphql/query-skills-list'
import QueryStageTypes from '~/lib/features/candidates/graphql/query-stage-types'
import QueryTalentPoolList from '~/lib/features/candidates/graphql/query-talent-pool-list'
import { mappingAdvancedFilterCandidates } from '~/lib/features/candidates/mapping/candidate-filter-mapping'
import useProfileViewDisplayStore from '~/lib/features/candidates/store/profile-view-display-slice'
import {
  ICandidatesFilter,
  IProfileFiltersList
} from '~/lib/features/candidates/types'
import {
  CANDIDATE_CV_FILTER,
  CANDIDATE_STATUS_FILTER,
  CANDIDATE_YES_NO,
  FILTER_CANDIDATE_FIELDS_CONDITION,
  FILTER_CANDIDATE_FIELDS_VALUE,
  ListSuggestNoticeOfPeriod,
  totalYoeOptions
} from '~/lib/features/candidates/utilities/enum'
import { descriptionLocationSelectJob } from '~/lib/features/jobs/utilities/common'
import {
  JOB_DOT_STATUS,
  JOB_STATUS_ENUM
} from '~/lib/features/jobs/utilities/enum'
import QueryTenantDepartment from '~/lib/features/settings/departments/graphql/query-tenant-department'
import { IDepartment } from '~/lib/features/settings/departments/types'
import QueryCompanyListFullOptions from '~/lib/features/settings/locations/graphql/query-company-list-full-options'
import QueryHiringMembers from '~/lib/features/settings/members/graphql/query-member-filter'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import { mappingCustomFieldKind } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import { CustomFieldViewType } from '~/lib/features/settings/profile-fields/types/custom-field'
import QueryTagsList from '~/lib/features/settings/tags/graphql/query-tags-list'
import { TAG_KIND } from '~/lib/features/settings/tags/utilities/enum'
import QueryCountryStates from '~/lib/graphql/query-country-states'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

const FilterCandidateListingConfig = ({
  user,
  filters,
  configUserDisplay,
  children,
  mappingsProfileField,
  setActionOnFilters,
  filterVariable
}: {
  user?: IUserInformation
  filters?: ICandidatesFilter
  configUserDisplay?: FieldSettingType[]
  children?: ReactElement | null
  mappingsProfileField?: CustomFieldViewType[]
  setActionOnFilters?: (val: string) => void
  filterVariable?: ICandidatesFilter
}) => {
  const { t, i18n } = useTranslation()
  const { profileViewDisplay, setOpenProfileViewModal } =
    useProfileViewDisplayStore()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { data, trigger: fetchProfileFilters } = useQueryGraphQL({
    query: QueryProfileFiltersList,
    variables: { id: null },
    shouldPause: true
  })

  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const profileLevel = useEnumsData({
    enumType: 'ProfileProfileLevel',
    locale: i18n.language
  })
  const profileSourced = useEnumsData({
    enumType: 'ProfileSourced',
    locale: i18n.language
  })
  const languageCollection = useStaticData({
    keyName: 'languages',
    locale: i18n.language
  })
  const [openDropdown, setOpenDropdown] = useState<boolean>(false)
  const [filterConditions, setFilterConditions] = useState<
    {
      id?: String
      field?: string
      direction?: string
      directions?: Array<string>
    }[]
  >([])
  const [isViewOwner, setIsViewOwner] = useState<boolean>(false)
  const { onClearFilterSubmit, getFilterItemValue, onFilterItemChange } =
    useContext(FilterDataHolderContext)

  const profileFiltersList = (data?.profileFiltersList ||
    []) as IProfileFiltersList[]
  const FILTER_CANDIDATE_FIELDS = profileFiltersList
    .filter((item) => item)
    .map((item) => ({
      id:
        item.field_type === 'custom_field'
          ? String(
              `${mappingCustomFieldKind(String(item.field_kind))}_${item.id}`
            )
          : item.field_type,
      value:
        item.field_type === 'custom_field'
          ? `custom_field_${mappingCustomFieldKind(String(item.field_kind))}_${
              item.id
            }`
          : item.object,
      label: item.direction,
      supportingObj: {
        name: item.field_name
      }
    })) as ISelectOption[]

  const mappingFieldIds = filterConditions.map((item) => String(item?.field))
  const filterFieldList = FILTER_CANDIDATE_FIELDS.filter(
    (item) => !mappingFieldIds.includes(String(item.value))
  )
  let filterKeyChange = Object.keys(filters || {}).filter(
    (field) => filters?.[field as keyof ICandidatesFilter]
  )
  const filterChange = filterKeyChange.filter(
    (field) =>
      ![
        'page',
        'search',
        'operator',
        'fieldsFilter',
        'isFilterTouched',
        'sorting',
        'isSubmitCount'
      ].includes(field)
  )

  useEffect(() => {
    if (openDropdown && !data?.profileFiltersList) {
      fetchProfileFilters()
    }

    if (
      openDropdown &&
      data?.profileFiltersList &&
      profileViewDisplay?.profileFilters?.fieldsFilter
    ) {
      const newConditions =
        profileViewDisplay?.profileFilters?.fieldsFilter.map((con) => {
          const _newDirections = profileFiltersList.find((field) => {
            if (con.fieldType === 'custom_field') return field.id === con.id
            return field.object === con.object
          })

          return {
            id:
              con.fieldType === 'custom_field'
                ? String(
                    `${mappingCustomFieldKind(String(con.fieldKind))}_${con.id}`
                  )
                : con.fieldType,
            field:
              con.fieldType === 'custom_field'
                ? String(
                    `${con.fieldType}_${mappingCustomFieldKind(
                      String(con.fieldKind)
                    )}_${con.id}`
                  )
                : con.object,
            direction: con.direction,
            directions: _newDirections?.direction
          }
        })

      setFilterConditions(newConditions)
    }
  }, [openDropdown, data])

  useEffect(() => {
    setIsViewOwner(Number(user?.id) === Number(profileViewDisplay?.createdById))
    if (!profileViewDisplay?.profileFilters?.fieldsFilter)
      setFilterConditions([])
  }, [user, profileViewDisplay])

  useEffect(() => {
    if (openDropdown) setOpenDropdown(false)
  }, [profileViewDisplay])

  const onSaveAsNewView = () => {
    let relatedObjects = {}
    const idFilters = filterConditions.map((item) => String(item?.field))

    idFilters.map((id) => {
      relatedObjects = {
        ...relatedObjects,
        [id]: getFilterItemValue && getFilterItemValue(id)
      }
    })
    const params = mappingAdvancedFilterCandidates(
      {
        ...relatedObjects,
        fieldsFilter: filterConditions,
        operator: getFilterItemValue && getFilterItemValue('operator')
      },
      user
    )

    setOpenProfileViewModal({
      open: true,
      defaultValue: {
        isRedirect: true,
        profileFilters: {
          operator: params.operator,
          fieldsFilter: params.fieldsFilter,
          relatedObjects: params.relatedObjects
        }
      }
    })
  }

  return (
    <>
      <div className="relative z-50">
        <Button
          label={`${t('button:filter')}`}
          iconMenus="Filter"
          iconDotMenus={
            filterChange.length ||
            profileViewDisplay?.profileFilters?.fieldsFilter?.length ? (
              <div className="absolute -right-[3px] -top-[3px] rounded border border-white">
                <Dot color="red" className="block" />
              </div>
            ) : null
          }
          size="xs"
          type="secondary"
          onClick={() => {
            if (!openDropdown) {
              setOpenDropdown(true)

              if (filterChange.length) {
                setFilterConditions(filters?.fieldsFilter || [])
              } else {
                if (!filterConditions.length)
                  setFilterConditions([
                    {
                      field: undefined
                    }
                  ])
              }
            }
          }}
        />

        {openDropdown ? (
          <div className="absolute right-0 z-50 mt-1 max-h-[580px] w-[740px] rounded bg-white shadow-[0px_0px_0px_1px_rgba(0,0,0,0.05),0px_4px_6px_-2px_rgba(0,0,0,0.05),0px_10px_15px_-3px_rgba(0,0,0,0.10)]">
            <div className="py-3">
              <div className="flex items-center justify-between px-5">
                <TypographyText className="text-base font-medium text-gray-900">
                  {t('button:filters')}
                </TypographyText>

                <CloseButton onClick={() => setOpenDropdown(false)} />
              </div>

              <div className="scrollbar-macos max-h-[472px] min-w-[700px] space-y-2 overflow-y-auto px-5 py-3">
                {filterConditions.map((condition, index) => {
                  const {
                    id: customField,
                    field,
                    direction,
                    directions
                  } = condition

                  // Prepare for custom field
                  const splitCustomField = customField?.split('_')
                  const customFieldId = splitCustomField?.[1]
                  const customFieldKind = splitCustomField?.[0]
                  const mergeViewData = [...(mappingsProfileField || [])]
                  const selectOptions = mergeViewData
                    ?.find((item) => String(item.id) === String(customFieldId))
                    ?.selectOptions?.map((item) => ({
                      value: item.value,
                      supportingObj: {
                        name: item.supportingObj?.name
                      }
                    })) as ISelectOption[]

                  const maxWidthItem =
                    index > 0
                      ? 'max-w-[296px]'
                      : filterConditions.length > 1
                      ? 'max-w-[296px]'
                      : 'max-w-[364px]'

                  return (
                    <div
                      key={`condition-field-${index}`}
                      className="flex space-x-2">
                      {index > 0 ? (
                        <div className="flex h-8 w-[60px] min-w-[60px] items-center">
                          <Filter.Item<string>
                            triggerFilterChange={false}
                            name={FILTER_CANDIDATE_FIELDS_VALUE.operator}>
                            {({ value, onChange }) => {
                              if (index !== 1) {
                                return (
                                  <TextButton
                                    isDisabled
                                    type="secondary"
                                    size="sm"
                                    underline={false}
                                    icon="trailing"
                                    label={`${t(
                                      `candidates:filterOperator:${value}`
                                    )}`}
                                  />
                                )
                              }

                              return (
                                <DropdownMenu
                                  side="bottom"
                                  align="start"
                                  menuClassName="w-[100px] z-[51]"
                                  trigger={
                                    <TextButton
                                      type="secondary"
                                      size="sm"
                                      underline={false}
                                      iconMenus="ChevronDown"
                                      icon="trailing"
                                      label={`${t(
                                        `candidates:filterOperator:${value}`
                                      )}`}
                                    />
                                  }
                                  showCheckedIcon
                                  menu={[
                                    {
                                      label: `${t(
                                        'candidates:filterOperator:and'
                                      )}`,
                                      isChecked: value === 'and',
                                      onClick: () => onChange('and')
                                    },
                                    {
                                      label: `${t(
                                        'candidates:filterOperator:or'
                                      )}`,
                                      isChecked: value === 'or',
                                      onClick: () => onChange('or')
                                    }
                                  ]}
                                />
                              )
                            }}
                          </Filter.Item>
                        </div>
                      ) : filterConditions.length > 1 ? (
                        <div className="flex h-8 w-[60px] min-w-[60px] items-center">
                          <TextButton
                            isDisabled
                            type="secondary"
                            size="sm"
                            underline={false}
                            icon="trailing"
                            label={`${t('candidates:filterOperator:where')}`}
                          />
                        </div>
                      ) : null}

                      <div className="w-[150px] min-w-[150px]">
                        <ComboboxSelect
                          closeOnSelect
                          isSearchable
                          size="md"
                          buttonClassName={`w-full ${maxWidthItem}`}
                          buttonFontWeightClassName="font-normal"
                          options={filterFieldList}
                          dropdownMenuClassName="min-w-[260px]"
                          containerMenuClassName="max-w-[260px]"
                          isClearable={false}
                          value={FILTER_CANDIDATE_FIELDS.find(
                            (item) => String(item.value) === String(field)
                          )}
                          onChange={(option) => {
                            const singleValue = option as ISelectOption
                            const cloneValue = JSON.parse(
                              JSON.stringify(filterConditions)
                            )
                            cloneValue[index].id = singleValue.id
                            cloneValue[index].field = singleValue.value
                            cloneValue[index].direction = singleValue.label?.[0]
                            cloneValue[index].directions = singleValue.label

                            setFilterConditions(cloneValue)
                            onFilterItemChange &&
                              onFilterItemChange(String(field), undefined, {
                                triggerFilterChange: false
                              })
                          }}
                          placeholder={`${t('button:filter')}`}
                          searchPlaceholder={`${t('label:placeholder:search')}`}
                          loadingMessage={`${t('label:loading')}`}
                          noOptionsMessage={`${t('label:noOptions')}`}
                          menuOptionSide="bottom"
                        />
                      </div>

                      <div className="flex flex-1 space-x-2">
                        <div className="w-[130px] min-w-[130px]">
                          <ComboboxSelect
                            closeOnSelect
                            isSearchable={false}
                            size="md"
                            menuOptionSide="bottom"
                            buttonClassName={`w-full ${maxWidthItem}`}
                            buttonFontWeightClassName="font-normal"
                            options={directions?.map((item) => ({
                              value: item,
                              supportingObj: {
                                name: `${t(
                                  `candidates:filterOperatorCondition:${item}`
                                )}`
                              }
                            }))}
                            dropdownMenuClassName="min-w-[130px] w-[130px]"
                            isClearable={false}
                            value={directions
                              ?.map((item) => ({
                                value: item,
                                supportingObj: {
                                  name: `${t(
                                    `candidates:filterOperatorCondition:${item}`
                                  )}`
                                }
                              }))
                              ?.find(
                                (item) =>
                                  String(item.value) === String(direction)
                              )}
                            onChange={(option) => {
                              const singleValue = option as ISelectOption
                              const cloneValue = JSON.parse(
                                JSON.stringify(filterConditions)
                              )
                              cloneValue[index].direction = singleValue.value

                              setFilterConditions(cloneValue)
                              if (
                                singleValue.value === 'range' ||
                                direction === 'range'
                              ) {
                                onFilterItemChange &&
                                  onFilterItemChange(String(field), undefined, {
                                    triggerFilterChange: false
                                  })
                              }
                            }}
                            placeholder={`${t('button:condition')}`}
                            searchPlaceholder={`${t(
                              'label:placeholder:search'
                            )}`}
                            loadingMessage={`${t('label:loading')}`}
                            noOptionsMessage={`${t('label:noOptions')}`}
                          />
                        </div>

                        <div className="flex-1">
                          {![
                            FILTER_CANDIDATE_FIELDS_CONDITION.isEmpty,
                            FILTER_CANDIDATE_FIELDS_CONDITION.isNotEmpty
                          ].includes(String(direction)) ? (
                            <>
                              {field ? (
                                <>
                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.job_id ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      optionsFromDocumentNode={{
                                        documentNode: isCompanyKind
                                          ? QueryCompanyJobsFilter
                                          : QueryJobListFilter,
                                        variable: (search) => ({
                                          ...search,
                                          status: [
                                            JOB_STATUS_ENUM.publish,
                                            JOB_STATUS_ENUM.internal,
                                            JOB_STATUS_ENUM.archived
                                          ]
                                        }),
                                        mapping: (data) => {
                                          const isFilterJobArchived =
                                            configUserDisplay?.find(
                                              (config) =>
                                                config?.value === 'jobArchived'
                                            )?.visibleValue
                                          return {
                                            metadata: data?.jobsList.metadata,
                                            collection: [
                                              ...(data.jobsList.metadata
                                                .currentPage === 1
                                                ? [
                                                    {
                                                      value: '0',
                                                      dot: 'white',
                                                      supportingObj: {
                                                        name: `${t(
                                                          'label:any_open_job'
                                                        )}`,
                                                        description: `${
                                                          data?.jobsList
                                                            .metadata?.extras
                                                            ?.openJobsCount
                                                        } ${t('label:jobs')}`
                                                      }
                                                    }
                                                  ]
                                                : []),
                                              ...data?.jobsList.collection.map(
                                                (job: IJobCard) => ({
                                                  value: String(job.id),
                                                  dot: JOB_DOT_STATUS(
                                                    job?.status || 'gray'
                                                  ),
                                                  dotDescription:
                                                    job?.statusDescription,
                                                  supportingObj: {
                                                    name: job.title,
                                                    shortName: job.title,
                                                    description: isCompanyKind
                                                      ? job?.company
                                                          ?.permittedFields
                                                          ?.name?.value
                                                      : job?.department?.name,
                                                    descriptionHelpName:
                                                      descriptionLocationSelectJob(
                                                        job?.jobLocations,
                                                        t
                                                      )
                                                  }
                                                })
                                              )
                                            ]
                                          }
                                        }
                                      }}
                                      size="md"
                                      configSelectOption={{
                                        dot: true
                                      }}
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.job_id
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.applicant_statuses ? (
                                    <Filter.Combobox
                                      triggerFilterChange={false}
                                      isMulti
                                      isSearchable={false}
                                      options={CANDIDATE_STATUS_FILTER.map(
                                        (item) => ({
                                          ...item,
                                          supportingObj: {
                                            name: `${t(`button:${item.value}`)}`
                                          }
                                        })
                                      )}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      countName={`${t(
                                        'label:placeholder:status'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.applicant_statuses
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.stage_type_id ? (
                                    <Filter.Combobox
                                      isMulti
                                      triggerFilterChange={false}
                                      size="md"
                                      isSearchable={false}
                                      optionsFromDocumentNode={{
                                        documentNode: QueryStageTypes,
                                        variable: { limit: 10000, page: 1 },
                                        mapping: (data) => {
                                          return {
                                            metadata: data.stageTypes.metadata,
                                            collection:
                                              data?.stageTypes.collection.map(
                                                (stage) => ({
                                                  value: String(stage.id),
                                                  supportingObj: {
                                                    name: stage.label
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.stage_type_id
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.tagged_ids ? (
                                    <Filter.Combobox
                                      triggerFilterChange={false}
                                      size="md"
                                      isSearchable={true}
                                      isMulti
                                      countName={`${t(
                                        'label:placeholder:tags'
                                      )}`}
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                      optionsFromDocumentNode={{
                                        documentNode: QueryTagsList,
                                        variable: (searchParams) => ({
                                          ...searchParams,
                                          sortByAlphabet: 'true',
                                          kind: TAG_KIND.profile
                                        }),
                                        mapping: (data) => {
                                          return {
                                            metadata: data.tagsList.metadata,
                                            collection:
                                              data?.tagsList.collection.map(
                                                (tag) => ({
                                                  value: String(tag.id),
                                                  supportingObj: {
                                                    name: tag.name
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.tagged_ids
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.department_ids ? (
                                    <Filter.Combobox
                                      triggerFilterChange={false}
                                      isSearchable={true}
                                      countName={`${t(
                                        'label:placeholder:departments'
                                      )}`}
                                      size="md"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                      isMulti
                                      isMultiGroup
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.department_ids
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      containerMenuClassName="max-w-[428px]"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      optionsFromDocumentNode={{
                                        documentNode: QueryTenantDepartment,
                                        variable: (params) => ({
                                          ...params,
                                          limit: 25
                                        }),
                                        mapping: (data) => {
                                          const cloneData =
                                            data?.tenantDepartmentsList.collection.map(
                                              (item: IDepartment) => {
                                                return {
                                                  id: item.id,
                                                  name: item.name,
                                                  parentId: item.parentId,
                                                  subordinates:
                                                    item.subordinates
                                                }
                                              }
                                            )

                                          const newCloneData =
                                            [] as Array<ISelectOption>
                                          cloneData.forEach((item) => {
                                            newCloneData.push({
                                              value: String(item.id),
                                              parentId: undefined,
                                              supportingObj: {
                                                name: item.name || ''
                                              }
                                            })

                                            if (item.subordinates?.length) {
                                              item.subordinates.forEach(
                                                (sub) => {
                                                  newCloneData.push({
                                                    value: String(sub.id),
                                                    parentId: String(item.id),
                                                    supportingObj: {
                                                      name: sub.name || ''
                                                    }
                                                  })
                                                }
                                              )
                                            }
                                          })
                                          return {
                                            metadata:
                                              data.tenantDepartmentsList
                                                .metadata,
                                            collection: newCloneData
                                          }
                                        }
                                      }}
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.company_id ? (
                                    <Filter.Combobox
                                      triggerFilterChange={false}
                                      optionsFromDocumentNode={{
                                        documentNode:
                                          QueryCompanyListFullOptions,
                                        variable: (searchParams) =>
                                          searchParams,
                                        mapping: (data) => {
                                          return {
                                            metadata:
                                              data?.jobCreatingCompaniesList
                                                .metadata,
                                            collection:
                                              data?.jobCreatingCompaniesList.collection.map(
                                                (company: {
                                                  id: number
                                                  permittedFields?: ICompanyPermittedFields
                                                }) => ({
                                                  value: company.id,
                                                  supportingObj: {
                                                    name: company
                                                      ?.permittedFields?.name
                                                      ?.value
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      size="md"
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                      avatarToolTipPosition="bottom"
                                      toolTipPositionAvatarCount="bottom"
                                      tooltipAlignAvatarCount="end"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.company_id
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.owner_id ? (
                                    <Filter.Combobox
                                      triggerFilterChange={false}
                                      isMulti
                                      optionsFromDocumentNode={{
                                        documentNode: QueryHiringMembers,
                                        variable: (searchParams) =>
                                          searchParams,
                                        mapping: (data) => {
                                          return {
                                            metadata:
                                              data?.membersList.metadata,
                                            collection:
                                              data?.membersList.collection.map(
                                                (member) => ({
                                                  value: String(member.id),
                                                  avatar:
                                                    member?.avatarVariants
                                                      ?.thumb?.url,
                                                  avatarVariants:
                                                    member.avatarVariants,
                                                  supportingObj: {
                                                    name: member.fullName,
                                                    defaultColour:
                                                      member.defaultColour
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      size="md"
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                      avatarToolTipPosition="bottom"
                                      toolTipPositionAvatarCount="bottom"
                                      tooltipAlignAvatarCount="end"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.owner_id
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.sourced ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      isSearchable={false}
                                      options={profileSourced}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.sourced
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                    FILTER_CANDIDATE_FIELDS_VALUE.created_at &&
                                  direction !== 'range' ? (
                                    <Filter.SingleDatePicker
                                      triggerFilterChange={false}
                                      size="md"
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.created_at
                                      }
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      config={{
                                        showOutsideDays: false,
                                        fromYear: 1920
                                      }}
                                      iconMenus="Calendar"
                                    />
                                  ) : null}

                                  {field ===
                                    FILTER_CANDIDATE_FIELDS_VALUE.created_at &&
                                  direction === 'range' ? (
                                    <Filter.RangeDatePicker
                                      triggerFilterChange={false}
                                      size="md"
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.created_at
                                      }
                                      placeholder={`${t(
                                        'label:placeholder:formTo'
                                      )}`}
                                      config={{
                                        showOutsideDays: false,
                                        fromYear: 1920
                                      }}
                                      classNameInput={`${maxWidthItem} truncate`}
                                      iconMenus="Calendar"
                                    />
                                  ) : null}

                                  {field ===
                                    FILTER_CANDIDATE_FIELDS_VALUE.updated_at &&
                                  direction !== 'range' ? (
                                    <Filter.SingleDatePicker
                                      triggerFilterChange={false}
                                      size="md"
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.updated_at
                                      }
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      config={{
                                        showOutsideDays: false,
                                        fromYear: 1920
                                      }}
                                      iconMenus="Calendar"
                                    />
                                  ) : null}

                                  {field ===
                                    FILTER_CANDIDATE_FIELDS_VALUE.updated_at &&
                                  direction === 'range' ? (
                                    <Filter.RangeDatePicker
                                      triggerFilterChange={false}
                                      size="md"
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.updated_at
                                      }
                                      placeholder={`${t(
                                        'label:placeholder:formTo'
                                      )}`}
                                      config={{
                                        showOutsideDays: false,
                                        fromYear: 1920
                                      }}
                                      classNameInput={`${maxWidthItem} truncate`}
                                      iconMenus="Calendar"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.preferred_work_state_ids ? (
                                    <Filter.Combobox
                                      isMulti
                                      triggerFilterChange={false}
                                      menuOptionSide="bottom"
                                      isSearchable={true}
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      size="md"
                                      configSelectOption={{
                                        supportingText: ['name']
                                      }}
                                      optionsFromDocumentNode={{
                                        documentNode: QueryCountryStates,
                                        variable: (searchParams) => ({
                                          ...searchParams
                                        }),
                                        mapping: (data) => {
                                          return {
                                            metadata:
                                              data?.countryStates.metadata,
                                            collection:
                                              data?.countryStates.collection.map(
                                                (countryStates) => ({
                                                  value: String(
                                                    countryStates.id
                                                  ),
                                                  supportingObj: {
                                                    name: countryStates.fullName
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.preferred_work_state_ids
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.profile_talent_pool_ids ? (
                                    <Filter.Combobox
                                      isMulti
                                      triggerFilterChange={false}
                                      menuOptionSide="bottom"
                                      isSearchable={true}
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      size="md"
                                      configSelectOption={{
                                        supportingText: ['name']
                                      }}
                                      optionsFromDocumentNode={{
                                        documentNode: QueryTalentPoolList,
                                        variable: (searchParams) => ({
                                          ...searchParams,
                                          status: 'active'
                                        }),
                                        mapping: (data) => {
                                          return {
                                            metadata:
                                              data?.talentPoolsList.metadata,
                                            collection:
                                              data?.talentPoolsList.collection.map(
                                                (talentPool) => ({
                                                  value: String(talentPool.id),
                                                  supportingObj: {
                                                    name: talentPool.name
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.profile_talent_pool_ids
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.full_name ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:input'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.full_name
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.public_id ? (
                                    <Filter.MultipleSelect
                                      triggerFilterChange={false}
                                      formatIdsTag
                                      showDropdownIndicator={false}
                                      isClearable={false}
                                      size="sm"
                                      creatable={false}
                                      placeholder={`${t(
                                        'label:placeholder:fieldFormat',
                                        { field: 'ID' }
                                      )}`}
                                      classNameOverride={{
                                        loadingMessage: `${t('label:loading')}`,
                                        noOptionsMessage: `${t(
                                          'label:noOptions'
                                        )}`,
                                        control: 'max-h-[60px] overflow-y-auto'
                                      }}
                                      noOptionsMessage={() => null}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.public_id
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.email ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:input'
                                      )}`}
                                      name={FILTER_CANDIDATE_FIELDS_VALUE.email}
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.links ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:input'
                                      )}`}
                                      name={FILTER_CANDIDATE_FIELDS_VALUE.links}
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.phone_number ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      inputType="number"
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:input'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.phone_number
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.country_state_id ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      menuOptionSide="bottom"
                                      isSearchable={true}
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      size="md"
                                      countName={`${t(
                                        'label:placeholder:locations'
                                      )}`}
                                      configSelectOption={{
                                        supportingText: ['name']
                                      }}
                                      optionsFromDocumentNode={{
                                        documentNode: QueryCountryStates,
                                        variable: (searchParams) => ({
                                          ...searchParams,
                                          stateOnly: true,
                                          defaultShow: true
                                        }),
                                        mapping: (data) => {
                                          return {
                                            metadata:
                                              data?.countryStates.metadata,
                                            collection:
                                              data?.countryStates.collection.map(
                                                (location) => ({
                                                  value: String(location.id),
                                                  supportingObj: {
                                                    name: location.fullName,
                                                    description:
                                                      location.country.name,
                                                    descriptionHelpName:
                                                      location.name
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.country_state_id
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.profile_level ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      isSearchable={false}
                                      options={profileLevel}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.profile_level
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.profile_cvs_empty ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      isSearchable={false}
                                      options={CANDIDATE_CV_FILTER.map(
                                        (item) => ({
                                          ...item,
                                          supportingObj: {
                                            name: `${t(`button:${item.value}`)}`
                                          }
                                        })
                                      )}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.profile_cvs_empty
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.total_years_of_exp ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      isSearchable={false}
                                      options={totalYoeOptions.map((item) => ({
                                        ...item,
                                        supportingObj: {
                                          name: `${t(
                                            `candidates:yoeOptions:${item.value}`
                                          )}`
                                        }
                                      }))}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.total_years_of_exp
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.notice_to_period_days ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      isSearchable={false}
                                      options={ListSuggestNoticeOfPeriod.map(
                                        (item) => ({
                                          value: `${item.value} ${t(
                                            'label:days'
                                          )}`,
                                          supportingObj: {
                                            name: `${item.value} ${t(
                                              'label:days'
                                            )}`
                                          }
                                        })
                                      )}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.notice_to_period_days
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.willing_to_relocate ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      isSearchable={false}
                                      options={CANDIDATE_YES_NO.map((item) => ({
                                        ...item,
                                        supportingObj: {
                                          name: `${t(`button:${item.value}`)}`
                                        }
                                      }))}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.willing_to_relocate
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.nationality ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:input'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.nationality
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.expected_salary ? (
                                    <InputGroup size="sm">
                                      <Filter.Input
                                        triggerFilterChange={false}
                                        inputType="number"
                                        maxLength={50}
                                        size="sm"
                                        placeholder={`${t(
                                          'label:placeholder:input'
                                        )}`}
                                        name={
                                          FILTER_CANDIDATE_FIELDS_VALUE.expected_salary
                                        }
                                      />
                                      <InputRightElement className="right-[35px]">
                                        <div className="flex h-[30px] items-center">
                                          <TypographyText className="text-sm text-gray-500">
                                            {`${t(
                                              'candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalaryMonthly'
                                            )} (${
                                              user?.currentTenant?.currency
                                            })`}
                                          </TypographyText>
                                        </div>
                                      </InputRightElement>
                                    </InputGroup>
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.languages ? (
                                    <Filter.Combobox
                                      windowMenuList={{
                                        width: 420,
                                        height: 32
                                      }}
                                      triggerFilterChange={false}
                                      isSearchable
                                      isMulti
                                      options={languageCollection.map(
                                        (item: {
                                          language: string
                                          code: string
                                        }) => {
                                          return {
                                            value: item.code,
                                            supportingObj: {
                                              name: item.language
                                            }
                                          }
                                        }
                                      )}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.languages
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.headline ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:input'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.headline
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.summary ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:input'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.summary
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.open_to_work ? (
                                    <Filter.Combobox
                                      closeOnSelect
                                      triggerFilterChange={false}
                                      isSearchable={false}
                                      options={CANDIDATE_YES_NO.map((item) => ({
                                        ...item,
                                        supportingObj: {
                                          name: `${t(`button:${item.value}`)}`
                                        }
                                      }))}
                                      size="md"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.open_to_work
                                      }
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.skills ? (
                                    <Filter.Combobox
                                      triggerFilterChange={false}
                                      isMulti
                                      optionsFromDocumentNode={{
                                        documentNode: QuerySkillsList,
                                        variable: (searchParams) =>
                                          searchParams,
                                        mapping: (data) => {
                                          return {
                                            metadata: data?.skillsList.metadata,
                                            collection:
                                              data?.skillsList.collection.map(
                                                (member) => ({
                                                  value: String(member.id),
                                                  supportingObj: {
                                                    name: member.name
                                                  }
                                                })
                                              )
                                          }
                                        }
                                      }}
                                      size="md"
                                      buttonClassName={`w-full ${maxWidthItem}`}
                                      buttonFontWeightClassName="font-normal"
                                      dropdownMenuClassName="!min-w-[428px]"
                                      containerMenuClassName="max-w-[428px]"
                                      menuOptionAlign="end"
                                      menuOptionSide="bottom"
                                      avatarToolTipPosition="bottom"
                                      toolTipPositionAvatarCount="bottom"
                                      tooltipAlignAvatarCount="end"
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      searchPlaceholder={`${t(
                                        'label:placeholder:search'
                                      )}`}
                                      loadingMessage={`${t('label:loading')}`}
                                      noOptionsMessage={`${t(
                                        'label:noOptions'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.skills
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                    FILTER_CANDIDATE_FIELDS_VALUE.birthday &&
                                  direction !== 'range' ? (
                                    <Filter.DateWithYearOnlyPicker
                                      triggerFilterChange={false}
                                      size="md"
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.birthday
                                      }
                                      placeholder={`${t(
                                        'label:placeholder:select'
                                      )}`}
                                      config={{
                                        showOutsideDays: false,
                                        fromYear: 1920
                                      }}
                                    />
                                  ) : null}

                                  {field ===
                                    FILTER_CANDIDATE_FIELDS_VALUE.birthday &&
                                  direction === 'range' ? (
                                    <Filter.RangeDatePicker
                                      triggerFilterChange={false}
                                      size="md"
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.birthday
                                      }
                                      placeholder={`${t(
                                        'label:placeholder:formTo'
                                      )}`}
                                      config={{
                                        showOutsideDays: false,
                                        fromYear: 1920
                                      }}
                                      classNameInput={`${maxWidthItem} truncate`}
                                      iconMenus="Calendar"
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.current_salary ? (
                                    <InputGroup size="sm">
                                      <Filter.Input
                                        triggerFilterChange={false}
                                        inputType="number"
                                        maxLength={50}
                                        size="sm"
                                        placeholder={`${t(
                                          'label:placeholder:input'
                                        )}`}
                                        name={
                                          FILTER_CANDIDATE_FIELDS_VALUE.current_salary
                                        }
                                      />
                                      <InputRightElement className="right-[35px]">
                                        <div className="flex h-[30px] items-center">
                                          <TypographyText className="text-sm text-gray-500">
                                            {`${t(
                                              'candidates:tabs:candidateOverview:profileInformation:typeOfCurrentSalaryMonthly'
                                            )} (${
                                              user?.currentTenant?.currency
                                            })`}
                                          </TypographyText>
                                        </div>
                                      </InputRightElement>
                                    </InputGroup>
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.educations ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:inputSchoolName'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.educations
                                      }
                                    />
                                  ) : null}

                                  {field ===
                                  FILTER_CANDIDATE_FIELDS_VALUE.work_experiences ? (
                                    <Filter.Input
                                      triggerFilterChange={false}
                                      size="sm"
                                      placeholder={`${t(
                                        'label:placeholder:inputCompanyName'
                                      )}`}
                                      name={
                                        FILTER_CANDIDATE_FIELDS_VALUE.work_experiences
                                      }
                                    />
                                  ) : null}

                                  {field === `custom_field_${customField}` ? (
                                    <>
                                      {customFieldKind === 'text' ? (
                                        <Filter.Input
                                          triggerFilterChange={false}
                                          size="sm"
                                          placeholder={`${t(
                                            'label:placeholder:input'
                                          )}`}
                                          name={field}
                                        />
                                      ) : null}

                                      {customFieldKind === 'number' ? (
                                        <Filter.Input
                                          triggerFilterChange={false}
                                          inputType="number"
                                          maxLength={50}
                                          size="sm"
                                          placeholder={`${t(
                                            'label:placeholder:input'
                                          )}`}
                                          name={field}
                                        />
                                      ) : null}

                                      {customFieldKind === 'paragraph' ? (
                                        <Filter.Input
                                          triggerFilterChange={false}
                                          size="sm"
                                          placeholder={`${t(
                                            'label:placeholder:input'
                                          )}`}
                                          name={field}
                                        />
                                      ) : null}

                                      {customFieldKind === 'toggle' ? (
                                        <Filter.Combobox
                                          closeOnSelect
                                          triggerFilterChange={false}
                                          isSearchable={false}
                                          options={CANDIDATE_YES_NO.map(
                                            (item) => ({
                                              ...item,
                                              supportingObj: {
                                                name: `${t(
                                                  `button:${item.value}`
                                                )}`
                                              }
                                            })
                                          )}
                                          size="md"
                                          placeholder={`${t(
                                            'label:placeholder:select'
                                          )}`}
                                          searchPlaceholder={`${t(
                                            'label:placeholder:search'
                                          )}`}
                                          loadingMessage={`${t(
                                            'label:loading'
                                          )}`}
                                          noOptionsMessage={`${t(
                                            'label:noOptions'
                                          )}`}
                                          name={field}
                                          buttonClassName={`w-full ${maxWidthItem}`}
                                          buttonFontWeightClassName="font-normal"
                                          dropdownMenuClassName="!min-w-[428px]"
                                          containerMenuClassName="max-w-[428px]"
                                          menuOptionAlign="end"
                                          menuOptionSide="bottom"
                                        />
                                      ) : null}

                                      {customFieldKind === 'select' ? (
                                        <Filter.Combobox
                                          closeOnSelect
                                          triggerFilterChange={false}
                                          isSearchable={false}
                                          options={selectOptions}
                                          size="md"
                                          placeholder={`${t(
                                            'label:placeholder:select'
                                          )}`}
                                          searchPlaceholder={`${t(
                                            'label:placeholder:search'
                                          )}`}
                                          loadingMessage={`${t(
                                            'label:loading'
                                          )}`}
                                          noOptionsMessage={`${t(
                                            'label:noOptions'
                                          )}`}
                                          countName={`${t(
                                            'label:placeholder:status'
                                          )}`}
                                          name={field}
                                          buttonClassName={`w-full ${maxWidthItem}`}
                                          buttonFontWeightClassName="font-normal"
                                          dropdownMenuClassName="!min-w-[428px]"
                                          containerMenuClassName="max-w-[428px]"
                                          menuOptionAlign="end"
                                          menuOptionSide="bottom"
                                        />
                                      ) : null}

                                      {customFieldKind === 'multiple' ? (
                                        <Filter.Combobox
                                          isMulti
                                          triggerFilterChange={false}
                                          isSearchable={true}
                                          options={selectOptions}
                                          size="md"
                                          placeholder={`${t(
                                            'label:placeholder:select'
                                          )}`}
                                          searchPlaceholder={`${t(
                                            'label:placeholder:search'
                                          )}`}
                                          loadingMessage={`${t(
                                            'label:loading'
                                          )}`}
                                          noOptionsMessage={`${t(
                                            'label:noOptions'
                                          )}`}
                                          name={field}
                                          buttonClassName={`w-full ${maxWidthItem}`}
                                          buttonFontWeightClassName="font-normal"
                                          dropdownMenuClassName="!min-w-[428px]"
                                          containerMenuClassName="max-w-[428px]"
                                          menuOptionAlign="end"
                                          menuOptionSide="bottom"
                                        />
                                      ) : null}

                                      {customFieldKind === 'date' &&
                                      direction !== 'range' ? (
                                        <Filter.DateWithYearOnlyPicker
                                          triggerFilterChange={false}
                                          size="md"
                                          name={field}
                                          placeholder={`${t(
                                            'label:placeholder:select'
                                          )}`}
                                          config={{
                                            showOutsideDays: false,
                                            fromYear: 1920
                                          }}
                                        />
                                      ) : null}

                                      {customFieldKind === 'date' &&
                                      direction === 'range' ? (
                                        <Filter.RangeDatePicker
                                          triggerFilterChange={false}
                                          size="md"
                                          name={field}
                                          placeholder={`${t(
                                            'label:placeholder:formTo'
                                          )}`}
                                          config={{
                                            showOutsideDays: false,
                                            fromYear: 1920
                                          }}
                                          classNameInput={`${maxWidthItem} truncate`}
                                          iconMenus="Calendar"
                                        />
                                      ) : null}
                                    </>
                                  ) : null}
                                </>
                              ) : (
                                <Input
                                  maxLength={50}
                                  size="sm"
                                  placeholder={`${t(
                                    'label:placeholder:input'
                                  )}`}
                                  name="search"
                                />
                              )}
                            </>
                          ) : null}
                        </div>
                      </div>

                      <Filter.Item<string>
                        triggerFilterChange={false}
                        name={String(field)}>
                        {({ onChange }) => {
                          return (
                            <Button
                              type="secondary"
                              size="sm"
                              iconMenus="Trash2"
                              onClick={() => {
                                const cloneValue = JSON.parse(
                                  JSON.stringify(filterConditions)
                                )
                                cloneValue.splice(index, 1)
                                setFilterConditions(cloneValue)
                                onChange(undefined as unknown as string)
                              }}
                            />
                          )
                        }}
                      </Filter.Item>
                    </div>
                  )
                })}
              </div>

              <div className="mt-1 flex items-center justify-between space-x-3 px-5">
                <TextButton
                  size="md"
                  underline={false}
                  iconMenus="Plus"
                  label={`${t('button:addFilter')}`}
                  onClick={() => {
                    const cloneValue = JSON.parse(
                      JSON.stringify(filterConditions)
                    )
                    setFilterConditions([
                      ...cloneValue,
                      {
                        field: undefined
                      }
                    ])
                  }}
                />
                <div className="flex items-center space-x-3">
                  <Filter.Button
                    defaultFilter={{
                      page: 1,
                      operator: 'and',
                      search: filters?.search
                    }}
                    isSubmit={false}
                    type="secondary"
                    size="sm"
                    label={`${t('button:clearFilter')}`}
                    onClick={() => {
                      setFilterConditions([])
                      setActionOnFilters &&
                        setActionOnFilters(LIST_ACTION_FILTERS.clearFilter)
                    }}
                  />
                  <If
                    condition={
                      isFeatureEnabled(PLAN_FEATURE_KEYS.profile_view) &&
                      isUnLockFeature(PLAN_FEATURE_KEYS.profile_view)
                    }>
                    <If
                      condition={isViewOwner}
                      fallback={
                        <Button
                          type="secondary"
                          size="sm"
                          label={`${t('button:saveAsNewView')}`}
                          onClick={onSaveAsNewView}
                        />
                      }>
                      <Filter.Button
                        isSubmit
                        extraFilters={{
                          fieldsFilter: filterConditions
                        }}
                        type="secondary"
                        size="sm"
                        label={`${t('button:saveView')}`}
                        onClick={() => {
                          setOpenDropdown(false)
                          setActionOnFilters &&
                            setActionOnFilters(LIST_ACTION_FILTERS.updateView)
                        }}
                      />
                    </If>
                  </If>

                  <Filter.Button
                    isSubmit
                    extraFilters={{
                      fieldsFilter: filterConditions
                    }}
                    size="sm"
                    htmlType="submit"
                    label={`${t('button:apply')}`}
                    onClick={() => {
                      setOpenDropdown(false)
                      setActionOnFilters &&
                        setActionOnFilters(LIST_ACTION_FILTERS.applyFilter)
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </>
  )
}

export default FilterCandidateListingConfig
