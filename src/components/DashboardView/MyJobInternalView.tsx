/* eslint-disable react-hooks/rules-of-hooks */
import { CellContext, HeaderContext } from '@tanstack/react-table'
import Link from 'next/link'
import React, {
  FC,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react'
import { useTranslation } from 'react-i18next'
import pathConfiguration from 'src/configuration/path'
import { AGENCY_TENANT, DEFAULT_MOUNT_PAGE_SIZE } from '~/core/constants/enum'
import IconWrapper, { LucideIconName } from '~/core/ui/IconWrapper'
import { TablePagination } from '~/core/ui/TablePagination'
import { TypographyText } from '~/core/ui/Text'
import { Tooltip } from '~/core/ui/Tooltip'
import { truncateTextWithDot } from '~/core/utilities/common'
import { defaultFormatDate } from '~/core/utilities/format-date'
import QueryOpenJobs from '~/lib/features/referrals/graphql/query-open-jobs'
import {
  EmployeeProfileType,
  JobDetailType,
  ReferralFormType
} from '~/lib/features/referrals/types'
import { ReferralSettingType } from '~/lib/features/settings/referrals/types'
import useBoundStore from '~/lib/store'
import MatchedRankDetail, {
  ModalMatchedRankType
} from '../Candidates/Profile/components/Recommendation/MatchedRankDetail'
import MatchedRankDetailWrapper from '../Candidates/Profile/components/Recommendation/MatchedRankDetailWrapper'
import ReferralModal from '../CareerHub/[id]/ReferralModal'
import usePaginationGraphPage from '../../lib/hooks/use-pagination-graph-page'
import ActionOpenJobInternal from './Components/ActionOpenJobInternal'
import configuration from '~/configuration'
import useSubscriptionPlan from '../Subscription/useSubscriptionPlan'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import QueryDirectOpenJobs from '~/lib/features/referrals/graphql/query-direct-open-jobs'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

const JobInfoWrapper: FC<{
  iconMenu?: LucideIconName
  text: string
  tooltip?: React.ReactElement
  className?: string
}> = ({ iconMenu, text, tooltip, className }) => {
  return (
    <div className={`flex items-center ${className}`}>
      <div className="mr-1.5">
        <IconWrapper name={iconMenu} size={12} className="text-gray-600" />
      </div>
      {tooltip ? (
        <Tooltip content={tooltip}>
          <TypographyText className="line-clamp-1 max-w-[250px] break-all text-sm text-gray-700">
            {text}
          </TypographyText>
        </Tooltip>
      ) : (
        <TypographyText className="text-sm text-gray-700">
          {text}
        </TypographyText>
      )}
    </div>
  )
}

const MyJobInternalView = ({
  setCountOpenJobs,
  dataReferral,
  employeeProfile,
  fetchEmployeeProfile
}: {
  fetchEmployeeProfile: () => void
  dataReferral: ReferralSettingType | undefined
  employeeProfile: EmployeeProfileType | undefined
  setCountOpenJobs: React.Dispatch<React.SetStateAction<number | undefined>>
}) => {
  const { t } = useTranslation()
  const { user } = useBoundStore()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const setModalMatchedRankRef =
    useRef<React.Dispatch<React.SetStateAction<ModalMatchedRankType>>>()
  const referralPortal = dataReferral?.values?.referral_portal
  const [openReferralModal, setOpenReferralModal] = useState<boolean>(false)
  const [referralJob, setReferralJob] = useState<ReferralFormType>()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const isCompanyFlag =
    isFeatureEnabled(PLAN_FEATURE_KEYS.company) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.company)

  const { data, isFetching, fetchPagination, forceChangeCurrentPage, refetch } =
    usePaginationGraphPage({
      pageSize: configuration.defaultTableMountPageSize,
      queryDocumentNode: isCompanyKind ? QueryOpenJobs : QueryDirectOpenJobs,
      queryKey: 'open-job-dashboard',
      filter: {}
    })

  const callbackOnFinishReferralModal = async () => {
    refetch()
  }

  const onCloseReferralModal = useCallback(() => {
    setReferralJob({})
    setOpenReferralModal(false)
  }, [setOpenReferralModal])

  const renderLocations = useCallback(
    (locations: JobDetailType['jobLocations']) => {
      const locationFormatted = (locations || [])?.map(
        (location) =>
          `${location.state || ''}${
            !!location.state && !!location.country ? ', ' : ''
          }${location.country || ''}`
      )
      const locationFormattedUnique = locationFormatted?.filter(
        (location, index) => locationFormatted.indexOf(location) === index
      )
      return (
        <JobInfoWrapper
          iconMenu="MapPin"
          className="flex max-w-[50%]"
          tooltip={
            <>
              {locationFormattedUnique?.map((location) => (
                <div key={`open-job-location-${location}`}>{location}</div>
              ))}
            </>
          }
          text={
            locationFormattedUnique.length > 1
              ? `${locationFormattedUnique.length} ${t('label:locations')}`
              : truncateTextWithDot(locationFormattedUnique?.[0])
          }
        />
      )
    },
    []
  )

  useEffect(() => {
    if (data?.meta?.totalRowCount) {
      setCountOpenJobs(data?.meta?.totalRowCount)
    }
  }, [data, setCountOpenJobs])

  const columns = useMemo(
    () =>
      [
        {
          accessorKey: 'jobTitle',
          header: () => (
            <TypographyText className="text-left text-xs font-normal text-gray-700 dark:text-gray-300">
              {t(`dashboard:myJob:table:jobTitle`)}
            </TypographyText>
          ),
          cell: (info: CellContext<JobDetailType, undefined>) => {
            const job = info?.row?.original
            const rank = job?.recommendationMatchedFields?.filter(
              (r) => r.field === 'total'
            )[0]
            const companyName = isCompanyKind
              ? job?.company?.permittedFields?.name?.value
              : job.permittedFields?.company?.value?.name
            const companyId = isCompanyKind
              ? job?.company?.id
              : job.permittedFields?.company?.value?.id

            return (
              <div>
                <div className="flex w-full items-center space-x-1.5">
                  {job.recommendationMatchedFields && rank.total_rate > 0 && (
                    <div className="mr-1.5">
                      <MatchedRankDetailWrapper
                        openMatchedRank={setModalMatchedRankRef?.current}
                        job={job}
                      />
                    </div>
                  )}
                  <Tooltip content={`${job?.title}`}>
                    <Link
                      href={pathConfiguration?.careerHub.jobDetail({
                        tenantSlug: String(user?.currentTenant?.slug),
                        jobId: job?.id
                      })}
                      target="_blank">
                      <TypographyText className="line-clamp-1 break-all text-sm font-medium text-gray-900 hover:underline">
                        {job?.title}
                      </TypographyText>
                    </Link>
                  </Tooltip>
                </div>
                <div className="flex w-full items-center space-x-1.5">
                  {(job?.jobLocations || [])?.length > 0 &&
                    !!job?.jobLocations?.[0]?.country &&
                    renderLocations(job?.jobLocations)}
                  {(job?.jobLocations || [])?.length > 0 &&
                    !!job?.jobLocations?.[0].country &&
                    !!job?.department?.name && (
                      <div className="h-0.5 w-0.5 rounded bg-gray-400" />
                    )}
                  {!!job?.department?.name && (
                    <div className="flex max-w-[50%] items-center break-all">
                      <IconWrapper
                        size={12}
                        name="Network"
                        className="mr-2 flex-none text-gray-600"
                      />
                      <Tooltip content={job.department?.name}>
                        <TypographyText className="line-clamp-1 text-sm text-gray-700">
                          {job.department?.name}
                        </TypographyText>
                      </Tooltip>
                    </div>
                  )}
                </div>
              </div>
            )
          },
          footer: (props: HeaderContext<JobDetailType, undefined>) =>
            props.column.id,
          size: 600,
          maxSize: 780
        },
        {
          accessorKey: 'company',
          header: () => (
            <TypographyText className="text-left text-xs font-normal text-gray-700 dark:text-gray-300">
              {t('dashboard:internal:job:company')}
            </TypographyText>
          ),
          cell: (info: CellContext<JobDetailType, undefined>) => {
            const job = info?.row?.original
            const companyName = isCompanyKind
              ? job?.company?.permittedFields?.name?.value
              : job.permittedFields?.company?.value?.name
            const companyId = isCompanyKind
              ? job?.company?.id
              : job.permittedFields?.company?.value?.id
            return (
              <div className="flex">
                {companyName ? (
                  <Link
                    href={configuration.path.agency.companyDetail(
                      Number(companyId)
                    )}>
                    <Tooltip content={companyName}>
                      <TypographyText className="line-clamp-1 max-w-[200px] text-sm font-medium text-gray-800 hover:underline">
                        {companyName}
                      </TypographyText>
                    </Tooltip>
                  </Link>
                ) : (
                  <Tooltip content={user?.currentTenant?.name}>
                    <TypographyText className="line-clamp-1 max-w-[200px] text-sm font-medium text-gray-800">
                      {user?.currentTenant?.name}
                    </TypographyText>
                  </Tooltip>
                )}
              </div>
            )
          },
          footer: (props: HeaderContext<JobDetailType, undefined>) =>
            props.column.id,
          size: 200
        },
        {
          accessorKey: 'referrals',
          header: () => (
            <TypographyText className="text-left text-xs font-normal text-gray-700 dark:text-gray-300">
              {t('dashboard:internal:job:referrals')}
            </TypographyText>
          ),
          cell: (info: CellContext<JobDetailType, undefined>) => {
            const job = info?.row?.original
            return (
              <div className="flex">
                {job?.referralsCount > 0 && !referralPortal?.job_only && (
                  <TypographyText className="whitespace-nowrap text-sm text-gray-600">
                    {job?.referralsCount}
                  </TypographyText>
                )}
              </div>
            )
          },
          footer: (props: HeaderContext<JobDetailType, undefined>) =>
            props.column.id,
          size: 124,
          maxSize: 161
        },
        {
          accessorKey: 'createdAt',
          header: () => (
            <TypographyText className="text-center text-xs font-normal text-gray-700 dark:text-gray-300">
              {t('dashboard:internal:job:publishDate')}
            </TypographyText>
          ),
          cell: (info: CellContext<JobDetailType, undefined>) => {
            const job = info?.row?.original
            return (
              <div className="text-center">
                {job?.createdAt && (
                  <TypographyText className="whitespace-nowrap text-sm text-gray-600">
                    {defaultFormatDate(new Date(job.createdAt))}
                  </TypographyText>
                )}
              </div>
            )
          },
          footer: (props: HeaderContext<JobDetailType, undefined>) =>
            props.column.id,
          size: 180,
          maxSize: 210
        },
        {
          accessorKey: 'action',
          header: () => (
            <TypographyText className="text-center text-xs font-normal text-gray-600 dark:text-gray-300"></TypographyText>
          ),
          cell: (info: CellContext<JobDetailType, undefined>) => {
            const job = info?.row?.original

            return (
              <ActionOpenJobInternal
                fetchEmployeeProfile={fetchEmployeeProfile}
                employeeProfile={employeeProfile}
                refetch={refetch}
                dataReferral={dataReferral}
                setReferralJob={setReferralJob}
                setOpenReferralModal={setOpenReferralModal}
                job={job}
              />
            )
          },
          footer: (props: HeaderContext<JobDetailType, undefined>) =>
            props.column.id,
          size: 252
        }
      ].filter((col) => !!col),
    [dataReferral, data, employeeProfile]
  )

  return (
    <MatchedRankDetail>
      {(setModalMatchedRank) => {
        if (setModalMatchedRank) {
          setModalMatchedRankRef.current = setModalMatchedRank
        }

        return (
          <>
            <TablePagination
              textOverride={{
                of: `${t('label:of')}`,
                page: `${t('label:page')}`,
                placeholder: `${t('label:placeholder:select')}`,
                search: `${t('label:placeholder:search')}`,
                loading: `${t('label:loading')}`,
                noOptions: `${t('label:noOptions')}`,
                rowsPerPage: `${t('label:rowsPerPage')}`
              }}
              emptyConfig={{
                classNameEmpty:
                  'flex items-center h-auto justify-center h-full',
                title: `${t('dashboard:internal:job:empty:title')}`,
                titleSearch: undefined
              }}
              tableConfig={{
                defaultPageSize: DEFAULT_MOUNT_PAGE_SIZE
              }}
              dataQuery={{
                isFetching,
                fetcher: {
                  fetchPagination,
                  forceChangeCurrentPage
                },
                data
              }}
              columns={columns}
              columnVisibility={{
                referrals: !referralPortal?.job_only,
                company: isCompanyKind || !!isCompanyFlag
              }}
              classNameTable={'h-[calc(100%_-_100px)] mt-2 px-4'}
              classNamePaginationWrapper={
                (data?.data || [])?.length > 0 ? 'pr-3' : ''
              }
              isHeaderSticky
            />
            <ReferralModal
              openReferralModal={openReferralModal}
              onClose={onCloseReferralModal}
              defaultValue={referralJob}
              callbackOnFinish={callbackOnFinishReferralModal}
            />
          </>
        )
      }}
    </MatchedRankDetail>
  )
}

export default MyJobInternalView
