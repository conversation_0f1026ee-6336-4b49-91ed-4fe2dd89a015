import { FC, memo, useCallback, useEffect, useState } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import BottomActionBar from '~/components/BottomActionBar'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import configuration from '~/configuration'
import { IFormAction } from '~/core/@types/global'
import useContextGraphQL from '~/core/middleware/use-context-graphQL'
import { openAlert } from '~/core/ui/AlertDialog'
import If from '~/core/ui/If'
import { IPagePagination } from '~/core/ui/TablePagination'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { adminCanAction } from '~/core/utilities/permission'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import QueryDeleteSkillMutation from '~/lib/features/settings/skills/graphql/submit-delete-skill-mutation'
import QueryEquivalentSkillSuggestionMutation from '~/lib/features/settings/skills/graphql/submit-equivalent-suggestion-skill-mutation'
import QueryMergeSkillMutation from '~/lib/features/settings/skills/graphql/submit-merge-skill-mutation'
import QueryUpdateSkillMutation from '~/lib/features/settings/skills/graphql/submit-update-skill-mutation'
import {
  mappingAISuggestionSkillsForm,
  mappingDeleteSkillsForm,
  mappingMergeSkillsForm,
  mappingMoveSkillsForm
} from '~/lib/features/settings/skills/mapping/form'
import { ISkill } from '~/lib/features/settings/skills/types'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'
import BulkAISuggestionEquivalentSkill from './BulkAISuggestionEquivalentSkill'
import BulkMergeSkill from './BulkMergeSkill'
import BulkMoveSkill from './BulkMoveSkill'
export interface QueryKeyProps {
  search: String
  parentIds: number[]
}
const BulkActions: FC<{
  search: String
  tableRef: any
  skillsSelected?: ISkill[]
  parentIds: number[] | undefined
  onClose: () => void
  skills: IPagePagination | undefined
}> = ({
  search,
  tableRef,
  skillsSelected,
  onClose,
  parentIds = [],
  skills
}) => {
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const { t } = useTranslation()
  const {
    bulkValues,
    bulkSelectedAll,
    resetBulkValues,
    setBulkValues,
    setRefetchMyList,
    setBulkSelectedAll,
    currentRole
  } = useBoundStore()
  const { clientGraphQL } = useContextGraphQL()

  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const enableAIWriter =
    isFeatureEnabled(PLAN_FEATURE_KEYS.ai_writer) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.ai_writer)
  const totalCount = skills?.meta?.totalRowCount || 0
  const { trigger: deleteSkills, isLoading: isDeleting } = useSubmitCommon(
    QueryDeleteSkillMutation
  )
  const { trigger: triggerMerge, isLoading: isMerging } = useSubmitCommon(
    QueryMergeSkillMutation
  )
  const { trigger: triggerMove, isLoading: isMoving } = useSubmitCommon(
    QueryUpdateSkillMutation
  )

  const onDeleteSkills = useCallback(async () => {
    setShowLockApp('')
    if (isDeleting) {
      return
    }

    deleteSkills(
      mappingDeleteSkillsForm({
        bulkValues,
        search,
        parentIds,
        bulkSelectedAll
      })
    ).then((result) => {
      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast
        })
      }

      const { skillsDelete } = result.data
      if (skillsDelete.success) {
        onClose && onClose()
        setCloseLockApp()
        setRefetchMyList(true)
        setToast({
          open: true,
          type: 'success',
          title: t('notification:settings:skills:skillsDeleted')
        })
        resetBulkValues()
      }

      return true
    })
  }, [bulkValues, search, parentIds])

  const onMergeSkill = useCallback(
    async (dataSkill: ISkill, formAction: IFormAction) => {
      setShowLockApp('')
      if (isMerging) {
        return
      }
      triggerMerge(
        mappingMergeSkillsForm({
          bulkValues,
          dataSkill
        })
      ).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.skills,
            formAction,
            setToast
          })
        }

        const { skillsMerge } = result.data
        if (skillsMerge.success) {
          onClose && onClose()
          setCloseLockApp()
          setRefetchMyList(true)
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:settings:skills:skillMerged')}`
          })
          resetBulkValues()
        }

        return true
      })
    },
    [bulkValues, search, parentIds]
  )

  const onAISuggestSkill = useCallback(async () => {
    setShowLockApp('')
    return clientGraphQL
      .query(
        QueryEquivalentSkillSuggestionMutation,
        mappingAISuggestionSkillsForm({
          bulkValues,
          bulkSelectedAll,
          search,
          parentIds
        })
      )
      .toPromise()
      .then(
        (result: {
          error: { graphQLErrors: Array<object> }
          data: {
            equivalentSkillsSuggestion: {
              skills: string[]
            }
          }
        }) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              setToast
            })

            return false
          }

          const { equivalentSkillsSuggestion } = result.data
          if (equivalentSkillsSuggestion) {
            setCloseLockApp()
            setRefetchMyList(true)
            setToast({
              open: true,
              type: 'success',
              title: `${t('notification:settings:skills:skillsAISuggest')}`
            })
            resetBulkValues()
          }

          return true
        }
      )
  }, [bulkValues, search, parentIds])

  const onMoveSkills = useCallback(
    async (dataSkill: ISkill, formAction: IFormAction) => {
      setShowLockApp('')
      if (isMoving) {
        return
      }

      const newSkillMapping = mappingMoveSkillsForm({
        bulkValues,
        search,
        dataSkill,
        bulkSelectedAll,
        parentIds
      })

      triggerMove(newSkillMapping).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.skills,
            formAction,
            setToast
          })
        }
        let messageSuccessfully =
          (newSkillMapping && newSkillMapping?.ids?.length) === 1
            ? `${t('notification:settings:skills:skillMoved', {
                parentName: dataSkill.group?.supportingObj?.name
              })}`
            : `${t('notification:settings:skills:skillsMoved', {
                parentName: dataSkill.group?.supportingObj?.name
              })}`

        const { skillsUpdate } = result.data
        if (skillsUpdate.success) {
          onClose && onClose()
          setCloseLockApp()
          setRefetchMyList(true)
          setToast({
            open: true,
            type: 'success',
            title: messageSuccessfully
          })
          resetBulkValues()
        }

        return true
      })
    },
    [bulkValues, search, parentIds]
  )

  useEffect(() => {
    return () => {
      resetBulkValues()
    }
  }, [])

  const handleClose = () => {
    if (tableRef.current) {
      tableRef.current.toggleAllRowsSelected(false)
    }
    resetBulkValues()
  }

  const showAlertConfirmDelete = () => {
    openAlert({
      className: 'w-[480px]',
      title: `${t('settings:skills:bulkRemoveSkillAlert:title')}`,
      description: (
        <Trans
          i18nKey="settings:skills:bulkRemoveSkillAlert:content"
          values={{
            count: bulkSelectedAll ? totalCount : bulkValues?.length || 0
          }}>
          <span className="font-medium text-gray-900" />
        </Trans>
      ),
      actions: [
        {
          label: t('button:cancel') || '',
          type: 'secondary',
          size: 'sm'
        },
        {
          isCallAPI: true,
          label: t('button:delete') || '',
          type: 'destructive',
          size: 'sm',
          onClick: () => {
            onDeleteSkills()
          }
        }
      ]
    })
  }

  const setIds = () => {
    let ids: string[] = []
    skills?.data?.map((skill: ISkill) => {
      ids = [...ids, String(skill?.id)]
    })
    setBulkValues(ids)
  }
  const handleCheckAll = (e: { target: { checked: boolean } }) => {
    const { checked } = e.target
    setBulkSelectedAll(checked)
    if (tableRef.current) {
      tableRef.current.toggleAllRowsSelected(checked)
    }
    if (checked) setIds()
    else resetBulkValues()
  }

  useEffect(() => {
    if (bulkSelectedAll) setIds()
  }, [skills])

  if (bulkSelectedAll || bulkValues?.length)
    return (
      <BottomActionBar
        data={bulkValues || []}
        canSelectAll={true}
        isSelectedAll={bulkSelectedAll || false}
        onClose={handleClose}
        setSelectedAll={handleCheckAll}
        totalCount={totalCount}
        className="bottom-6 w-max tablet:ml-[120px]"
        onDelete={
          currentRole && adminCanAction(currentRole?.code)
            ? showAlertConfirmDelete
            : null
        }>
        <Tooltip
          content={
            bulkValues?.length === 1 || bulkSelectedAll
              ? t('tooltip:visibilityMergeAction')
              : ''
          }>
          <BulkMergeSkill
            onSubmit={onMergeSkill}
            loading={isMerging}
            disabled={bulkValues?.length === 1 || bulkSelectedAll}
            primarySkills={skillsSelected}
          />
        </Tooltip>

        <BulkMoveSkill
          onSubmit={onMoveSkills}
          totalCount={totalCount}
          loading={isMoving}
        />

        <If condition={enableAIWriter}>
          <Tooltip
            sideOffset={8}
            content={
              (bulkValues || []).length > 100 ||
              (bulkSelectedAll && (skills?.meta?.totalRowCount || 0) > 100)
                ? t('settings:skills:skillSimilar:canNotUseAISuggestTooltip')
                : t('settings:skills:skillSimilar:aiSuggestTooltip')
            }>
            <BulkAISuggestionEquivalentSkill
              disabled={
                (bulkValues || []).length > 100 ||
                (bulkSelectedAll && (skills?.meta?.totalRowCount || 0) > 100)
              }
              onSubmit={onAISuggestSkill}
            />
          </Tooltip>
        </If>
      </BottomActionBar>
    )

  return null
}

export default memo(BulkActions)
