import { useQueryClient } from '@tanstack/react-query'
import { parseCookies } from 'nookies'
import { useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import { generateReportFileName } from '~/components/Reports/components/ReportFilterExportWrapper'
import configuration from '~/configuration'
import {
  SESSION_COOKIE_IP,
  SESSION_COOKIE_NAME
} from '~/core/constants/cookies'
import { DESC_SORTING } from '~/core/constants/enum'
import logger from '~/core/logger'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DebouncedInput } from '~/core/ui/DebouncedInput'
import { ISelectOption } from '~/core/ui/Select'
import { IPagePagination } from '~/core/ui/TablePagination'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import QueryTenantSubSkill from '~/lib/features/settings/skills/graphql/query-tenant-sub-skill'
import QueryDeleteSkillMutation from '~/lib/features/settings/skills/graphql/submit-delete-skill-mutation'
import useSkillManagement from '~/lib/features/settings/skills/hooks/use-skill-management'
import { mappingSkillSearchGraphQL } from '~/lib/features/settings/skills/mapping/search-skill-groups-listing.mapping'
import { ISkill } from '~/lib/features/settings/skills/types'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'
import BulkActions from './BulkActions'
import SkillModal from './SkillModal'
import SkillsEditingListingTable, { QueryKeyProps } from './SkillsEditingTable'

interface SkillModalProps {
  open: boolean
  defaultValue?: ISkill
  action?: string
  moveOnly?: boolean
}
export enum ACTION_SKILL {
  ADD = 'add',
  EDIT = 'edit',
  MOVE = 'move'
}
const SkillView: React.FC<{
  userId?: string
  suspend?: boolean
  setCount: (val: number) => void
  isOpenAddSkill?: boolean
  isExportSkill: boolean
  setCloseAddSKill: (open: boolean) => void
  setExportSkill: (open: boolean) => void
  setDisableExportSkill: (disable: boolean) => void
}> = ({
  userId,
  suspend,
  setCount,
  isOpenAddSkill = false,
  isExportSkill = false,
  setCloseAddSKill,
  setExportSkill,
  setDisableExportSkill
}) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const { refetchMyList, setRefetchMyList, bulkValues, resetBulkValues } =
    useBoundStore()
  const queryClient = useQueryClient()
  const { promiseGroupOptions } = useSkillManagement()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()

  const [isDefaultFilters, setIsDefaultFilters] = useState<boolean>(true)
  const [queryKey, setQueryKey] = useState<QueryKeyProps>({})
  const [openSkill, setOpenSKill] = useState<SkillModalProps>({
    open: false,
    defaultValue: undefined
  })
  const [sorting, setSorting] = useState<{
    [key: string]: string | null
  }>({
    updatedAt: DESC_SORTING
  })

  const [itemSelected, setItemSelected] = useState<ISkill[]>([])
  const tableRef = useRef<any>()
  const { trigger: triggerDelete, isLoading: isLoadingDelete } =
    useSubmitCommon(QueryDeleteSkillMutation)

  // FUNCTION HANDLER

  const deleteSkillCallback = useCallback(
    async (skill: ISkill) => {
      if (isLoadingDelete) {
        return
      }

      triggerDelete({
        ids: [Number(skill.id)],
        group: true
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.skills,
            setToast
          })
        }

        const { skillsDelete } = result.data
        if (skillsDelete.success) {
          setToast({
            open: true,
            type: 'success',
            title: `${t('notification:settings:skills:groupDeleted')}`
          })
          refetch()
        }

        return
      })
    },
    [
      isLoadingDelete,
      triggerDelete,
      setToast,
      t,
      queryClient,
      queryKey,
      bulkValues,
      itemSelected
    ]
  )

  const handleCallbackSkill = (
    dataCallback: ISkill,
    parentSkill?: ISelectOption
  ) => {
    refetch()
  }

  const handleChangeInput = (value: string | number) => {
    setTimeout(() => {
      setQueryKey({
        ...queryKey,
        search: String(value),
        page: 1,
        key: (queryKey?.key || 1) + 1
      })
    }, 0)
    setIsDefaultFilters(false)
    resetBulkValues()
  }

  const handleClearInput = () => {
    if (tableRef.current) {
      tableRef.current.toggleAllRowsExpanded(false)
    }
    setTimeout(() => {
      setQueryKey({
        ...queryKey,
        search: '',
        page: 1,
        key: (queryKey?.key || 1) + 1
      })
    }, 0)
    setIsDefaultFilters(false)
    resetBulkValues()
  }

  const handleChangeGroupOption = (value?: ISelectOption | ISelectOption[]) => {
    const newValue = value || []
    setQueryKey({
      ...queryKey,
      parentIds: Array.isArray(newValue) ? newValue : [newValue]
    })
    if (tableRef.current) tableRef.current.toggleAllRowsExpanded(false)
    resetBulkValues()
    setIsDefaultFilters(false)
  }

  const handleClearBulkActions = () => {
    if (tableRef.current) {
      tableRef.current.toggleAllRowsSelected(false)
    }
    resetBulkValues()
    setItemSelected([])
  }

  const handleOpenEditSkill = (skill: ISkill) => {
    setOpenSKill({
      open: true,
      defaultValue: {
        ...skill,
        group: {
          value: skill?.parent?.id,
          supportingObj: {
            name: skill?.parent?.name
          }
        }
      },
      action: ACTION_SKILL.EDIT
    })
  }

  const handleOpenMoveSkill = (skill: ISkill) => {
    setOpenSKill({
      open: true,
      defaultValue: {
        ...skill,
        group: {
          value: skill?.parent?.id,
          supportingObj: {
            name: skill?.parent?.name
          }
        }
      },
      action: ACTION_SKILL.MOVE,
      moveOnly: true
    })
  }

  const getApiUrl = () => {
    return configuration.api.exportSkillSettingCsv()
  }

  const onExportCsv = async () => {
    setShowLockApp('')
    const cookies = parseCookies()
    const authenticationToken = cookies[SESSION_COOKIE_NAME]
    const ip = cookies[SESSION_COOKIE_IP]

    try {
      const fileName = generateReportFileName('Skills_listing')
      const response = await fetch(getApiUrl(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${authenticationToken}`,
          IP: String(ip)
        },
        body: JSON.stringify({ sorting: sorting })
      })
      if (!response.ok) {
        throw new Error(`Error on download: ${response.status}`)
      }

      const blob = await response.blob()

      const downloadUrl = window.URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = downloadUrl

      link.setAttribute('download', fileName)

      document.body.appendChild(link)
      link.click()

      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
      setCloseLockApp()
    } catch (error) {
      setCloseLockApp()
      logger.error(error)
    }
  }

  const checkCanExportSkill = useCallback(
    (totalCount: number) => {
      const canExport =
        (totalCount === 0 && !queryKey.search && !queryKey.parentIds) ||
        queryKey?.parentIds?.length === 0
      setDisableExportSkill(canExport)
    },
    [queryKey.search, queryKey.parentIds]
  )

  const {
    data: skills,
    fetchPagination,
    globalFilter,
    setGlobalFilter,
    refetch,
    forceChangeCurrentPage,
    isFetching
  } = usePaginationGraphPage({
    queryDocumentNode: QueryTenantSubSkill,
    queryKey: 'skill-listing-setting',
    filter: { sorting, ...mappingSkillSearchGraphQL(queryKey) }
  })

  useEffect(() => {
    if (skills) {
      const totalCount = skills?.meta.totalRowCount || 0
      setCount(totalCount)
      checkCanExportSkill(totalCount)
    }
  }, [skills])

  useEffect(() => {
    if (!isOpenAddSkill) return
    setOpenSKill({
      open: true,
      defaultValue: {},
      action: ACTION_SKILL.ADD
    })
  }, [isOpenAddSkill])

  useEffect(() => {
    if (refetchMyList) {
      if (tableRef.current) tableRef.current.toggleAllRowsExpanded(false)
      refetch()
      setRefetchMyList(false)
    }
  }, [refetchMyList])

  useEffect(() => {
    setCloseAddSKill(false)
  }, [openSkill])

  useEffect(() => {
    if (isExportSkill) {
      onExportCsv()
    }
    setExportSkill(false)
  }, [isExportSkill])

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-[320px]">
            <DebouncedInput
              debounceDelay={500}
              placeholder={`${t('settings:skills:searchInput')}`}
              value={''}
              onChange={handleChangeInput}
              onClear={handleClearInput}
              forceOnChangeCallBack={queryKey}
            />
          </div>

          <ComboboxSelect
            size="md"
            isMulti={true}
            menuOptionAlign="end"
            menuOptionSide="bottom"
            avatarToolTipPosition="bottom"
            toolTipPositionAvatarCount="bottom"
            tooltipAlignAvatarCount="end"
            dropdownMenuClassName="!w-[264px]"
            containerMenuClassName="w-[264px]"
            buttonClassName="max-w-[164px]"
            placeholder={`${t('settings:skills:allGroup')}`}
            searchPlaceholder={`${t('label:placeholder:search')}`}
            loadingMessage={`${t('label:loading')}`}
            noOptionsMessage={`${t('label:noOptions')}`}
            value={queryKey?.parentIds}
            options={promiseGroupOptions}
            onChange={handleChangeGroupOption}
            countName={`${t('settings:skills:skillTable:groups')}`}
          />
        </div>
      </div>

      <SkillsEditingListingTable
        tableRef={(tableEditor: any) => {
          return (tableRef.current = tableEditor)
        }}
        queryKey={queryKey}
        globalFilter={globalFilter}
        setGlobalFilter={setGlobalFilter}
        isDefaultFilters={isDefaultFilters}
        classNameEmpty="-ml-6"
        data={skills}
        fetcher={{
          fetchPagination,
          forceChangeCurrentPage
        }}
        isFetching={isFetching}
        classNameTable={'pb-[55px]'}
        deleteSkillCallback={deleteSkillCallback}
        editSkillCallback={handleOpenEditSkill}
        moveSkillCallback={handleOpenMoveSkill}
        setItemSelected={setItemSelected}
        itemSelected={itemSelected}
        sorting={sorting}
        setSorting={setSorting}
      />

      <SkillModal
        open={openSkill.open}
        setOpen={setOpenSKill}
        tableRef={tableRef}
        data={openSkill}
        isMove={openSkill.moveOnly}
        callback={handleCallbackSkill}
      />

      <BulkActions
        tableRef={tableRef}
        skills={skills}
        search={queryKey?.search ? queryKey?.search : ''}
        parentIds={queryKey?.parentIds?.map((item) => Number(item.value))}
        skillsSelected={itemSelected}
        onClose={handleClearBulkActions}
      />
    </>
  )
}

export default withQueryClientProvider(SkillView)
