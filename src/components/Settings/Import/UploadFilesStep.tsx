import { useRouter } from 'next/router'
import { FC, useCallback, useMemo } from 'react'
import { Controller } from 'react-hook-form'
import { Trans, useTranslation } from 'react-i18next'
import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { Button } from '~/core/ui/Button'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { UploadFileDragAndDrop } from '~/core/ui/FileDragAndDrop'
import { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { Radio } from '~/core/ui/Radio'
import useImportHandler from '~/lib/features/settings/import/hooks/use-import'
import schemaUploadFileForm from '~/lib/features/settings/import/schema/schema-upload-file-form'
import {
  ISourceImportType,
  MappedFieldType
} from '~/lib/features/settings/import/types'
import {
  ENUMS_IMPORT_TYPE,
  IMPORT_TYPE_URL,
  PROGRESS_IMPORT
} from '~/lib/features/settings/import/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { MAX_FILE_SIZE, MAX_FILE_SIZE_50MB } from '~/lib/schema'
import ImportProgressBar from './components/ImportProgressBar'

// Configuration object for different import types
const IMPORT_CONFIG = {
  [ENUMS_IMPORT_TYPE.jobs]: {
    maxRecords: '1000',
    maxFileSize: 10,
    fileSizeLimit: MAX_FILE_SIZE
  },
  [ENUMS_IMPORT_TYPE.candidate]: {
    maxRecords: '1000',
    maxFileSize: 10,
    fileSizeLimit: MAX_FILE_SIZE
  },
  [ENUMS_IMPORT_TYPE.course]: {
    maxRecords: '10,000',
    maxFileSize: 50,
    fileSizeLimit: MAX_FILE_SIZE_50MB
  },
  [ENUMS_IMPORT_TYPE.company]: {
    maxRecords: '5000',
    maxFileSize: 10,
    fileSizeLimit: MAX_FILE_SIZE
  }
} as const

interface UploadFilesStepProps {
  listSourceImportType: ISourceImportType[]
  importType: ISourceImportType
  finishCallback?: (data: {
    mappedFields?: MappedFieldType[]
    file?: File
  }) => Promise<any>
}

const UploadFilesStep: FC<UploadFilesStepProps> = ({
  finishCallback,
  importType,
  listSourceImportType
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const { validateImport, isLoadingValidate: isLoading } = useImportHandler({
    importType
  })

  // Get current import configuration
  const currentConfig = useMemo(
    () => IMPORT_CONFIG[importType.value as keyof typeof IMPORT_CONFIG],
    [importType.value]
  )

  const sampleLink = useMemo(
    () =>
      isCompanyKind
        ? importType.templateUrl.agency
        : importType.templateUrl.direct,
    [isCompanyKind, importType.templateUrl]
  )

  const handleImportTypeChange = useCallback(
    (valueSelected: string) => {
      const selectedImportType = listSourceImportType.find(
        (source) => source.value === valueSelected
      )

      if (selectedImportType) {
        const objectKind =
          IMPORT_TYPE_URL[
            selectedImportType.value as keyof typeof IMPORT_TYPE_URL
          ]
        router.push(
          `${configuration.path.settings.import}?tab=import&object_kind=${objectKind}`
        )
      }
    },
    [listSourceImportType, router]
  )

  const onFinishImport = useCallback(
    (data: { file: File[] }, formAction: IFormAction) => {
      const file = data.file?.[0]
      if (!file) return Promise.resolve()

      return validateImport(file, finishCallback)
    },
    [validateImport, finishCallback]
  )

  // Prepare radio source data
  const radioSources = useMemo(
    () =>
      listSourceImportType.map((source) => ({
        ...source,
        text: t(`settings:import:sources:${source.value}:title`) || '',
        description: source.description
      })),
    [listSourceImportType, t]
  )

  const dragDropHelperText = useMemo(
    () =>
      t('settings:import:only_support_csv', {
        numberOfRecord: currentConfig.maxRecords,
        maxFile: currentConfig.maxFileSize
      }),
    [t, currentConfig]
  )

  return (
    <DynamicImportForm
      id="upload-files-form"
      className="h-full w-full"
      schema={schemaUploadFileForm(t, currentConfig.fileSizeLimit)}
      onSubmit={onFinishImport}
      defaultValue={{
        file: [] as File[]
      }}>
      {({ formState, control }) => (
        <div className="flex h-full flex-col justify-between">
          <div className="flex-1 overflow-auto">
            <div className="mb-6">
              <ImportProgressBar currentStep={0} data={PROGRESS_IMPORT(t)} />
            </div>

            <div className="mb-4 ml-0.5">
              <Radio
                orientation="horizontal"
                size="sm"
                source={radioSources}
                onValueChange={handleImportTypeChange}
                value={importType.value}
              />
            </div>

            <div>
              <Controller
                control={control}
                name="file"
                defaultValue={undefined}
                render={({ field: { onChange, value } }) => (
                  <FormControlItem
                    destructive={!!formState.errors?.file}
                    destructiveText={formState.errors?.file?.message as string}>
                    <UploadFileDragAndDrop
                      className="w-full [&>div:first-child]:flex [&>div:first-child]:min-h-[256px] [&>div:first-child]:flex-col [&>div:first-child]:items-center [&>div:first-child]:justify-center"
                      configText={{
                        clickToUpload: t('label:dragAndDrop:clickToUpload'),
                        orDragAndDrop: t('label:dragAndDrop:orDragAndDrop'),
                        delete: t('tooltip:delete'),
                        tryAgain: t('tooltip:tryAgain'),
                        uploadANewFile: t('tooltip:uploadANewFile')
                      }}
                      dragNDropHelperText={dragDropHelperText}
                      maximumFiles={1}
                      accept={{ 'text/csv': ['.csv'] }}
                      files={
                        value ? (Array.isArray(value) ? value : [value]) : []
                      }
                      onChange={onChange}
                    />
                  </FormControlItem>
                )}
              />
            </div>

            <div className="mt-1 text-sm text-gray-600">
              <Trans i18nKey="settings:import:view_sample_file">
                <a
                  className="text-primary-400 hover:cursor-pointer"
                  href={sampleLink}
                  target="_blank"
                  rel="noopener noreferrer"
                />
              </Trans>
            </div>
          </div>

          <div className="sticky bottom-0 flex flex-none items-center justify-end bg-white py-4">
            <Button
              htmlType="submit"
              size="sm"
              isDisabled={!formState.isValid || isLoading}
              label={`${t('settings:import:next')}`}
              iconMenus="ArrowRight"
              icon="trailing"
            />
          </div>
        </div>
      )}
    </DynamicImportForm>
  )
}

export default UploadFilesStep
