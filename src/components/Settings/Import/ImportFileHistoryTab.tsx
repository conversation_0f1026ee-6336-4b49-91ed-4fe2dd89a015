import Link from 'next/link'
import { useRouter } from 'next/router'
import { FC, useEffect } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import configuration from '~/configuration'
import { Avatar } from '~/core/ui/Avatar'
import { Badge, IColorBadgeType } from '~/core/ui/Badge'
import { IconButton } from '~/core/ui/IconButton'
import { Radio } from '~/core/ui/Radio'
import { TableInfinityOrdering } from '~/core/ui/TableInfinityOrdering'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { fetchAndDownloadFile } from '~/core/utilities/common'
import { defaultFormatDate } from '~/core/utilities/format-date'
import useImportHandler from '~/lib/features/settings/import/hooks/use-import'
import {
  ImportFileType,
  ISourceImportType
} from '~/lib/features/settings/import/types'
import {
  IMPORT_FILE_STATUS_COLOR,
  IMPORT_TYPE_URL
} from '~/lib/features/settings/import/utilities/enum'
import { useInfinityQuerySearch } from '~/lib/hooks/use-infinity-query-search'

const ImportFileHistoryTab: FC<{
  goToUploadFileStep?: () => void
  listSourceImportType: ISourceImportType[]
  importType: ISourceImportType
}> = ({ goToUploadFileStep, listSourceImportType, importType }) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { getImportList } = useImportHandler({ importType })
  const {
    data,
    fetchNextPage,
    isFetching,
    isLoading,
    isFetchedAfterMount,
    refetch
  } = useInfinityQuerySearch({
    configuration,
    fetchData: getImportList,
    queryKey: {}
  })
  const topSpace = useClassBasedTopSpace({
    34: 'max-h-[calc(100vh_-_168px)]',
    default: 'max-h-[calc(100vh_-_134px)]'
  })

  useEffect(() => {
    refetch()
  }, [importType.value])

  return (
    <div className="-mt-3 h-full">
      <div className="mb-4 ml-0.5 mt-5">
        <Radio
          orientation="horizontal"
          size="sm"
          source={listSourceImportType.map((source) => ({
            ...source,
            text: t(`settings:import:sources:${source.value}:title`) || '',
            description: source.description
          }))}
          onValueChange={(valueSelected) => {
            let filterImportType = listSourceImportType.filter(
              (source) => source.value === valueSelected
            )
            if (filterImportType.length) {
              router.push(
                `${
                  configuration.path.settings.import
                }?tab=history&object_kind=${
                  IMPORT_TYPE_URL[
                    filterImportType[0].value as keyof typeof IMPORT_TYPE_URL
                  ]
                }`
              )
            }
          }}
          value={importType.value}
        />
      </div>
      <TableInfinityOrdering
        tableConfig={{
          defaultFirstLoadingSize: configuration.defaultTableMountPageSize,
          useInfinity: false,
          renderButton: (
            <TextButton
              label={t('common:table:show25More')}
              type="tertiary"
              underline={false}
              size="md"
            />
          ),
          endOfList: `${t('common:table:endOfList')}`
        }}
        emptyConfig={{
          classNameEmpty: 'h-full flex items-center justify-center',
          title: `${t('settings:import:empty_history:title')}`,
          description: `${t('settings:import:empty_history:description')}`,
          buttonTitle: `${t('settings:import:empty_history:import_file')}`,
          buttonTitleOnClick: goToUploadFileStep
        }}
        dataQuery={{
          isFetching,
          isLoading,
          isFetchedAfterMount,
          fetcher: {
            fetchNextPage
          },
          data
        }}
        columns={[
          {
            accessorKey: 'fileName',
            header: () => {
              return (
                <div className="mr-2">
                  <Trans i18nKey={'settings:import:file_name'} />
                </div>
              )
            },
            cell: (info: {
              row: { original: ImportFileType }
              getValue: Function
            }) => (
              <div className="inline-flex w-full">
                <Tooltip
                  classNameAsChild="truncate"
                  content={info?.row?.original?.name}>
                  <Link
                    href={`${configuration.path.settings.import}?tab=${router.query['tab']}&import_id=${info.row.original.id}&object_kind=${router.query['object_kind']}`}>
                    <TypographyText className="truncate text-sm font-medium text-gray-900 hover:underline">
                      {info?.row?.original?.name}
                    </TypographyText>
                  </Link>
                </Tooltip>
              </div>
            ),
            footer: (props) => props.column.id,
            size: 220
          },
          {
            accessorKey: 'status',
            header: () => {
              return (
                <div className="mr-2">
                  <Trans i18nKey={'settings:import:status'} />
                </div>
              )
            },
            cell: (info: {
              row: { original: ImportFileType }
              getValue: Function
            }) => (
              <div className="flex">
                <Badge
                  type="dotLeading"
                  radius="rounded"
                  size="sm"
                  color={
                    (IMPORT_FILE_STATUS_COLOR[info?.row?.original?.status] ||
                      'green') as IColorBadgeType
                  }>
                  {info.row.original.statusDescription}
                </Badge>
              </div>
            ),
            footer: (props) => props.column.id,
            size: 130
          },
          {
            accessorKey: 'objects',
            header: () => {
              return (
                <div className="mr-2">
                  <Trans i18nKey={'settings:import:objects'} />
                </div>
              )
            },
            cell: (info: {
              row: { original: ImportFileType }
              getValue: Function
            }) => (
              <TypographyText className="text-sm text-gray-900">
                {info.row.original.objectKindDescription}
              </TypographyText>
            ),
            footer: (props) => props.column.id,
            size: 120
          },
          {
            accessorKey: 'entries',
            header: () => {
              return (
                <div className="mr-2">
                  <Trans i18nKey={'settings:import:entries'} />
                </div>
              )
            },
            cell: (info: {
              row: { original: ImportFileType }
              getValue: Function
            }) => (
              <TypographyText className="text-sm text-gray-900">
                {info.row.original.importedCount}/{info.row.original.rowsCount}
              </TypographyText>
            ),
            footer: (props) => props.column.id,
            size: 110
          },
          {
            accessorKey: 'uploadedBy',
            header: () => <Trans i18nKey={'settings:import:uploaded_by'} />,
            cell: (info: {
              row: { original: ImportFileType }
              getValue: Function
            }) => {
              return (
                <div className="flex">
                  {!!info.row.original?.uploadedBy?.fullName ? (
                    <Tooltip content={info.row.original?.uploadedBy?.fullName}>
                      <Avatar
                        size="sm"
                        src={
                          info.row.original?.uploadedBy?.avatarVariants?.thumb
                            ?.url
                        }
                        color={info.row.original?.uploadedBy?.defaultColour}
                        alt={info.row.original?.uploadedBy?.fullName}
                      />
                    </Tooltip>
                  ) : (
                    <TypographyText className="text-sm text-gray-900">
                      -
                    </TypographyText>
                  )}
                </div>
              )
            },
            footer: (props) => props.column.id,
            size: 120
          },
          {
            accessorKey: 'import_date',
            header: () => {
              return (
                <div className="mr-2">
                  <Trans i18nKey={'settings:import:import_date'} />
                </div>
              )
            },
            cell: (info: {
              row: { original: ImportFileType }
              getValue: Function
            }) => (
              <TypographyText className="text-sm text-gray-900">
                {info.row.original.createdAt
                  ? defaultFormatDate(new Date(info.row.original.createdAt))
                  : '-'}
              </TypographyText>
            ),
            footer: (props) => props.column.id,
            size: 180
          },
          {
            accessorKey: 'download',
            header: () => {
              return <div />
            },
            cell: (info: {
              row: { original: ImportFileType }
              getValue: Function
            }) => (
              <div>
                <Tooltip content={`${t('tooltip:download')}`}>
                  <IconButton
                    iconMenus="Download"
                    size="sm"
                    type="secondary"
                    onClick={() => {
                      fetchAndDownloadFile({
                        file: info.row.original.file,
                        name: info.row.original.name
                      })
                    }}
                  />
                </Tooltip>
              </div>
            ),
            footer: (props) => props.column.id,
            size: 56
          }
        ]}
        isHeaderSticky
        columnVisibility={{
          fileName: true,
          status: true,
          objects: true,
          entries: true,
          uploadedBy: true,
          import_date: true,
          download: true
        }}
        classNameTable={cn('max-w-full bg-white pb-[60px]', topSpace)}
      />
    </div>
  )
}

export default ImportFileHistoryTab
