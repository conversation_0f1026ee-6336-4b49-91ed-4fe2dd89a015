import { FC, useEffect, useState } from 'react'
import { Controller } from 'react-hook-form'
import { Trans, useTranslation } from 'react-i18next'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import { <PERSON><PERSON> } from '~/core/ui/Button'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import IconWrapper from '~/core/ui/IconWrapper'
import { ISelectOption } from '~/core/ui/Select'
import { TableInfinityOrdering } from '~/core/ui/TableInfinityOrdering'
import { TypographyText } from '~/core/ui/Text'
import { cn } from '~/core/ui/utils'
import useImportHandler from '~/lib/features/settings/import/hooks/use-import'
import schemaMappingDataForm from '~/lib/features/settings/import/schema/schema-mapping-data-form'
import { MappedFieldType } from '~/lib/features/settings/import/types'
import {
  ENUMS_IMPORT_TYPE,
  PROGRESS_IMPORT
} from '~/lib/features/settings/import/utilities/enum'
import ImportProgressBar from './components/ImportProgressBar'
import { ImportTypeKey } from '~/lib/features/settings/import/types'

const MappingDataStep: FC<{
  file?: File
  defaultValues?: {
    mappedFields?: MappedFieldType[]
  }
  previousStep?: () => void
  type: ImportTypeKey
}> = ({ defaultValues, file, previousStep, type }) => {
  const { t } = useTranslation()
  const [mappedFields, setMappedFields] = useState<
    MappedFieldType[] | undefined
  >(defaultValues?.mappedFields)
  const [headerTitle, setHeaderTitle] = useState<string>('')

  const { promiseFields, onImport, isLoadingImport } = useImportHandler({
    importType: {
      value: type
    },
    mappedFields,
    file
  })

  const topSpace = useClassBasedTopSpace({
    34: 'h-[calc(100vh_-_330px)]',
    default: 'h-[calc(100vh_-_296px)]'
  })

  useEffect(() => {
    switch (type) {
      case ENUMS_IMPORT_TYPE.jobs:
        setHeaderTitle(`${t('settings:import:job_fields')}`)
        break
      case ENUMS_IMPORT_TYPE.candidate:
        setHeaderTitle(`${t('settings:import:profile_fields')}`)
        break
      default:
        setHeaderTitle(`${t('settings:import:job_fields')}`)
        break
    }
  }, [type])

  return (
    <DynamicImportForm
      id="mapping-data-form"
      className="h-full w-full"
      schema={schemaMappingDataForm(t)}
      onSubmit={onImport}
      defaultValue={defaultValues}>
      {({ formState, control }) => {
        return (
          <div className="flex h-full flex-col justify-between">
            <div className="flex-1 overflow-auto">
              <div className="mb-5">
                <ImportProgressBar currentStep={1} data={PROGRESS_IMPORT(t)} />
              </div>
              <div>
                <Controller
                  control={control}
                  name="mappedFields"
                  defaultValue={undefined}
                  render={({ field: { onChange, value } }) => {
                    return (
                      <div>
                        <TableInfinityOrdering
                          search={undefined}
                          dataQuery={{
                            isFetching: false,
                            isLoading: false,
                            isFetchedAfterMount: true,
                            fetcher: {
                              fetchNextPage: () => {}
                            },
                            data: {
                              pages: [
                                {
                                  data: value,
                                  meta: {
                                    totalRowCount: value.length,
                                    currentPage: 1
                                  }
                                }
                              ]
                            }
                          }}
                          columns={[
                            {
                              accessorKey: 'fileField',
                              header: () => (
                                <Trans
                                  i18nKey={'settings:import:file_fields'}
                                />
                              ),
                              cell: (info: {
                                row: { original: MappedFieldType }
                                getValue: Function
                              }) => (
                                <div className="flex items-center text-sm text-gray-900">
                                  {info?.row?.original?.fileField}
                                </div>
                              ),
                              footer: (props) => props.column.id,
                              size: 350
                            },
                            {
                              accessorKey: 'arrow',
                              header: () => <div />,
                              cell: (info: {
                                row: { original: MappedFieldType }
                                getValue: Function
                              }) => (
                                <div className="flex items-center text-sm text-gray-900">
                                  <IconWrapper
                                    name="ArrowRight"
                                    size={14}
                                    className={
                                      info?.row?.original?.mappedUuid
                                        ? 'text-gray-600'
                                        : 'text-gray-400'
                                    }
                                  />
                                </div>
                              ),
                              footer: (props) => props.column.id,
                              size: 36
                            },
                            {
                              accessorKey: 'jobField',
                              header: () => (
                                <TypographyText className="text-xs text-gray-600 dark:text-gray-300">
                                  {headerTitle}
                                </TypographyText>
                              ),
                              cell: (info: {
                                row: {
                                  original: MappedFieldType
                                  index: number
                                }
                                getValue: Function
                              }) => (
                                <div className="flex items-center text-sm text-gray-900">
                                  <ComboboxSelect
                                    type="unstyled"
                                    options={promiseFields}
                                    size="sm"
                                    dropdownMenuClassName="!w-[240px]"
                                    menuOptionSide="bottom"
                                    containerMenuClassName="max-w-[240px]"
                                    onChange={(newValue) => {
                                      const formattedMappedFields =
                                        value.reduce((result, fileField) => {
                                          return fileField.fileField !==
                                            info?.row?.original?.fileField
                                            ? [...result, fileField]
                                            : [
                                                ...result,
                                                {
                                                  ...fileField,
                                                  name: newValue
                                                    ? String(
                                                        (
                                                          newValue as ISelectOption
                                                        )?.supportingObj?.name
                                                      )
                                                    : null,
                                                  mappedUuid: newValue
                                                    ? (
                                                        newValue as ISelectOption
                                                      )?.value
                                                    : null,
                                                  required: !!(
                                                    newValue as ISelectOption
                                                  )?.supportingObj?.nameRequired
                                                }
                                              ]
                                        }, [] as MappedFieldType[])
                                      onChange(formattedMappedFields)
                                      setMappedFields(formattedMappedFields)
                                    }}
                                    placeholder={`${t('form:select')}`}
                                    searchPlaceholder={`${t(
                                      'common:sidebar:search'
                                    )}`}
                                    loadingMessage={`${t('label:loading')}`}
                                    noOptionsMessage={`${t('label:noOptions')}`}
                                    buttonClassName="[&>div]:!font-normal"
                                    value={
                                      info?.row?.original?.mappedUuid
                                        ? {
                                            value:
                                              info?.row?.original?.mappedUuid,
                                            supportingObj: {
                                              name:
                                                info?.row?.original?.name || '',
                                              nameRequired:
                                                info?.row?.original?.required
                                            }
                                          }
                                        : undefined
                                    }
                                  />
                                </div>
                              ),
                              footer: (props) => props.column.id,
                              size: 350
                            }
                          ]}
                          isHeaderSticky
                          classNameTable={cn(
                            'max-w-full bg-white dark:bg-gray-900',
                            topSpace
                          )}
                        />
                      </div>
                    )
                  }}
                />
              </div>
            </div>

            <div className="sticky bottom-0 flex flex-none items-center justify-between bg-white py-4">
              <Button
                type="secondary"
                size="sm"
                label={`${t('button:back')}`}
                iconMenus="ArrowLeft"
                icon="leading"
                onClick={previousStep}
              />
              <Button
                htmlType="submit"
                size="sm"
                isDisabled={isLoadingImport}
                label={`${t('settings:import:import')}`}
                iconMenus="Upload"
                icon="trailing"
              />
            </div>
          </div>
        )
      }}
    </DynamicImportForm>
  )
}

export default MappingDataStep
