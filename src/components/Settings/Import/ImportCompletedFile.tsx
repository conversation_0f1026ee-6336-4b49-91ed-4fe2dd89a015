import { useRouter } from 'next/router'
import { FC, useMemo, useRef, useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import configuration from '~/configuration'
import { Button } from '~/core/ui/Button'
import { TablePagination } from '~/core/ui/TablePagination'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import MutationImportReparseSkills from '~/lib/features/settings/import/graphql/mutation-import-reparse-skills'
import QueryImportedJobHistory from '~/lib/features/settings/import/graphql/query-import-file-history'
import {
  ImportFileHistoriesType,
  ISourceImportType,
  MappedFieldType
} from '~/lib/features/settings/import/types'
import { ENUM_IMPORT_FILE_STATUS } from '~/lib/features/settings/import/utilities/enum'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import usePaginationGraphPage from '~/lib/hooks/use-pagination-graph-page'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'
import ImportSummaryFilter, {
  DEFAULT_IMPORT_KEYS,
  SummaryTotalCountIcon
} from './components/ImportSummaryFilter'

const TAB_KEYS = {
  history: 'history'
}

const ImportCompletedFile: FC<{
  onTryAgain?: () => void
  onBackToHistoryTabList?: () => void
  tab?: string
  importType: ISourceImportType
}> = ({ onTryAgain, onBackToHistoryTabList, tab, importType }) => {
  const { t } = useTranslation()
  const { query } = useRouter()
  const { setToast } = useToastStore()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const { trigger, isLoading } = useSubmitCommon(MutationImportReparseSkills)
  const [isDisabledButton, setDisabledButton] = useState(false)
  const [importKeys, setImportKeys] = useState<Array<string>>([])

  const { data, isFetching, fetchPagination, forceChangeCurrentPage } =
    usePaginationGraphPage({
      queryDocumentNode: QueryImportedJobHistory,
      queryKey: 'my-import-completed-file',
      filter: {
        keys: importKeys?.length ? importKeys : DEFAULT_IMPORT_KEYS,
        importId: Number(query['import_id'])
      },
      enabled: !!Number(query['import_id'])
    })

  const handleTriggerImportReparseSkills = async () => {
    if (isLoading) return
    const formatData = {
      importId: Number(query['import_id'])
    }

    trigger(formatData).then((result) => {
      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast
        })
      }

      const { jobsImportReparseSkills } = result.data
      if (jobsImportReparseSkills?.success) {
        setDisabledButton(true)
      }

      return
    })
  }

  const columnsField = useMemo(
    () =>
      (data?.meta?.extras?.reorder_mapped_fields || []).map(
        (item: MappedFieldType) => item.fileField
      ),
    [data]
  )
  const mappedFields = useMemo(
    () =>
      (data?.meta?.extras?.reorder_mapped_fields || [])?.reduce(
        (result: { [key: string]: string }, mappedField: MappedFieldType) => {
          return { ...result, [mappedField?.fileField]: mappedField?.name }
        },
        {} as { [key: string]: string }
      ),
    [data]
  )
  const isEnabledFeature =
    isFeatureEnabled(PLAN_FEATURE_KEYS.ai_skill_parser) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.ai_skill_parser)
  const skillReparsable = data?.meta?.extras?.skill_reparsable
  const skillReparsableCount = data?.meta?.extras?.skill_reparsable_count

  const topSpace = useClassBasedTopSpace({
    34:
      tab === TAB_KEYS.history
        ? 'h-[calc(100vh_-_330px)]'
        : 'h-[calc(100vh_-_353px)]',
    default:
      tab === TAB_KEYS.history
        ? 'h-[calc(100vh_-_296px)]'
        : 'h-[calc(100vh_-_319px)]'
  })

  return !columnsField ? null : (
    <div className="flex h-full flex-col justify-between">
      <div className="flex-1">
        <div className="mb-4 flex gap-x-4">
          {/* List jobs without status no_skills */}
          <ImportSummaryFilter
            importKeys={importKeys}
            data={data}
            transTooltipKey="filterJobsByStatus"
            isEnabledAiSkillParser={isEnabledFeature}
            setImportKeys={setImportKeys}
          />

          {/* Jobs with status no_skills */}
          {isEnabledFeature && skillReparsableCount > 0 ? (
            <>
              <TextButton
                size="md"
                underline={false}
                label={`${t('button:aiReparseSkills')}`}
                isDisabled={!skillReparsable || isDisabledButton}
                iconMenus={isDisabledButton ? 'Loader2' : undefined}
                iconMenusLoading={isDisabledButton ? 'animate-spin' : undefined}
                icon="trailing"
                onClick={handleTriggerImportReparseSkills}
                classNameText="font-medium"
              />
            </>
          ) : null}
        </div>
        <TablePagination
          search={{
            globalFilter: `search`,
            filter: {}
          }}
          textOverride={{
            of: `${t('label:of')}`,
            page: `${t('label:page')}`,
            placeholder: `${t('label:placeholder:select')}`,
            search: `${t('label:placeholder:search')}`,
            loading: `${t('label:loading')}`,
            noOptions: `${t('label:noOptions')}`,
            rowsPerPage: `${t('label:rowsPerPage')}`
          }}
          emptyConfig={{
            classNameEmpty: cn(
              'h-full items-center justify-center flex',
              topSpace
            ),
            titleSearch: `${t('settings:import:empty_history:titleSearch')}`,
            descriptionSearch: `${t(
              'settings:import:empty_history:descriptionSearch'
            )}`,
            buttonTitleSearch: `${t('button:clearFilter')}`,
            buttonTitleSearchOnClick: () => setImportKeys([])
          }}
          tableConfig={{
            defaultPageSize: configuration.defaultPageSize
          }}
          isHeaderSticky
          stickyConfig={[
            { index: 0, position: 'left', value: 0 },
            { index: 1, position: 'left', value: 30, useShadow: true },
            { index: 2, position: 'left', value: 126, useShadow: true }
          ]}
          dataQuery={{
            isFetching,
            fetcher: {
              fetchPagination,
              forceChangeCurrentPage
            },
            data: {
              ...data,
              data: (data?.data || []).map((item) => ({
                ...item,
                id: item.data.key
              }))
            }
          }}
          classNameTable={cn('', topSpace)}
          classNamePaginationWrapper="bg-white pt-4 pb-6"
          columns={[
            {
              accessorKey: 'status',
              header: () => <div />,
              cell: (info: {
                row: { original: ImportFileHistoriesType }
                getValue: Function
              }) => (
                <div className="flex">
                  <Tooltip
                    content={
                      info?.row?.original?.grouppedKey ===
                      ENUM_IMPORT_FILE_STATUS.failed
                        ? info?.row?.original?.data?.message
                        : ''
                    }
                    classNameConfig={{
                      content:
                        info?.row?.original?.grouppedKey ===
                        ENUM_IMPORT_FILE_STATUS.failed
                          ? ''
                          : 'hidden'
                    }}>
                    <SummaryTotalCountIcon
                      type={info?.row?.original?.grouppedKey}
                    />
                  </Tooltip>
                </div>
              ),
              footer: (props) => props.column.id,
              size: 30
            },
            {
              accessorKey: 'jobID',
              header: () => t('settings:import:job_id'),
              cell: (info: {
                row: { original: ImportFileHistoriesType }
                getValue: Function
              }) => (
                <div className="line-clamp-1 break-all text-sm text-gray-900">
                  <a
                    href={configuration.path.jobs.detail(
                      info?.row?.original?.data?.jobId
                    )}
                    className="hover:underline"
                    target="_blank">
                    {info?.row?.original?.data?.publicJobId}
                  </a>
                </div>
              ),
              footer: (props) => props.column.id,
              size: 96
            },
            ...columnsField.map((columnKey: string, index: number) => ({
              accessorKey: `${columnKey}-${index}`,
              header: () => mappedFields?.[columnKey],
              cell: (info: {
                row: { original: ImportFileHistoriesType }
                getValue: Function
              }) => (
                <div className="flex">
                  <Tooltip
                    content={info?.row?.original?.rawData?.[columnKey]}
                    classNameAsChild="line-clamp-1 break-all text-sm text-gray-900">
                    {info?.row?.original?.rawData?.[columnKey]}
                  </Tooltip>
                </div>
              ),
              footer: (props: { column: { id: string } }) => props.column.id
            }))
          ]}
        />
      </div>

      <div
        className={cn(
          'sticky bottom-0 flex flex-none items-center bg-white py-4',
          tab === TAB_KEYS.history ? 'justify-between' : 'justify-end'
        )}>
        {tab === TAB_KEYS.history ? (
          <>
            <Button
              size="sm"
              type="secondary"
              label={`${t('button:back')}`}
              iconMenus="ArrowLeft"
              icon="leading"
              onClick={onBackToHistoryTabList}
            />
            <Button
              size="sm"
              label={`${t('settings:import:reupload')}`}
              iconMenus="RotateCcw"
              icon="trailing"
              onClick={onTryAgain}
            />
          </>
        ) : (
          <Button
            size="sm"
            label={`${t('settings:import:reupload')}`}
            iconMenus="RotateCcw"
            icon="trailing"
            onClick={onTryAgain}
          />
        )}
      </div>
    </div>
  )
}

export default ImportCompletedFile
