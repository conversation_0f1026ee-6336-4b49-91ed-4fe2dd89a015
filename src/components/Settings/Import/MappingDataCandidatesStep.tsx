import { useRouter } from 'next/router'
import { FC, useCallback, useState } from 'react'
import { Controller } from 'react-hook-form'
import { Trans, useTranslation } from 'react-i18next'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import configuration from '~/configuration'
import useContextGraphQL, {
  IResponseContextResult
} from '~/core/middleware/use-context-graphQL'
import { Button } from '~/core/ui/Button'
import ComboboxSelect from '~/core/ui/ComboboxSelect'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import IconWrapper from '~/core/ui/IconWrapper'
import { IPromiseSearchOption, ISelectOption } from '~/core/ui/Select'
import { TableInfinityOrdering } from '~/core/ui/TableInfinityOrdering'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import MutationImportProfiles from '~/lib/features/settings/import/graphql/mutation-import-profiles'
import QueryProfileFields from '~/lib/features/settings/import/graphql/query-profile-fields'
import schemaMappingDataForm from '~/lib/features/settings/import/schema/schema-mapping-data-form'
import {
  MappedFieldType,
  PartialFieldType
} from '~/lib/features/settings/import/types'
import { PROGRESS_IMPORT } from '~/lib/features/settings/import/utilities/enum'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useToastStore from '~/lib/store/toast'
import useLoadingBlockStore from '~/lib/store/loading-block'
import ImportProgressBar from './components/ImportProgressBar'

const MappingDataCandidatesStep: FC<{
  file?: File
  defaultValues?: {
    mappedFields?: MappedFieldType[]
  }
  previousStep?: () => void
}> = ({ defaultValues, file, previousStep }) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { clientGraphQL } = useContextGraphQL()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const [mappedFields, setMappedFields] = useState<
    MappedFieldType[] | undefined
  >(defaultValues?.mappedFields)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const { trigger } = useSubmitCommon(MutationImportProfiles)
  const promiseProfileFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryProfileFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<PartialFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { profileFieldsList } = result.data
          const collection = profileFieldsList?.collection || []
          const metadata = profileFieldsList?.metadata || {}

          const cloneData = collection.map((item) => {
            return {
              value: item.uuid,
              supportingObj: {
                name: item.name,
                nameRequired: item.required
              }
            }
          })

          return {
            metadata,
            collection: cloneData
          }
        }),
    [mappedFields]
  )

  const onFinish = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setIsLoading(true)
      setShowLockApp('')
      return trigger({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          setCloseLockApp()
          setIsLoading(false)
          const errorMessage = result.error.graphQLErrors?.[0]?.message
          if (errorMessage.includes('duplicated'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:duplicated_title')}`,
              description: errorMessage
            })
          else if (errorMessage.includes('required fields'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:import_mapping_require_title')}`,
              description: errorMessage
            })
          else
            catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router
            })
          return Promise.resolve(false)
        }

        if (result.data.profilesImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?tab=history&import_id=${result.data.profilesImport?.import.id}&object_kind=profile`
          )
        }

        return Promise.resolve(true)
      })
    },
    [file]
  )

  const topSpace = useClassBasedTopSpace({
    34: 'h-[calc(100vh_-_330px)]',
    default: 'h-[calc(100vh_-_296px)]'
  })

  return (
    <DynamicImportForm
      id="mapping-data-form"
      className="h-full w-full"
      schema={schemaMappingDataForm(t)}
      onSubmit={onFinish}
      defaultValue={defaultValues}>
      {({ formState, control }) => {
        return (
          <div className="flex h-full flex-col justify-between">
            <div className="flex-1 overflow-auto">
              <div className="mb-5">
                <ImportProgressBar currentStep={1} data={PROGRESS_IMPORT(t)} />
              </div>
              <div>
                <Controller
                  control={control}
                  name="mappedFields"
                  defaultValue={undefined}
                  render={({ field: { onChange, value } }) => {
                    return (
                      <div>
                        <TableInfinityOrdering
                          search={undefined}
                          dataQuery={{
                            isFetching: false,
                            isLoading: false,
                            isFetchedAfterMount: true,
                            fetcher: {
                              fetchNextPage: () => {}
                            },
                            data: {
                              pages: [
                                {
                                  data: value,
                                  meta: {
                                    totalRowCount: value.length,
                                    currentPage: 1
                                  }
                                }
                              ]
                            }
                          }}
                          columns={[
                            {
                              accessorKey: 'fileField',
                              header: () => (
                                <Trans
                                  i18nKey={'settings:import:file_fields'}
                                />
                              ),
                              cell: (info: {
                                row: { original: MappedFieldType }
                                getValue: Function
                              }) => (
                                <div className="flex items-center text-sm text-gray-900">
                                  {info?.row?.original?.fileField}
                                </div>
                              ),
                              footer: (props) => props.column.id,
                              size: 350
                            },
                            {
                              accessorKey: 'arrow',
                              header: () => <div />,
                              cell: (info: {
                                row: { original: MappedFieldType }
                                getValue: Function
                              }) => (
                                <div className="flex items-center text-sm text-gray-900">
                                  <IconWrapper
                                    name="ArrowRight"
                                    size={14}
                                    className={
                                      info?.row?.original?.mappedUuid
                                        ? 'text-gray-600'
                                        : 'text-gray-400'
                                    }
                                  />
                                </div>
                              ),
                              footer: (props) => props.column.id,
                              size: 36
                            },
                            {
                              accessorKey: 'jobField',
                              header: () => (
                                <Trans
                                  i18nKey={'settings:import:profile_fields'}
                                />
                              ),
                              cell: (info: {
                                row: {
                                  original: MappedFieldType
                                  index: number
                                }
                                getValue: Function
                              }) => (
                                <div className="flex items-center text-sm text-gray-900">
                                  <ComboboxSelect
                                    type="unstyled"
                                    options={promiseProfileFields}
                                    size="sm"
                                    dropdownMenuClassName="!w-[240px]"
                                    menuOptionSide="bottom"
                                    containerMenuClassName="max-w-[240px]"
                                    onChange={(newValue) => {
                                      const formattedMappedFields =
                                        value.reduce((result, fileField) => {
                                          return fileField.fileField !==
                                            info?.row?.original?.fileField
                                            ? [...result, fileField]
                                            : [
                                                ...result,
                                                {
                                                  ...fileField,
                                                  name: String(
                                                    (newValue as ISelectOption)
                                                      ?.supportingObj?.name
                                                  ),
                                                  mappedUuid: (
                                                    newValue as ISelectOption
                                                  )?.value,
                                                  required: !!(
                                                    newValue as ISelectOption
                                                  )?.supportingObj?.nameRequired
                                                }
                                              ]
                                        }, [] as MappedFieldType[])
                                      onChange(formattedMappedFields)
                                      setMappedFields(formattedMappedFields)
                                    }}
                                    placeholder={`${t('form:select')}`}
                                    searchPlaceholder={`${t(
                                      'common:sidebar:search'
                                    )}`}
                                    loadingMessage={`${t('label:loading')}`}
                                    noOptionsMessage={`${t('label:noOptions')}`}
                                    buttonClassName="[&>div]:!font-normal"
                                    value={
                                      info?.row?.original?.mappedUuid
                                        ? {
                                            value:
                                              info?.row?.original?.mappedUuid,
                                            supportingObj: {
                                              name:
                                                info?.row?.original?.name || '',
                                              nameRequired:
                                                info?.row?.original?.required
                                            }
                                          }
                                        : undefined
                                    }
                                  />
                                </div>
                              ),
                              footer: (props) => props.column.id,
                              size: 350
                            }
                          ]}
                          isHeaderSticky
                          classNameTable={cn(
                            'max-w-full bg-white dark:bg-gray-900',
                            topSpace
                          )}
                        />
                      </div>
                    )
                  }}
                />
              </div>
            </div>

            <div className="sticky bottom-0 flex flex-none items-center justify-between bg-white py-4">
              <Button
                type="secondary"
                size="sm"
                label={`${t('button:back')}`}
                iconMenus="ArrowLeft"
                icon="leading"
                onClick={previousStep}
              />
              <Button
                htmlType="submit"
                size="sm"
                isDisabled={isLoading}
                label={`${t('settings:import:import')}`}
                iconMenus="Upload"
                icon="trailing"
              />
            </div>
          </div>
        )
      }}
    </DynamicImportForm>
  )
}

export default MappingDataCandidatesStep
