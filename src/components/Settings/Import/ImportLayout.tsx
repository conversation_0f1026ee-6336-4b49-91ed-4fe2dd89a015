import { useRouter } from 'next/router'
import {
  Dispatch,
  FC,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useState
} from 'react'
import { useTranslation } from 'react-i18next'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import configuration from '~/configuration'
import { PageHeaderSimple } from '~/core/ui/PageHeaderSimple'
import { Skeleton } from '~/core/ui/Skeleton'
import {
  ISourceImportType,
  MappedFieldType
} from '~/lib/features/settings/import/types'
import useBrowserTab from '~/lib/hooks/use-browser-tab'
import ImportFileHistoryTab from './ImportFileHistoryTab'
import MappingDataStep from './MappingDataStep'
import UploadFilesStep from './UploadFilesStep'
import { IMPORT_TYPE_URL } from '~/lib/features/settings/import/utilities/enum'
import { TextButton } from '~/core/ui/TextButton'
import pathConfiguration from 'src/configuration/path'
import {
  CANDIDATE_TAB,
  COMPANY_TAB,
  JOBS_TAB
} from '~/lib/features/candidates/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { AGENCY_TENANT } from '~/core/constants/enum'

const ImportLayout: FC<{
  listSourceImportType: ISourceImportType[]
  suspend?: boolean
  actionUploadFile?: {
    goToUploadFileStep?: () => void
    nextStep: () => void
    previousStep: () => void
  }

  importFileStatus?: string
  currentStep?: number
  importDetail?: (tab?: string) => ReactNode
  importType: ISourceImportType
}> = ({
  suspend,
  actionUploadFile,
  importFileStatus,
  currentStep,
  importDetail,
  importType,
  listSourceImportType
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const [dataOutputStepUploadFile, setDataOutputStepUploadFile] = useState<{
    mappedFields?: MappedFieldType[]
    file?: File
  }>()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const importJobLink = pathConfiguration.helpCenter.importJobDirect
  const tabControl = useBrowserTab({
    defaultValue: 'import',
    queryKeyName: 'tab'
  })
  const topSpace = useClassBasedTopSpace({
    34: 'h-[calc(100vh_-_181px)]',
    default: 'h-[calc(100vh_-_147px)]'
  })

  const onGoToStepMappingData = useCallback(
    ({
      mappedFields,
      file
    }: {
      mappedFields?: MappedFieldType[]
      file?: File
    }) => {
      return Promise.resolve().then(() => {
        setDataOutputStepUploadFile({ mappedFields, file })
        actionUploadFile?.nextStep()
      })
    },
    [currentStep]
  )

  const tabs = [
    {
      value: 'import',
      label: `${t('settings:import:import')}`,
      children: (
        <div className="mt-5 h-full">
          {suspend ? (
            <div className={`${topSpace}`} />
          ) : typeof currentStep !== 'number' && !importFileStatus ? (
            <div className="">
              {[1, 2, 3, 4].map((item) => (
                <div key={`skeleton-${item}`} className="mt-4">
                  <Skeleton className="mb-4 h-5 w-full rounded" />
                </div>
              ))}
            </div>
          ) : (
            <>
              {!importFileStatus &&
                {
                  0: (
                    <UploadFilesStep
                      listSourceImportType={listSourceImportType}
                      importType={importType}
                      finishCallback={onGoToStepMappingData}
                    />
                  ),
                  1: (
                    <MappingDataStep
                      file={dataOutputStepUploadFile?.file}
                      defaultValues={{
                        mappedFields: dataOutputStepUploadFile?.mappedFields
                      }}
                      previousStep={actionUploadFile?.previousStep}
                      type={importType.value}
                    />
                  )
                }[currentStep || 0]}
              {importDetail && importDetail(tabControl.value)}
            </>
          )}
        </div>
      )
    },
    {
      value: 'history',
      label: `${t('settings:import:history')}`,
      children: (
        <div className="h-full">
          {!!router.query['import_id'] ? (
            <>{importDetail && importDetail(tabControl.value)}</>
          ) : (
            <ImportFileHistoryTab
              goToUploadFileStep={actionUploadFile?.goToUploadFileStep}
              listSourceImportType={listSourceImportType}
              importType={importType}
            />
          )}
        </div>
      )
    }
  ]

  return (
    <div className="h-full w-full overflow-hidden px-6 pt-6">
      <PageHeaderSimple
        {...tabControl}
        title={`${t('settings:import:import')}`}
        description={
          <>
            {t(`settings:import:sources:${importType?.value}:description`)}
            {![CANDIDATE_TAB, COMPANY_TAB].includes(importType?.value) &&
            !isCompanyKind ? (
              <TextButton
                className="contents"
                classNameText="font-normal ml-1"
                label={`${t('common:learn_more')}`}
                onClick={() => {
                  importType?.value === JOBS_TAB
                    ? window.open(importJobLink, '_blank')
                    : window.open(
                        pathConfiguration.helpCenter.importCourse,
                        '_blank'
                      )
                }}
              />
            ) : (
              ''
            )}
          </>
        }
        classNameWrapper="space-y-[26px]"
        // Rendered Components
        tabs={tabs}
      />
    </div>
  )
}

export default ImportLayout
