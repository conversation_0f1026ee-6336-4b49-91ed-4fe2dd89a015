import { FC } from 'react'
import { Trans, useTranslation } from 'react-i18next'
import { useClassBasedTopSpace } from '~/components/Subscription/TopSpace'
import configuration from '~/configuration'
import { Button } from '~/core/ui/Button'
import { TableInfinityOrdering } from '~/core/ui/TableInfinityOrdering'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { cn } from '~/core/ui/utils'
import useImportHandler from '~/lib/features/settings/import/hooks/use-import'
import {
  ImportFileCoursesHistoriesType,
  ImportFileHistoriesType,
  ImportFileProfilesHistoriesType,
  ISourceImportType
} from '~/lib/features/settings/import/types'
import { PROGRESS_IMPORT } from '~/lib/features/settings/import/utilities/enum'
import { useInfinityQuerySearch } from '~/lib/hooks/use-infinity-query-search'
import ImportProgressBar from './ImportProgressBar'

const FailedIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="48"
    height="48"
    viewBox="0 0 48 48"
    fill="none">
    <circle cx="24" cy="24" r="23.5" fill="#FEF2F2" stroke="#FEE2E2" />
    <g clipPath="url(#clip0_26087_43858)">
      <path
        d="M34.5889 28.0642C31.9761 23.2633 29.3702 18.4598 26.7479 13.6642C26.2019 12.6674 25.3246 12.0953 24.1909 12.0116C22.7546 11.9061 21.6775 12.5235 20.9868 13.7854C19.5915 16.3354 18.2048 18.8898 16.8173 21.4441C15.5685 23.743 14.3092 26.0375 13.0796 28.3476C12.4403 29.5476 12.763 31.1601 13.7598 32.0889C14.4374 32.7203 15.2345 33.002 16.1554 33.0002C18.7316 32.9933 21.3086 32.9976 23.8857 32.9976C26.433 32.9976 28.9804 32.9976 31.5287 32.9976C31.6813 32.9976 31.8339 32.9976 31.9857 32.988C33.0278 32.9148 33.8676 32.4595 34.4519 31.597C35.2246 30.4581 35.2447 29.2703 34.5889 28.0651V28.0642Z"
        fill="#EE5959"
      />
      <path
        d="M17.2133 27.5585C16.7032 29.1788 17.9128 30.8288 19.6116 30.8288H34.8549C35.1976 29.928 35.0991 29.0018 34.5889 28.0652C31.9761 23.2643 29.3703 18.4608 26.7479 13.6652C26.202 12.6675 25.3247 12.0954 24.191 12.0117C23.284 11.9445 22.5209 12.1678 21.9069 12.6579L17.2133 27.5585Z"
        fill="#F26F6F"
      />
      <path
        d="M23.9189 17.2823C22.9665 17.277 22.227 18.0767 22.3072 19.0238C22.4633 20.8805 22.6186 22.7372 22.7738 24.593C22.8235 25.1904 23.3101 25.6465 23.8997 25.6482C24.4892 25.65 24.9819 25.1982 25.0325 24.6009C25.1904 22.7372 25.3465 20.8735 25.5017 19.0099C25.5785 18.0837 24.8476 17.2875 23.9189 17.2823Z"
        fill="white"
      />
      <path
        d="M23.9058 27.0459C23.1444 27.0442 22.5165 27.6633 22.5113 28.4238C22.5052 29.1878 23.1209 29.8209 23.8778 29.8305C24.6496 29.8401 25.2871 29.22 25.2959 28.4508C25.3046 27.6817 24.6776 27.0476 23.9066 27.0459H23.9058Z"
        fill="white"
      />
      <path
        d="M26.7104 16.3104C26.5883 16.3104 26.4706 16.2467 26.4061 16.1334L25.602 14.7101C25.5069 14.5427 25.5662 14.3299 25.7337 14.2348C25.902 14.1398 26.1139 14.1991 26.209 14.3665L27.013 15.7898C27.1081 15.9572 27.0488 16.17 26.8814 16.265C26.8273 16.2956 26.768 16.3104 26.7104 16.3104Z"
        fill="#FEE2E2"
      />
    </g>
    <defs>
      <clipPath id="clip0_26087_43858">
        <rect
          width="22.3073"
          height="21"
          fill="white"
          transform="translate(12.75 12)"
        />
      </clipPath>
    </defs>
  </svg>
)

const ImportFailedFile: FC<{
  onTryAgain?: () => void
  onBackToHistoryTabList?: () => void
  importType: ISourceImportType
  tab?: string
}> = ({ onTryAgain, onBackToHistoryTabList, tab, importType }) => {
  const { t } = useTranslation()
  const { getImportedHistory } = useImportHandler({
    importType
  })

  const { data, fetchNextPage, isFetching, isLoading, isFetchedAfterMount } =
    useInfinityQuerySearch<
      | ImportFileHistoriesType
      | ImportFileProfilesHistoriesType
      | ImportFileCoursesHistoriesType
    >({
      configuration,
      fetchData: getImportedHistory,
      queryKey: {}
    })

  const topSpace = useClassBasedTopSpace({
    34:
      tab === 'history' ? 'h-[calc(100vh_-_381px)]' : 'h-[calc(100vh_-_404px)]',
    default:
      tab === 'history' ? 'h-[calc(100vh_-_347px)]' : 'h-[calc(100vh_-_370px)]'
  })

  return (
    <div className="flex h-full flex-col justify-between">
      <div className="flex-1">
        <div className="mb-5">
          <ImportProgressBar currentStep={2} data={PROGRESS_IMPORT(t)} />
        </div>
        <div className="mb-6 flex items-center gap-x-4">
          <div className="flex-none">
            <FailedIcon />
          </div>
          <div>
            <TypographyText className="mb-0.5 text-base font-medium text-gray-900">
              {t('settings:import:import_failed')}
            </TypographyText>
            <TypographyText className="text-sm text-gray-700">
              {t('settings:import:update_and_try_again')}
            </TypographyText>
          </div>
        </div>
        <TableInfinityOrdering
          search={{
            globalFilter: '',
            filter: {}
          }}
          emptyConfig={{}}
          tableConfig={{
            defaultFirstLoadingSize: configuration.defaultTableMountPageSize,
            useInfinity: false,
            renderButton: (
              <TextButton
                label={t('common:table:show25More')}
                type="tertiary"
                underline={false}
                size="md"
              />
            ),
            endOfList: `${t('common:table:endOfList')}`
          }}
          isHeaderSticky
          dataQuery={{
            isFetching,
            isLoading,
            isFetchedAfterMount,
            fetcher: {
              fetchNextPage
            },
            data
          }}
          classNameTable={`${topSpace}`}
          columns={[
            {
              accessorKey: 'column',
              header: () => {
                return (
                  <span className="mr-2">
                    <Trans i18nKey={'settings:import:column'} />
                  </span>
                )
              },
              cell: (info: {
                row: { original: ImportFileHistoriesType }
                getValue: Function
              }) => (
                <div className="text-sm text-gray-900">
                  {info?.row?.original?.data?.column}
                </div>
              ),
              footer: (props) => props.column.id,
              size: 180
            },
            {
              accessorKey: 'row',
              header: () => {
                return (
                  <span className="mr-2">
                    <Trans i18nKey={'settings:import:row'} />
                  </span>
                )
              },
              cell: (info: {
                row: { original: ImportFileHistoriesType }
                getValue: Function
              }) => (
                <div className="text-sm font-medium text-gray-900">
                  {info?.row?.original?.data?.row}
                </div>
              ),
              footer: (props) => props.column.id,
              size: 100
            },
            {
              accessorKey: 'reason',
              header: () => {
                return (
                  <span className="mr-2">
                    <Trans i18nKey={'settings:import:reason'} />
                  </span>
                )
              },
              cell: (info: {
                row: { original: ImportFileHistoriesType }
                getValue: Function
              }) => (
                <div className="text-sm text-gray-900">
                  {info?.row?.original?.data?.description || ''}
                </div>
              ),
              footer: (props) => props.column.id,
              size: 460
            }
          ]}
          columnVisibility={{
            column: true,
            row: true,
            reason: true
          }}
        />
      </div>

      <div
        className={cn(
          'sticky bottom-0 flex flex-none items-center bg-white py-4',
          tab === 'history' ? 'justify-between' : 'justify-end'
        )}>
        {tab === 'history' ? (
          <>
            <Button
              size="sm"
              type="secondary"
              label={`${t('button:back')}`}
              iconMenus="ArrowLeft"
              icon="leading"
              onClick={onBackToHistoryTabList}
            />
            <Button
              size="sm"
              label={`${t('settings:import:reupload')}`}
              iconMenus="RotateCcw"
              icon="trailing"
              onClick={onTryAgain}
            />
          </>
        ) : (
          <Button
            size="sm"
            label={`${t('settings:import:reupload')}`}
            iconMenus="RotateCcw"
            icon="trailing"
            onClick={onTryAgain}
          />
        )}
      </div>
    </div>
  )
}

export default ImportFailedFile
