import Link from 'next/link'
import { useRouter } from 'next/router'
import { Trans, useTranslation } from 'react-i18next'
import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { adminCanAction } from '~/core/utilities/permission'
import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'
import { useClassBasedTopSpace } from '../Subscription/TopSpace'
import useSubscriptionPlan from '../Subscription/useSubscriptionPlan'
import SidebarItemSettings from './SidebarItemSettings'

// const SidebarSearch = dynamic(() => import('./SidebarSearch'), {
//   ssr: false
// })

const SidebarSettings = () => {
  const { t } = useTranslation()
  const router = useRouter()
  const { user, currentRole } = useBoundStore()
  const {
    isFeaturePremiumShow,
    isFeatureEnabled,
    isUnLockFeature,
    showIconFeatureFromAdminSetting
  } = useSubscriptionPlan()
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })
  const isShowCompanyFeature =
    isFeatureEnabled(PLAN_FEATURE_KEYS.company) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.company)
  const {
    actionCareer: canAccessCareer,
    actionDepartment: canAccessDepartment,
    actionLocation: canAccessLocation,
    actionUser: canAccessUser,
    actionPosition: canAccessPosition,
    // actionReferral: canAccessReferral,
    actionManageReferral: canManageReferral,
    actionCompanyStatus: canAccessCompanyStatus,
    actionCustomField: canAccessCustomField,
    actionTag: canAccessTag,
    actionRejectedReason: canAccessRejectedReason,
    canAccessModule,
    actionEmailTemplate: canAccessEmailTemplate,
    actionPipelineTemplate: canAccessPipelineTemplate,
    actionIkitTemplate: canAccessIkitTemplate,
    actionProfileTemplate: canAccessProfileTemplate,
    actionTeam: canAccessTeam
  } = usePermissionSetting()
  const topSpace = useClassBasedTopSpace({
    34: 'h-[calc(100%_-_34px)]',
    default: 'h-full'
  })

  const checkPermissionWorkspaceTitle = () => {
    if (adminCanAction(currentRole?.code)) return true
    if (canAccessModule) return true
    if (canAccessCareer) return true
    if (canAccessDepartment) return true
    if (canAccessUser) return true
    if (canAccessLocation) return true
    if (user?.ownTenant) return true
    if (canAccessTeam) return true

    return false
  }

  const checkPermissionRecruitingTitle = () => {
    if (
      (canAccessModule && isFeatureEnabled(PLAN_FEATURE_KEYS.requisition)) ||
      user.ownTenant
    )
      return true

    if (
      (canManageReferral && isFeatureEnabled(PLAN_FEATURE_KEYS.referral)) ||
      user.ownTenant
    )
      return true

    if (canAccessCompanyStatus) return true
    if (canAccessCustomField) return true
    if (canAccessTag) return true
    if (canAccessRejectedReason) return true

    return false
  }

  const checkPermissionTemplatesTitle = () => {
    if (canAccessEmailTemplate) return true
    if (canAccessPipelineTemplate) return true
    if (canAccessIkitTemplate) return true
    if (canAccessProfileTemplate) return true

    return false
  }

  const showSecurityWithAdmin = () => {
    return (
      isFeatureEnabled(PLAN_FEATURE_KEYS.security_setting) &&
      isUnLockFeature(PLAN_FEATURE_KEYS.security_setting) &&
      adminCanAction(currentRole?.code)
    )
  }

  const showSecurityWithAdminOwner = () => {
    return (
      user?.ownTenant && isFeatureEnabled(PLAN_FEATURE_KEYS.security_setting)
    )
  }

  return (
    <div
      className={cn(
        'fixed hidden min-h-0 flex-col border-r border-gray-100 bg-white dark:border-gray-700 dark:bg-gray-900 tablet:flex tablet:w-[240px] tablet:min-w-[240px]',
        topSpace
      )}>
      <div className="flex min-w-[240px] flex-1 flex-col overflow-y-auto py-5">
        <Link
          href={configuration.path.default}
          className="flex flex-shrink-0 items-center px-4">
          <Tooltip
            content={`${t('common:sidebar:settings:closeSetting')}`}
            position="bottom"
            align="start">
            <IconButton size="xs" type="secondary" iconMenus="ChevronLeft" />
          </Tooltip>
          <div className="ml-2">
            <p className="text-base font-medium text-gray-600 dark:text-gray-300">
              <Trans i18nKey={'common:sidebar:settings:title'} />
            </p>
          </div>
        </Link>

        <div className="mt-4 px-4">
          <div className="mb-2">
            <p className="text-xs font-medium text-gray-600 dark:text-gray-300">
              <Trans i18nKey={'common:sidebar:settings:list:accountTitle'} />
            </p>
          </div>

          <nav
            className="flex-1 space-y-1 bg-white dark:bg-gray-900"
            aria-label="Sidebar">
            <SidebarItemSettings
              selected={router.pathname === configuration.path.settings.account}
              link={configuration.path.settings.account}
              icon={<IconWrapper className="" name="User" size={18} />}
              title={`${t('common:sidebar:settings:list:accountList:account')}`}
              premium={isFeaturePremiumShow('profile')}
            />
            <SidebarItemSettings
              selected={
                router.pathname === configuration.path.settings.emailSetting
              }
              link={configuration.path.settings.emailSetting}
              icon={<IconWrapper className="" name="Mail" size={18} />}
              title={`${t(
                'common:sidebar:settings:list:accountList:emailSetting'
              )}`}
              // premium={isFeaturePremiumShow('profile')}
            />

            {/* {isCompanyKind === false ? (
              <If
                condition={
                  isFeatureEnabled(PLAN_FEATURE_KEYS.employee_profile) &&
                  showIconFeatureFromAdminSetting(
                    PLAN_FEATURE_KEYS.employee_profile
                  )
                }>
                <SidebarItemSettings
                  selected={
                    router.pathname === configuration.path.settings.profiles
                  }
                  link={configuration.path.settings.profiles}
                  icon={
                    <IconWrapper className="" name="ClipboardList" size={18} />
                  }
                  title={`${t(
                    'common:sidebar:settings:list:accountList:profiles'
                  )}`}
                  premium={
                    isFeatureEnabled(PLAN_FEATURE_KEYS.employee_profile) &&
                    !isUnLockFeature(PLAN_FEATURE_KEYS.employee_profile) &&
                    user?.ownTenant
                  }
                />
              </If>
            ) : null} */}
          </nav>
        </div>

        <div className={cn(checkPermissionWorkspaceTitle() ? 'mt-4 px-4' : '')}>
          {checkPermissionWorkspaceTitle() ? (
            <div className="mb-2">
              <p className="text-xs font-medium text-gray-600 dark:text-gray-300">
                <Trans
                  i18nKey={'common:sidebar:settings:list:workSpaceTitle'}
                />
              </p>
            </div>
          ) : null}

          <nav
            className="flex-1 space-y-1 bg-white dark:bg-gray-900"
            aria-label="Sidebar">
            <If condition={canAccessModule}>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.workspace
                }
                link={configuration.path.settings.workspace}
                icon={<IconWrapper className="" name="Building2" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:general'
                )}`}
                premium={isFeaturePremiumShow('profile')}
              />
            </If>

            <If condition={canAccessCareer}>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.careers
                }
                link={configuration.path.settings.careers}
                icon={<IconWrapper className="" name="Globe" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:careers'
                )}`}
                premium={isFeaturePremiumShow('careers')}
              />
            </If>
            <If condition={canAccessCareer}>
              <SidebarItemSettings
                selected={
                  router.pathname ===
                  configuration.path.settings.termsAndConditions
                }
                link={configuration.path.settings.termsAndConditions}
                icon={<IconWrapper className="" name="ShieldCheck" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:termsAndConditions'
                )}`}
                premium={isFeaturePremiumShow('careers')}
              />
            </If>

            {isCompanyKind === false ? (
              <If condition={canAccessDepartment}>
                <SidebarItemSettings
                  selected={
                    router.pathname === configuration.path.settings.departments
                  }
                  link={configuration.path.settings.departments}
                  icon={<IconWrapper className="" name="Network" size={18} />}
                  title={`${t(
                    'common:sidebar:settings:list:companyList:teamsDepartments'
                  )}`}
                  premium={isFeaturePremiumShow('departments')}
                />
              </If>
            ) : null}

            <If
              condition={
                canAccessPosition &&
                isFeatureEnabled(PLAN_FEATURE_KEYS.career_navigator) &&
                isUnLockFeature(PLAN_FEATURE_KEYS.career_navigator)
              }>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.positions
                }
                link={configuration.path.settings.positions}
                icon={<IconWrapper className="" name="Briefcase" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:positions'
                )}`}
                premium={isFeaturePremiumShow(
                  PLAN_FEATURE_KEYS.career_navigator
                )}
              />
            </If>

            <If condition={canAccessUser}>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.members
                }
                link={configuration.path.settings.members}
                icon={<IconWrapper className="" name="Users" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:teamMembers'
                )}`}
                premium={isFeaturePremiumShow('team_member')}
              />
            </If>

            {isCompanyKind ? (
              <If condition={canAccessTeam}>
                <SidebarItemSettings
                  selected={
                    router.pathname === configuration.path.settings.teams
                  }
                  link={configuration.path.settings.teams}
                  icon={<IconWrapper className="" name="UserCog" size={18} />}
                  title={`${t(
                    'common:sidebar:settings:list:companyList:teamsDepartments'
                  )}`}
                  premium={isFeaturePremiumShow('teams')}
                />
              </If>
            ) : null}

            <If
              condition={
                showSecurityWithAdminOwner() || showSecurityWithAdmin()
              }>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.security
                }
                link={configuration.path.settings.security}
                icon={<IconWrapper className="" name="Key" size={18} />}
                title={`${t('common:sidebar:settings:list:security')}`}
                premium={
                  isFeatureEnabled(PLAN_FEATURE_KEYS.security_setting) &&
                  !isUnLockFeature(PLAN_FEATURE_KEYS.security_setting) &&
                  user?.ownTenant
                }
              />
            </If>

            {isCompanyKind === false ? (
              <If condition={canAccessLocation}>
                <SidebarItemSettings
                  selected={
                    router.pathname === configuration.path.settings.locations
                  }
                  link={configuration.path.settings.locations}
                  icon={<IconWrapper className="" name="MapPin" size={18} />}
                  title={`${t(
                    'common:sidebar:settings:list:companyList:locations'
                  )}`}
                  premium={isFeaturePremiumShow('locations')}
                />
              </If>
            ) : null}

            <If condition={user?.ownTenant}>
              <SidebarItemSettings
                selected={router.pathname === configuration.path.settings.plans}
                link={configuration.path.settings.plans}
                icon={
                  <IconWrapper className="" name="FileSpreadsheet" size={18} />
                }
                title={`${t('common:sidebar:settings:list:companyList:plans')}`}
              />
            </If>
            <If
              condition={
                (adminCanAction(currentRole?.code) &&
                  isFeatureEnabled(PLAN_FEATURE_KEYS.import_job) &&
                  showIconFeatureFromAdminSetting(
                    PLAN_FEATURE_KEYS.import_job,
                    user?.ownTenant || false
                  )) ||
                (adminCanAction(currentRole?.code) &&
                  isFeatureEnabled(PLAN_FEATURE_KEYS.import_candidate) &&
                  showIconFeatureFromAdminSetting(
                    PLAN_FEATURE_KEYS.import_candidate,
                    user?.ownTenant || false
                  ))
              }>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.import
                }
                link={configuration.path.settings.import}
                icon={<IconWrapper className="" name="Upload" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:import'
                )}`}
                premium={
                  (isFeatureEnabled(PLAN_FEATURE_KEYS.import_job) &&
                    !isUnLockFeature(PLAN_FEATURE_KEYS.import_job) &&
                    user?.ownTenant) ||
                  (isFeatureEnabled(PLAN_FEATURE_KEYS.import_candidate) &&
                    !isUnLockFeature(PLAN_FEATURE_KEYS.import_candidate) &&
                    user?.ownTenant)
                }
              />
            </If>
            <If
              condition={
                isFeatureEnabled(PLAN_FEATURE_KEYS.audit_logs) &&
                adminCanAction(currentRole?.code) &&
                showIconFeatureFromAdminSetting(
                  PLAN_FEATURE_KEYS.audit_logs,
                  user?.ownTenant || false
                )
              }>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.audit_logs
                }
                link={configuration.path.settings.audit_logs}
                icon={<IconWrapper className="" name="Zap" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:audit_logs'
                )}`}
                premium={
                  isFeatureEnabled(PLAN_FEATURE_KEYS.audit_logs) &&
                  !isUnLockFeature(PLAN_FEATURE_KEYS.audit_logs) &&
                  user?.ownTenant
                }
              />
            </If>
          </nav>
        </div>

        <div
          className={cn(checkPermissionRecruitingTitle() ? 'mt-4 px-4' : '')}>
          {checkPermissionRecruitingTitle() ? (
            <div className="mb-2">
              <p className="text-xs font-medium text-gray-600 dark:text-gray-300">
                <Trans
                  i18nKey={'common:sidebar:settings:list:recruitingTitle'}
                />
              </p>
            </div>
          ) : null}

          <nav
            className="flex-1 space-y-1 bg-white dark:bg-gray-900"
            aria-label="Sidebar">
            {isCompanyKind === false ? (
              <>
                <If
                  condition={
                    canAccessModule &&
                    isFeatureEnabled(PLAN_FEATURE_KEYS.requisition) &&
                    showIconFeatureFromAdminSetting(
                      PLAN_FEATURE_KEYS.requisition,
                      user?.ownTenant || false
                    )
                  }>
                  <SidebarItemSettings
                    selected={
                      router.pathname ===
                      configuration.path.settings.requisitions
                    }
                    link={configuration.path.settings.requisitions}
                    icon={
                      <IconWrapper
                        className=""
                        name="ClipboardCheck"
                        size={18}
                      />
                    }
                    title={`${t(
                      'common:sidebar:settings:list:companyList:requisitions'
                    )}`}
                    premium={
                      isFeatureEnabled(PLAN_FEATURE_KEYS.requisition) &&
                      !isUnLockFeature(PLAN_FEATURE_KEYS.requisition) &&
                      user?.ownTenant
                    }
                  />
                </If>
              </>
            ) : null}
            <If
              condition={
                canManageReferral &&
                isFeatureEnabled(PLAN_FEATURE_KEYS.referral) &&
                showIconFeatureFromAdminSetting(
                  PLAN_FEATURE_KEYS.referral,
                  user?.ownTenant || false
                )
              }>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.careerhub
                }
                link={configuration.path.settings.careerhub}
                icon={<IconWrapper className="" name="FileBox" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:companyList:careerhub'
                )}`}
                premium={
                  isFeatureEnabled(PLAN_FEATURE_KEYS.referral) &&
                  !isUnLockFeature(PLAN_FEATURE_KEYS.referral) &&
                  user?.ownTenant
                }
              />
            </If>
            {isCompanyKind ? (
              <If condition={canAccessCompanyStatus}>
                <SidebarItemSettings
                  selected={
                    router.pathname ===
                    configuration.path.agency.settings.companyStatus
                  }
                  link={configuration.path.agency.settings.companyStatus}
                  icon={<IconWrapper className="" name="Activity" size={18} />}
                  title={`${t('common:sidebar:settings:list:companyStatus')}`}
                  premium={isFeaturePremiumShow('companyStatus')}
                />
              </If>
            ) : (
              <If
                condition={
                  isShowCompanyFeature && adminCanAction(currentRole?.code)
                }>
                <SidebarItemSettings
                  selected={
                    router.pathname ===
                    configuration.path.settings.company_settings
                  }
                  link={configuration.path.settings.company_settings}
                  icon={<IconWrapper className="" name="Activity" size={18} />}
                  title={`${t('common:sidebar:settings:list:companySettings')}`}
                />
              </If>
            )}

            <If condition={canAccessCustomField}>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.customFields
                }
                link={configuration.path.settings.customFields}
                icon={<IconWrapper className="" name="FileType" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:templatesList:customFields'
                )}`}
                premium={isFeaturePremiumShow('customFields')}
              />
            </If>

            <If condition={canAccessTag}>
              <SidebarItemSettings
                selected={router.pathname === configuration.path.settings.tags}
                link={configuration.path.settings.tags}
                icon={<IconWrapper className="" name="Tag" size={18} />}
                title={`${t('common:sidebar:settings:list:workFlowList:tags')}`}
                premium={isFeaturePremiumShow('tags')}
              />
            </If>

            {/* <If
              condition={
                canAccessModule &&
                isFeatureEnabled(PLAN_FEATURE_KEYS.skill_management) &&
                showIconFeatureFromAdminSetting(
                  PLAN_FEATURE_KEYS.skill_management
                )
              }> */}
            <If
              condition={
                (user?.ownTenant &&
                  isFeatureEnabled(PLAN_FEATURE_KEYS.skill_management)) ||
                (adminCanAction(currentRole?.code) &&
                  isFeatureEnabled(PLAN_FEATURE_KEYS.skill_management) &&
                  isUnLockFeature(PLAN_FEATURE_KEYS.skill_management))
              }>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.skills
                }
                link={configuration.path.settings.skills}
                icon={
                  <IconWrapper
                    className=""
                    name="StretchHorizontal"
                    size={18}
                  />
                }
                title={`${t('common:sidebar:settings:list:skills')}`}
                premium={
                  isFeatureEnabled(PLAN_FEATURE_KEYS.skill_management) &&
                  !isUnLockFeature(PLAN_FEATURE_KEYS.skill_management) &&
                  user?.ownTenant
                }
              />
            </If>

            <If condition={canAccessRejectedReason}>
              <SidebarItemSettings
                selected={
                  router.pathname ===
                  configuration.path.settings.disqualifyReasons
                }
                link={configuration.path.settings.disqualifyReasons}
                icon={<IconWrapper className="" name="Slash" size={18} />}
                title={`${t(
                  'common:sidebar:settings:list:workFlowList:disqualificationReasons'
                )}`}
                premium={isFeaturePremiumShow('disqualificationReasons')}
              />
            </If>
          </nav>
        </div>

        <div className={cn(checkPermissionTemplatesTitle() ? 'mt-4 px-4' : '')}>
          {checkPermissionTemplatesTitle() ? (
            <div className="mb-2">
              <p className="text-xs font-medium text-gray-600 dark:text-gray-300">
                <Trans
                  i18nKey={'common:sidebar:settings:list:templatesTitle'}
                />
              </p>
            </div>
          ) : null}

          <nav
            className="flex-1 space-y-1 bg-white dark:bg-gray-900"
            aria-label="Sidebar">
            <If condition={canAccessEmailTemplate}>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.emailTemplates
                }
                link={configuration.path.settings.emailTemplates}
                icon={<IconWrapper className="" name="FileCheck" size={18} />}
                title={`${t('settings:emailTemplates:title')}`}
                premium={isFeaturePremiumShow('emailTemplates')}
              />
            </If>

            <If condition={canAccessPipelineTemplate}>
              <SidebarItemSettings
                selected={
                  router.pathname ===
                  configuration.path.settings.hiringPipelines
                }
                link={configuration.path.settings.hiringPipelines}
                icon={
                  <IconWrapper className="" name="StretchVertical" size={18} />
                }
                title={`${t(
                  'common:sidebar:settings:list:templatesList:hiringPipelines'
                )}`}
                premium={isFeaturePremiumShow('hiringPipelines')}
              />
            </If>

            <If condition={canAccessIkitTemplate}>
              <SidebarItemSettings
                selected={
                  router.pathname === configuration.path.settings.interviewKits
                }
                link={configuration.path.settings.interviewKits}
                icon={<IconWrapper className="" name="LayoutList" size={18} />}
                title={`${t('common:sidebar:settings:list:interviewKits')}`}
                premium={isFeaturePremiumShow('interviewKits')}
              />
            </If>

            <If
              condition={
                canAccessProfileTemplate &&
                isFeatureEnabled(PLAN_FEATURE_KEYS.cv_template) &&
                showIconFeatureFromAdminSetting(
                  PLAN_FEATURE_KEYS.cv_template,
                  user?.ownTenant || false
                )
              }>
              <SidebarItemSettings
                selected={
                  router.pathname ===
                  configuration.path.settings.profileTemplates
                }
                link={configuration.path.settings.profileTemplates}
                icon={<IconWrapper className="" name="FileText" size={18} />}
                title={`${t('common:sidebar:settings:list:profileTemplates')}`}
                premium={
                  isFeatureEnabled(PLAN_FEATURE_KEYS.cv_template) &&
                  !isUnLockFeature(PLAN_FEATURE_KEYS.cv_template) &&
                  user?.ownTenant
                }
              />
            </If>
          </nav>
        </div>

        {/* <If condition={currentRole?.code}>
          <SidebarSearch />
        </If> */}
      </div>
    </div>
  )
}

export default SidebarSettings
