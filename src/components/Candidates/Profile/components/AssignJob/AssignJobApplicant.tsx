import { InfiniteData } from '@tanstack/react-query'
import { FC, useCallback, useEffect, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import withQueryClientProvider from 'src/hoc/with-query-client-provider'
import InfiniteScroll from '~/components/List/InfiniteScroll'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import configuration from '~/configuration'
import { IRouterWithID } from '~/core/@types/global'
import { AGENCY_TENANT } from '~/core/constants/enum'
import useMutationGraphQL from '~/core/middleware/use-mutation-graphQL'
import { Button } from '~/core/ui/Button'
import { DebouncedInput } from '~/core/ui/DebouncedInput'
import { Dialog } from '~/core/ui/Dialog'
import Empty from '~/core/ui/Empty'
import IconWrapper from '~/core/ui/IconWrapper'
import { ScrollArea } from '~/core/ui/ScrollArea'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import { IApplicableJobs } from '~/lib/features/calendar/types'
import MutationProfilesAssignJob from '~/lib/features/candidates/graphql/mutation-assign-profile-to-job'
import QueryTenantJobsApplicableList from '~/lib/features/jobs/graphql/query-job-assign'
import { useInfinityGraphPage } from '~/lib/features/jobs/hooks/use-infinity-graph-page'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'

interface JobsApplicableListPage {
  jobsApplicableList: {
    collection: IApplicableJobs[]
    metadata: {
      totalCount: number
    }
  }
}

export const AssignJobList: FC<{
  setOpen: (param: boolean) => void
  profileId?: IRouterWithID
  filter: {
    page: number
    limit: number
    search: string
  }
  setFilter: (param: { page: number; limit: number; search: string }) => void
  setApplicantId?: (applicantId: string) => void
  clickedCallback?: (jobId?: string) => void
  onSubmitted?: (submitted: boolean) => void
}> = ({
  setOpen,
  profileId,
  filter,
  setFilter,
  setApplicantId,
  clickedCallback,
  onSubmitted
}) => {
  const { t } = useTranslation()
  const { setToast } = useToastStore()
  const { user } = useBoundStore()
  const scrollingWrapper = useRef<HTMLDivElement>(null)
  const [jobsApplicableList, setJobsApplicableList] = useState<
    InfiniteData<JobsApplicableListPage> | undefined
  >(undefined)

  const { isUnLockFeature, isFeatureEnabled } = useSubscriptionPlan()
  const isShowCompanyFeature =
    isFeatureEnabled(PLAN_FEATURE_KEYS.company) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.company)
  const { isCompanyKind } = useDetectCompanyWithKind({ kind: AGENCY_TENANT })

  const {
    data: jobsApplicable,
    fetchNextPage,
    hasNextPage
  } = useInfinityGraphPage({
    queryDocumentNote: QueryTenantJobsApplicableList,
    getVariable: (page) => ({ ...filter, profileId: Number(profileId), page }),
    getPageAttribute: (_lastGroup, groups) => ({
      totalCount: _lastGroup?.jobsApplicableList?.metadata?.totalCount,
      pageLength: groups?.[0]?.jobsApplicableList?.collection?.length
    }),
    queryKey: ['candidate-jobsApplicableList', filter?.search]
  })

  useEffect(() => {
    setJobsApplicableList(jobsApplicable)
  }, [jobsApplicable])

  const { trigger: triggerApplicantsChangeStage, isLoading } =
    useMutationGraphQL({ query: MutationProfilesAssignJob })

  const assignJobCallback = useCallback(
    async (jobId: string) => {
      if (isLoading) {
        return
      }

      triggerApplicantsChangeStage({
        id: Number(profileId),
        jobId: Number(jobId)
      }).then((result) => {
        if (result.error) {
          return catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.candidates.list,
            setToast
          })
        }

        const { profilesAssignJob } = result.data

        const applicantId = profilesAssignJob?.applicant?.id

        if (applicantId) {
          setJobsApplicableList((prev) => {
            if (!prev) return prev
            return {
              ...prev,
              pages: prev.pages.map((page: JobsApplicableListPage) => ({
                ...page,
                jobsApplicableList: {
                  ...page.jobsApplicableList,
                  collection: page.jobsApplicableList.collection.filter(
                    (item: IApplicableJobs) => String(item.id) !== String(jobId)
                  )
                }
              }))
            }
          })
          setToast({
            open: true,
            type: 'success',
            title: `${t('assignJobs:candidateAssignToJob')}`
          })
          setApplicantId && setApplicantId(applicantId)
          onSubmitted && onSubmitted(true)
        }

        return
      })
    },
    [
      isLoading,
      triggerApplicantsChangeStage,
      profileId,
      setToast,
      setApplicantId,
      setOpen,
      t
    ]
  )

  return (
    <>
      <DebouncedInput
        autoFocus
        placeholder={`${t('assignJobs:searchByJobTitle')}`}
        forceOnChangeCallBack={filter}
        value={filter.search}
        onChange={(value) =>
          setFilter({
            ...filter,
            search: String(value)
          })
        }
        onClear={() =>
          setFilter({
            ...filter,
            search: ''
          })
        }
      />
      <ScrollArea
        scrollRef={scrollingWrapper}
        className="-mx-5 h-auto space-y-3 px-5"
        viewportClassName={
          jobsApplicableList?.pages?.[0]?.jobsApplicableList?.collection
            .length === 0
            ? '[&>div:first-child]:h-full h-full'
            : 'max-h-[552px]'
        }>
        {jobsApplicableList?.pages?.[0]?.jobsApplicableList?.collection
          .length === 0 ? (
          <div className="mt-3 flex h-full items-center justify-center">
            <Empty
              onClick={() => {
                if (!filter.search) {
                  window.open(configuration.path.jobs.create)
                }
              }}
              type={filter.search ? 'empty-search' : 'empty-data'}
              title={
                filter.search
                  ? `${t('assignJobs:emptySearch:title')}`
                  : `${t('assignJobs:emptyData:title')}`
              }
              description={
                filter.search
                  ? `${t('assignJobs:emptySearch:description')}`
                  : `${t('assignJobs:emptyData:description')}`
              }
              buttonTitle={filter.search ? '' : `${t('assignJobs:newJob')}`}
            />
          </div>
        ) : (
          <InfiniteScroll
            wrapperRef={scrollingWrapper}
            loadData={fetchNextPage}
            haveNext={!!hasNextPage}
            className={
              jobsApplicableList?.pages?.[0]?.jobsApplicableList?.metadata
                .totalCount === 0
                ? 'flex h-full items-center justify-center'
                : ''
            }>
            {jobsApplicableList?.pages.map((page: JobsApplicableListPage) =>
              page.jobsApplicableList.collection.map(
                (item: IApplicableJobs) => {
                  return (
                    <div
                      key={item.id}
                      className="border-b border-solid border-gray-100 py-3 pr-1 last:border-none last:pb-0">
                      <div className="flex w-full items-center justify-between space-x-6">
                        <div className="flex-1 space-y-1">
                          <div className="flex">
                            <Tooltip content={item.title}>
                              <TypographyText className="line-clamp-1 cursor-pointer text-sm font-medium text-gray-900 hover:underline">
                                <a
                                  href={`${configuration.path.jobs.detail(
                                    Number(item.id)
                                  )}?tabs=details`}
                                  target="_blank">
                                  {item.title}
                                </a>
                              </TypographyText>
                            </Tooltip>
                          </div>
                          <div className="flex items-center">
                            <div className="line-clamp-1 flex max-w-[50%] flex-none">
                              {isShowCompanyFeature || isCompanyKind ? (
                                <Tooltip
                                  content={
                                    item.company?.permittedFields?.name?.value
                                      ? item.company?.permittedFields?.name
                                          ?.value
                                      : user?.currentTenant?.name
                                  }>
                                  <div className="flex items-center">
                                    <div>
                                      <IconWrapper
                                        name="Building"
                                        size={12}
                                        className="mr-1 text-gray-600"
                                      />
                                    </div>
                                    <TypographyText className="line-clamp-1 break-all text-sm text-gray-500">
                                      {item.company?.permittedFields?.name
                                        ?.value
                                        ? item.company?.permittedFields?.name
                                            ?.value
                                        : user?.currentTenant?.name}
                                    </TypographyText>
                                  </div>
                                </Tooltip>
                              ) : null}
                            </div>
                            <div className="flex items-center">
                              {isShowCompanyFeature &&
                                item.department?.name &&
                                item.jobLocations.length > 0 && (
                                  <span className="mx-2 h-0.5 w-0.5 flex-none rounded bg-gray-400" />
                                )}
                              <div className="line-clamp-1 break-all">
                                {item.jobLocations.length > 0 &&
                                  (item.jobLocations.length === 1 ? (
                                    <Tooltip
                                      content={[
                                        item.jobLocations[0].state,
                                        item.jobLocations[0].country
                                      ]
                                        .filter((item) => !!item)
                                        .join(', ')}>
                                      <div className="flex items-center">
                                        <div>
                                          <IconWrapper
                                            name="MapPin"
                                            size={12}
                                            className="mr-1 text-gray-600"
                                          />
                                        </div>
                                        <TypographyText className="line-clamp-1 break-all text-sm text-gray-500">
                                          {[
                                            item.jobLocations[0].state,
                                            item.jobLocations[0].country
                                          ]
                                            .filter((item) => !!item)
                                            .join(', ')}
                                        </TypographyText>
                                      </div>
                                    </Tooltip>
                                  ) : item.jobLocations.length > 1 ? (
                                    <div className="flex items-center">
                                      <div>
                                        <IconWrapper
                                          name="MapPin"
                                          size={12}
                                          className="mr-1 text-gray-600"
                                        />
                                      </div>
                                      <TypographyText className="line-clamp-1 break-all text-sm text-gray-500">
                                        {t('assignJobs:countLocations', {
                                          count: item.jobLocations.length
                                        })}
                                      </TypographyText>
                                    </div>
                                  ) : null)}
                              </div>
                            </div>
                            <div className="flex items-center">
                              {item.department?.name ? (
                                <Tooltip content={item.department?.name}>
                                  <div className="flex items-center">
                                    <span className="mx-2 h-0.5 w-0.5 flex-none rounded bg-gray-400" />
                                    <div>
                                      <IconWrapper
                                        name="Network"
                                        size={12}
                                        className="mr-1 text-gray-600"
                                      />
                                    </div>
                                    <TypographyText className="line-clamp-1 break-all text-sm text-gray-500">
                                      {item.department?.name}
                                    </TypographyText>
                                  </div>
                                </Tooltip>
                              ) : null}
                            </div>
                          </div>
                        </div>
                        <div className="flex-none">
                          <Button
                            size="xs"
                            type="tertiary"
                            label={`${t('assignJobs:assign')}`}
                            iconMenus="Plus"
                            onClick={() => {
                              if (clickedCallback) {
                                clickedCallback(item.id)
                              } else {
                                assignJobCallback(item.id)
                              }
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  )
                }
              )
            )}
          </InfiniteScroll>
        )}
      </ScrollArea>
    </>
  )
}

const AssignJobApplicant: FC<{
  profileId?: IRouterWithID
  reload?: (applicantId: string) => void
  renderAssignLink?: boolean
  applicantId?: IRouterWithID
}> = ({ profileId, reload, renderAssignLink = false, applicantId }) => {
  const { t } = useTranslation()
  const [open, setOpen] = useState(false)
  const [hasSubmitted, setSubmitted] = useState(false)
  const [applicantProfileId, setApplicantId] = useState<string | undefined>(
    undefined
  )
  const { isUnLockFeature, isFeatureEnabled } = useSubscriptionPlan()
  const isShowCompanyFeature =
    isFeatureEnabled(PLAN_FEATURE_KEYS.company) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.company)
  const [filter, setFilter] = useState({
    page: 1,
    limit: configuration.defaultPageSize,
    search: ''
  })

  const handleDialogOpenChange = useCallback(
    (open: boolean) => {
      if (!open && hasSubmitted) {
        reload && reload(String(applicantProfileId))
      }
      setOpen(open)
    },
    [open, hasSubmitted]
  )

  return (
    <>
      <div>
        {renderAssignLink ? (
          <Tooltip content={t('assignJobs:assign')}>
            <TextButton
              onClick={() => setOpen(true)}
              size="md"
              iconMenus="Plus"
              type="primary"
              underline={false}
            />
          </Tooltip>
        ) : (
          <Tooltip position="bottom" content={t('assignJobs:assign')}>
            <Button
              onClick={() => setOpen(true)}
              size="xs"
              iconMenus="Briefcase"
              type="tertiary"
              label={applicantId ? '' : `${t('assignJobs:assign')}`}
            />
          </Tooltip>
        )}
      </div>

      <Dialog
        open={open}
        size={isShowCompanyFeature ? 'md' : 'sm'}
        onOpenChange={handleDialogOpenChange}
        isPreventAutoFocusDialog={true}
        label={`${t('button:assignJob')}`}>
        <AssignJobList
          profileId={profileId}
          filter={filter}
          setFilter={setFilter}
          setOpen={setOpen}
          setApplicantId={setApplicantId}
          onSubmitted={setSubmitted}
        />
      </Dialog>
    </>
  )
}

export default withQueryClientProvider(AssignJobApplicant)
