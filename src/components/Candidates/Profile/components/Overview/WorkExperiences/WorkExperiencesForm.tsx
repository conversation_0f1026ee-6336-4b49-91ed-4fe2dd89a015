import { useState } from 'react'
import { Controller, useWatch } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import { AsyncSingleSearchWithSelect } from '~/core/ui/AsyncSingleSearchWithSelect'
import { But<PERSON> } from '~/core/ui/Button'
import { Checkbox } from '~/core/ui/Checkbox'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { Input } from '~/core/ui/Input'
import { NativeSelect } from '~/core/ui/NativeSelect'
import { RichEditor } from '~/core/ui/RichEditor'
import { ISelectOption } from '~/core/ui/Select'
import { getMonths, getYears } from '~/core/utilities/common'
import { monthFormatDate, yearFormatDate } from '~/core/utilities/format-date'
import useCandidateProfile from '~/lib/features/candidates/hooks/use-query-candidate'
import { schemaWorkExpProfile } from '~/lib/features/candidates/schema/validation-work-exp'
import { WorkExperienceParamType } from '~/lib/features/candidates/types'

const WorkExperiencesForm = ({
  setOpen,
  onSubmit,
  defaultValue,
  setDefaultValue,
  isLoadingUpdateProfile = false
}: {
  setOpen: (open: boolean) => void
  onSubmit?: (
    data: WorkExperienceParamType,
    formAction: IFormAction
  ) => Promise<void>
  isLoadingUpdateProfile?: boolean
  defaultValue?: WorkExperienceParamType
  setDefaultValue?: (params: WorkExperienceParamType) => void
}) => {
  const { t } = useTranslation()
  const isEdit = defaultValue?.title
  const { promiseCountryStateOptions } = useCandidateProfile({})
  const [locationMenuOpen, setLocationMenuOpen] = useState<boolean>(false)

  return (
    <DynamicImportForm
      isShowDebug={false}
      id="work-exp-form"
      className="w-full"
      defaultValue={defaultValue}
      schema={schemaWorkExpProfile(t)}
      onSubmit={onSubmit}>
      {({ formState, control }) => {
        return (
          <>
            <div className="mb-4">
              <Controller
                control={control}
                name="title"
                defaultValue={defaultValue?.title || ''}
                render={({ field: { onChange, value } }) => (
                  <FormControlItem
                    labelRequired
                    label={`${t(
                      'candidates:tabs:candidateOverview:workExperiences:jobTitleLabel'
                    )}`}
                    destructive={formState.errors && !!formState.errors?.title}
                    destructiveText={
                      formState.errors &&
                      (formState.errors?.title?.message as string)
                    }>
                    <Input
                      autoFocus
                      placeholder={`${t(
                        'candidates:tabs:candidateOverview:workExperiences:jobTitlePlaceholder'
                      )}`}
                      size="sm"
                      onChange={onChange}
                      value={value}
                      destructive={
                        formState.errors && !!formState.errors?.title
                      }
                    />
                  </FormControlItem>
                )}
              />
            </div>

            <div className="mb-4 flex justify-between">
              <div className="mr-4 w-full">
                <Controller
                  control={control}
                  name="company"
                  defaultValue={defaultValue?.company || ''}
                  render={({ field: { onChange, value } }) => (
                    <FormControlItem
                      labelRequired
                      label={`${t(
                        'candidates:tabs:candidateOverview:workExperiences:companyNameLabel'
                      )}`}
                      destructive={
                        formState.errors && !!formState.errors?.company
                      }
                      destructiveText={
                        formState.errors &&
                        (formState.errors?.company?.message as string)
                      }>
                      <Input
                        placeholder={`${t(
                          'candidates:tabs:candidateOverview:workExperiences:companyNamePlaceholder'
                        )}`}
                        size="sm"
                        onChange={onChange}
                        value={value}
                        destructive={
                          formState.errors && !!formState.errors?.company
                        }
                      />
                    </FormControlItem>
                  )}
                />
              </div>
              <div className="w-full">
                <Controller
                  control={control}
                  name="location"
                  defaultValue={defaultValue?.location}
                  render={({ field: { onChange, value } }) => {
                    return (
                      <FormControlItem
                        label={`${t(
                          'candidates:tabs:candidateOverview:workExperiences:locationLabel'
                        )}`}>
                        <AsyncSingleSearchWithSelect
                          loadAsyncWhenOpen={false}
                          promiseOptions={promiseCountryStateOptions}
                          size="sm"
                          onChange={(newValue) => {
                            onChange(newValue)
                            setLocationMenuOpen(false)
                          }}
                          configSelectOption={{
                            supportingText: ['location']
                          }}
                          placeholder={`${t(
                            'candidates:tabs:candidateOverview:workExperiences:locationPlaceholder'
                          )}`}
                          value={value}
                          menuIsOpen={locationMenuOpen}
                          onInputChange={(search) => {
                            if (search.length > 0) setLocationMenuOpen(true)
                            else setLocationMenuOpen(false)
                          }}
                          classNameOverride={{
                            loadingMessage: `${t('label:loading')}`,
                            noOptionsMessage: `${t('label:noOptions')}`
                          }}
                        />
                      </FormControlItem>
                    )
                  }}
                />
              </div>
            </div>

            <div className="mb-4 flex justify-between">
              <div className="mr-4 w-full">
                <FormControlItem
                  labelRequired
                  label={`${t(
                    'candidates:tabs:candidateOverview:workExperiences:from'
                  )}`}
                  destructive={formState.errors && !!formState.errors?.fromYear}
                  destructiveText={
                    formState.errors &&
                    (formState.errors?.fromYear?.message as string)
                  }>
                  <div className="flex w-full items-center space-x-1">
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="fromMonth"
                        defaultValue={defaultValue?.fromMonth || ''}
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem
                              destructive={
                                formState.errors &&
                                !!formState.errors?.fromMonth
                              }
                              destructiveText={
                                formState.errors &&
                                (formState.errors?.fromMonth?.message as string)
                              }>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:month')}`}
                                value={
                                  value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                destructive={
                                  formState.errors &&
                                  !!formState.errors?.fromMonth
                                }
                                onChange={(newValue) => {
                                  onChange(
                                    (newValue as ISelectOption)?.value || null
                                  )
                                }}
                                options={getMonths()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="fromYear"
                        defaultValue={defaultValue?.fromYear || ''}
                        render={({ field: { onChange, value } }) => {
                          return (
                            <FormControlItem>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:year')}`}
                                value={
                                  value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                onChange={(newValue) => {
                                  onChange(
                                    (newValue as ISelectOption)?.value || ''
                                  )
                                }}
                                destructive={
                                  formState.errors &&
                                  !!formState.errors?.fromYear
                                }
                                options={getYears()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                  </div>
                </FormControlItem>
              </div>
              <div className="w-full">
                <FormControlItem
                  labelRequired
                  label={`${t(
                    'candidates:tabs:candidateOverview:workExperiences:to'
                  )}`}
                  destructive={formState.errors && !!formState.errors?.toYear}
                  destructiveText={
                    formState.errors &&
                    (formState.errors?.toYear?.message as string)
                  }>
                  <div className="flex w-full items-center space-x-1">
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="toMonth"
                        defaultValue={defaultValue?.toMonth || ''}
                        render={({ field: { onChange, value } }) => {
                          // eslint-disable-next-line react-hooks/rules-of-hooks
                          const currentWorking = useWatch({
                            control,
                            name: 'currentWorking'
                          })
                          return (
                            <FormControlItem>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:month')}`}
                                isDisabled={currentWorking}
                                value={
                                  currentWorking
                                    ? [
                                        {
                                          value: monthFormatDate(
                                            new Date()
                                          ).toString(),
                                          supportingObj: {
                                            name: monthFormatDate(
                                              new Date()
                                            ).toString()
                                          }
                                        }
                                      ]
                                    : value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                onChange={(newValue) => {
                                  onChange(
                                    (newValue as ISelectOption)?.value || null
                                  )
                                }}
                                destructive={
                                  formState.errors &&
                                  !!formState.errors?.toMonth
                                }
                                options={getMonths()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                    <div className="flex-1">
                      <Controller
                        control={control}
                        name="toYear"
                        defaultValue={defaultValue?.toYear || ''}
                        render={({ field: { onChange, value } }) => {
                          // eslint-disable-next-line react-hooks/rules-of-hooks
                          const currentWorking = useWatch({
                            control,
                            name: 'currentWorking'
                          })
                          return (
                            <FormControlItem>
                              <NativeSelect
                                classNameOverride={{
                                  menu: 'min-w-full',
                                  loadingMessage: `${t('label:loading')}`,
                                  noOptionsMessage: `${t('label:noOptions')}`
                                }}
                                size="sm"
                                placeholder={`${t('label:placeholder:year')}`}
                                isDisabled={currentWorking}
                                value={
                                  currentWorking
                                    ? [
                                        {
                                          value: yearFormatDate(
                                            new Date()
                                          ).toString(),
                                          supportingObj: {
                                            name: yearFormatDate(
                                              new Date()
                                            ).toString()
                                          }
                                        }
                                      ]
                                    : value
                                    ? [
                                        {
                                          value: value.toString(),
                                          supportingObj: {
                                            name: value
                                          }
                                        }
                                      ]
                                    : []
                                }
                                onChange={(newValue) => {
                                  onChange(
                                    (newValue as ISelectOption)?.value || ''
                                  )
                                }}
                                destructive={
                                  formState.errors && !!formState.errors?.toYear
                                }
                                options={getYears()}
                              />
                            </FormControlItem>
                          )
                        }}
                      />
                    </div>
                  </div>
                </FormControlItem>
              </div>
            </div>

            <div className="mb-4">
              <Controller
                control={control}
                name="currentWorking"
                defaultValue={defaultValue?.currentWorking || false}
                render={({ field: { onChange, value } }) => {
                  return (
                    <FormControlItem
                      destructive={
                        formState.errors && !!formState.errors.currentWorking
                      }
                      destructiveText={
                        formState.errors &&
                        (formState.errors.currentWorking?.message as string)
                      }>
                      <Checkbox
                        size="sm"
                        isChecked={value}
                        onCheckedChange={(value) => {
                          onChange(value.target.checked)
                        }}
                        text={`${t(
                          'candidates:tabs:candidateOverview:workExperiences:currentWorkingHere'
                        )}`}
                      />
                    </FormControlItem>
                  )
                }}
              />
            </div>

            <div className="mb-4">
              <Controller
                control={control}
                name="description"
                defaultValue={defaultValue?.description || ''}
                render={({ field: { onChange, value } }) => (
                  <FormControlItem
                    label={`${t(
                      'candidates:tabs:candidateOverview:workExperiences:description'
                    )}`}
                    destructive={
                      formState.errors && !!formState.errors?.description
                    }
                    destructiveText={
                      formState.errors &&
                      (formState.errors?.description?.message as string)
                    }>
                    <RichEditor
                      size="sm"
                      className="min-h-[166px] w-full max-w-full"
                      onChange={onChange}
                      content={value}
                      limit={10000}
                      showCount={false}
                      placeholder={`${t(
                        'candidates:tabs:candidateOverview:workExperiences:descriptionPlaceholder'
                      )}`}
                      destructive={
                        formState.errors && !!formState.errors?.description
                      }
                    />
                  </FormControlItem>
                )}
              />
            </div>

            <div className="mt-3 flex justify-end">
              <Button
                className="mr-2"
                size="sm"
                label={`${t('button:cancel')}`}
                type="secondary"
                onClick={() => setOpen(false)}
              />
              <Button
                label={`${isEdit ? t('button:update') : t('button:save')}`}
                type="primary"
                size="sm"
                isDisabled={isLoadingUpdateProfile}
                isLoading={isLoadingUpdateProfile}
                htmlType="submit"
              />
            </div>
          </>
        )
      }}
    </DynamicImportForm>
  )
}

export default WorkExperiencesForm
