import { Editor as CoreEditor } from '@tiptap/core'
import { differenceInDays } from 'date-fns'
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Controller, FieldPath } from 'react-hook-form'
import { Trans, useTranslation } from 'react-i18next'
import AttachmentView from '~/components/AttachmentView'
import { isFeedbackDue } from '~/components/Calendar/InterviewCalendarView'
import { ChangeJobStageWithModalActionProps } from '~/components/ChangeJobStageWithModal/ChangeJobStageWithModalView'
import HTMLDisplay from '~/components/HTMLDisplay'
import SkeletonContainer from '~/components/Skeleton'
import useSubscriptionPlan from '~/components/Subscription/useSubscriptionPlan'
import { ISwitchLayoutView } from '~/components/SwitchLayout/SwitchLayoutView'
import { IRouterWithID } from '~/core/@types/global'
import {
  AGENCY_TENANT,
  CONTACT_DETAILS,
  DEFAULT_PAGE_SIZE
} from '~/core/constants/enum'
import { SETTING_CUSTOM_FIELDS } from '~/core/constants/url'
import { openAlert } from '~/core/ui/AlertDialog'
import { AvatarGroup } from '~/core/ui/AvatarGroup'
import { DynamicImportForm } from '~/core/ui/DynamicImportForm'
import { UploadFileDragAndDrop } from '~/core/ui/FileDragAndDrop'
import { CheckVerifyFillIcon } from '~/core/ui/FillIcons'
import { IFormAction } from '~/core/ui/Form'
import { FormControlItem } from '~/core/ui/FormControlItem'
import { IconButton } from '~/core/ui/IconButton'
import IconWrapper from '~/core/ui/IconWrapper'
import If from '~/core/ui/If'
import { InlineEditingEditor } from '~/core/ui/InlineEditing'
import { Skeleton } from '~/core/ui/Skeleton'
import { TypographyText } from '~/core/ui/Text'
import { TextButton } from '~/core/ui/TextButton'
import { Tooltip } from '~/core/ui/Tooltip'
import { cn } from '~/core/ui/utils'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'
import {
  checkIfArrayStringsContainsObjectKeys,
  fetchAndDownloadFile,
  getTimeZone
} from '~/core/utilities/common'
import {
  defaultFormatDate,
  monthFormatDate,
  timeFormatDate,
  yearFormatDate
} from '~/core/utilities/format-date'
import { isBrowser } from '~/core/utilities/is-browser'
import {
  adminAndMemberCanAction,
  limitedMemberCanAction
} from '~/core/utilities/permission'
import DirectPlacementAlert from '~/features/placements/components/DirectPlacementAlert'
import PlacementAlert from '~/features/placements/components/PlacementAlert'
import useInterviewsCandidateManagement from '~/lib/features/calendar/hooks/use-interview-candidate-management'
import { InterviewDetailType } from '~/lib/features/calendar/types'
import {
  FEEDBACK_OPTIONS,
  INTERVIEW_STATE_VALUE
} from '~/lib/features/calendar/utilities/enum.cva'
import {
  changeTimezone,
  convertTimezone
} from '~/lib/features/calendar/utilities/helper-schedule-interview'
import MutationReparserProfile from '~/lib/features/candidates/graphql/mutation-reparser-profile'
import MutationSummaryAIWriter from '~/lib/features/candidates/graphql/mutation-sumary-AI-writer'
import { useQueryCandidateApplicants } from '~/lib/features/candidates/hooks/use-query-candidate-applicants'
import { schemaUpdateProfile } from '~/lib/features/candidates/schema/validation-update-profile'
import useCandidateStore from '~/lib/features/candidates/store'
import {
  CandidateProfileInputType,
  CertificatesType,
  EducationParamType,
  IAIWriterForm,
  ICandidateProfile,
  IPermittedFields,
  ReferencesType,
  WorkExperienceParamType
} from '~/lib/features/candidates/types'
import {
  checkPermissionForActions,
  permittedFieldsManagement
} from '~/lib/features/candidates/utilities'
import {
  OVERVIEW_TAB,
  PROFILE_CANDIDATE_TAB,
  REQUIRED_KEYS
} from '~/lib/features/candidates/utilities/enum'
import {
  JOB_APPLICANT_STATUS,
  JOB_STAGE_GROUP,
  JOB_STATUS_ENUM
} from '~/lib/features/jobs/utilities/enum'
import usePermissionJob from '~/lib/features/permissions/hooks/use-permission-job'
import usePermissionSetting from '~/lib/features/permissions/hooks/use-permission-setting'
import { PLAN_FEATURE_KEYS } from '~/lib/features/settings/plans/utilities/enum'
import { formatInitialValueCustomField } from '~/lib/features/settings/profile-fields/mapping/custom-field-mapping'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import {
  LIST_ACCEPT_FILES_PDF_DOC,
  MAXIMUM_10_MB
} from '~/lib/hooks/use-upload-s3-aws'
import { useUserCheckKindOf } from '~/lib/hooks/use-user-check-kind'
import useBoundStore from '~/lib/store'
import useLoadingBlockStore from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'
import DisableControlByPermission from './components/DisableControlByPermission'
import CertificatesModal from './components/Overview/Certificates/CertificatesModal'
import CertificatesView from './components/Overview/Certificates/CertificatesView'
import ContactDetailsView from './components/Overview/ContactDetailsView'
import EducationModal from './components/Overview/Education/EducationModal'
import EducationView from './components/Overview/Education/EducationView'
import EmployeeReferral from './components/Overview/EmployeeReferral'
import ProfileInformationView from './components/Overview/ProfileInformationView'
import ReferenceModal from './components/Overview/Reference/ReferenceModal'
import ReferenceView from './components/Overview/Reference/ReferenceView'
import WorkExperiencesModal from './components/Overview/WorkExperiences/WorkExperiencesModal'
import WorkExperiencesView from './components/Overview/WorkExperiences/WorkExperiencesView'
import Link from 'next/link'
import configuration from '~/configuration'

interface CandidateProfileViewInfoProps
  extends ChangeJobStageWithModalActionProps {
  id?: string | number | string[]
  profileCandidateTabState: string
  isFirstLoading?: boolean
  data?: ICandidateProfile
  applicantId?: IRouterWithID
  isLoadingUpdateProfile?: boolean
  onUpdateProfile: (
    data: CandidateProfileInputType & { paramType: string }
  ) => Promise<boolean | void>
  onDeleteProfileCVs: (id: number) => Promise<void>
  onSubmit: (
    data: CandidateProfileInputType,
    formAction?: IFormAction
  ) => Promise<void>
  submitPartialField: (
    fieldName: FieldPath<CandidateProfileInputType>,
    validate: () => Promise<boolean>,
    submit?: () => Promise<void>
  ) => Promise<boolean>
  setSwitchView?: (value: ISwitchLayoutView) => void
  tab?: string
  refetch?: boolean
  setRefetch?: (refetch: boolean) => void
  fetchProfile: () => Promise<void>
  isDrawer: boolean
  profileOverviewRef?: React.RefObject<HTMLDivElement>
}

const CandidateProfileViewInfo: FC<CandidateProfileViewInfoProps> = ({
  id,
  profileCandidateTabState,
  isFirstLoading = false,
  data = {},
  applicantId,
  isLoadingUpdateProfile = false,
  onUpdateProfile,
  onSubmit,
  submitPartialField,
  setSwitchView,
  onDeleteProfileCVs,
  tab,
  refetch,
  setRefetch,
  setOpenMarkAsHired,
  setApplicantCurrent,
  fetchProfile,
  isDrawer,
  profileOverviewRef
}) => {
  const { t } = useTranslation()
  const { isFeatureEnabled, isUnLockFeature } = useSubscriptionPlan()
  const [disableSourceReferral, setDisableSourceReferral] = useState(false)
  const [openWorkExp, setOpenWorkExp] = useState(false)
  const [defaultValueWorkExp, setDefaultValueWorkExp] =
    useState<WorkExperienceParamType>()
  const [openEducation, setOpenEducation] = useState(false)
  const [defaultValueEducation, setDefaultValueEducation] =
    useState<EducationParamType>()
  const [openReferences, setOpenReferences] = useState(false)
  const [defaultValueReferences, setDefaultValueReferences] =
    useState<ReferencesType>()
  const [openCertificates, setOpenCertificates] = useState(false)
  const [defaultValueCertificates, setDefaultValueCertificates] =
    useState<CertificatesType>()
  const { setToast } = useToastStore()
  const { user, currentRole } = useBoundStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlockStore()
  const { setIsRefetchActivitiesList } = useCandidateStore()
  const permittedFields = data?.permittedFields
  const profileCVS = (data.profileCvs || []).length
  const [openAIWriter, setOpenAIWriter] = useState(false)
  const toggleAIWriter = () => {
    setOpenAIWriter(!openAIWriter)
  }

  const isShowPlacementFeature =
    isFeatureEnabled(PLAN_FEATURE_KEYS.placement) &&
    isUnLockFeature(PLAN_FEATURE_KEYS.placement)

  const { userIsAsClient } = useUserCheckKindOf()

  const { actionJob } = usePermissionJob()
  const { actionCustomField } = usePermissionSetting()
  const richEditorRef = useRef<CoreEditor>()
  const [textGenerate, setTextGenerate] = useState('')
  const [languageGenerate, setLanguageGenerate] = useState('')
  const { trigger: triggerReparserProfile } = useSubmitCommon(
    MutationReparserProfile
  )
  const { trigger: triggerFetchApplicants, data: dataJobs } =
    useQueryCandidateApplicants({
      variables: {
        profileId: Number(id),
        page: 1,
        limit: DEFAULT_PAGE_SIZE,
        sorting: {
          hired_date: 'desc'
        }
      }
    })
  const { trigger: triggerSummaryAIWriter, isLoading: loadingGenerate } =
    useSubmitCommon(MutationSummaryAIWriter)

  useEffect(() => {
    triggerFetchApplicants()
  }, [id])

  useEffect(() => {
    if (refetch) {
      triggerFetchApplicants()
      setRefetch && setRefetch(false)
    }
  }, [refetch, setRefetch, triggerFetchApplicants])

  const { interviewsListControl } = useInterviewsCandidateManagement({
    applicantId,
    shouldCallRequest: tab === OVERVIEW_TAB
  })
  const dataInterviews =
    interviewsListControl?.data?.interviewsList?.collection || []

  const formatData = (dataFormat: ICandidateProfile) => {
    const permittedFieldsFormat = dataFormat?.permittedFields
    return {
      ...dataFormat,
      headline: permittedFieldsFormat?.headline?.value || dataFormat?.headline,
      skills: permittedFieldsFormat?.skills?.value || [],
      birthday: {
        year: permittedFieldsFormat?.birthday?.value?.birth_year,
        month: permittedFieldsFormat?.birthday?.value?.birth_month,
        date: permittedFieldsFormat?.birthday?.value?.birth_date
      },
      expectedSalary: Number(permittedFieldsFormat?.expectedSalary?.value),
      expectedSalaryCurrency:
        permittedFieldsFormat?.expectedSalaryCurrency?.value ||
        user?.currentTenant?.currency,
      typeOfExpectedSalary: permittedFieldsFormat?.typeOfExpectedSalary?.value,
      typeOfCurrentSalary: permittedFieldsFormat?.typeOfCurrentSalary?.value,
      currentSalary: Number(permittedFieldsFormat?.currentSalary?.value),
      currentSalaryInUsd: permittedFieldsFormat?.currentSalaryInUsd?.value,
      currentSalaryCurrency:
        permittedFieldsFormat?.currentSalaryCurrency?.value ||
        user?.currentTenant?.currency,
      languages: permittedFieldsFormat?.languages?.value,
      nationality: permittedFieldsFormat?.nationality?.value,
      noticeToPeriodDays: permittedFieldsFormat?.noticeToPeriodDays?.value,
      summary: permittedFieldsFormat?.summary?.value,
      willingToRelocate: permittedFieldsFormat?.willingToRelocate?.value,
      openToWork: permittedFieldsFormat?.openToWork?.value,
      ownerId: dataFormat.owner && {
        value: dataFormat.owner.id,
        avatar: dataFormat.owner?.avatarVariants?.thumb?.url,
        avatarVariants: dataFormat.owner.avatarVariants,
        supportingObj: {
          name: dataFormat.owner?.fullName,
          defaultColour: dataFormat.owner?.defaultColour
        }
      },
      totalYearsOfExp: permittedFieldsFormat?.totalYearsOfExp?.value,
      profileTalentPoolIds: (
        permittedFieldsFormat?.talentPools?.value || []
      )?.map((item: { name: string }) => Number(item.name)),
      preferredWorkStateIds: (
        permittedFieldsFormat?.preferredWorkStates?.value || []
      )?.map((item: { id: string }) => Number(item.id)),
      profileLevel: permittedFieldsFormat?.profileLevel?.value,
      customFields: formatInitialValueCustomField(dataFormat.customFields),
      locationWithStateID: permittedFieldsFormat?.location?.value
        ? {
            id: dataFormat?.countryStateId,
            value: permittedFieldsFormat?.location?.value,
            supportingObj: {
              name: permittedFieldsFormat?.location?.value
            }
          }
        : undefined
    }
  }

  const skillString = JSON.parse(
    JSON.stringify(permittedFields?.skills?.value || [])
  )
  const formatSkills = skillString.map((item: string) => {
    return {
      value: item,
      supportingObj: {
        name: item
      }
    }
  })

  const initialValues = useMemo(() => formatData(data), [data])

  const renderDueDate = (dueDate: string) => {
    const localeDate = new Date(dueDate).toLocaleDateString()
    const diffInDays = differenceInDays(
      localeDate ? new Date(localeDate) : new Date(),
      new Date().setHours(0, 0, 0, 0)
    )

    return dueDate ? (
      <div className="flex items-center">
        <TypographyText className="text-xs">
          {diffInDays === 0
            ? `${t('label:today')}`
            : diffInDays === 1
            ? `${t('label:tomorrow')}`
            : defaultFormatDate(dueDate ? new Date(dueDate) : new Date())}{' '}
        </TypographyText>
      </div>
    ) : null
  }
  const { isCompanyKind } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })

  const onFinishCallback = useCallback(
    async (data: IAIWriterForm, formAction: IFormAction) => {
      if (loadingGenerate) {
        return
      }
      triggerSummaryAIWriter({
        profileId: Number(id),
        headline: data.headline,
        skills: data.skills.map((item) => item.value),
        language: data.language
      }).then((result) => {
        if (result.error) {
          const parseErrors = JSON.parse(JSON.stringify(result.error))
          return catchErrorFromGraphQL({
            error: result.error,
            setToast,
            callbackHandleStatusError422: (keys) => {
              if (keys.length === 0) {
                return setToast({
                  open: true,
                  type: 'error',
                  title: parseErrors.graphQLErrors[0].message
                })
              }
            }
          })
        }

        const { profileSummary } = result.data.summaryAiWriter
        if (profileSummary) {
          setTextGenerate(profileSummary)
          setLanguageGenerate(data?.language || '')
        }
        return true
      })
    },
    [id, loadingGenerate]
  )
  const onRegenerate = () => {
    triggerSummaryAIWriter({
      profileId: Number(id),
      language: languageGenerate,
      summary: textGenerate
    }).then((result) => {
      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast
        })
      }

      const { profileSummary } = result.data.summaryAiWriter
      if (profileSummary) {
        setTextGenerate(profileSummary)
      }
      return true
    })
  }
  const onSubmitAddSummary = () => {
    return onUpdateProfile({
      summary: textGenerate,
      addActionLogsRelatedToAi: {
        summaryAiGenerated: !data?.permittedFields?.summary?.value,
        summaryAiRegenerated: !!data?.permittedFields?.summary?.value
      },
      paramType: 'summary'
    }).then((result) => {
      if (result) {
        if (richEditorRef.current) {
          richEditorRef.current.commands.setContent(textGenerate)
        }
        toggleAIWriter()
        setTextGenerate('')
        setLanguageGenerate('')
        setToast({
          open: true,
          type: 'success',
          title: !!data?.permittedFields?.summary?.value
            ? `${t('notification:candidates:replace_summary_success')}`
            : `${t('notification:candidates:add_summary_success')}`
        })
      }
    })
  }
  const checkEmptyContainsObjectKeys = (
    permittedFields: IPermittedFields,
    requiredKeys: string[]
  ): boolean => {
    return Object.keys(permittedFields)?.some(
      (key) => !requiredKeys.includes(key)
    )
  }

  return (
    <>
      <SkeletonContainer
        showMoreLabel={`${t('common:infinity:showMore')}`}
        useLoading={false}
        isFirstLoading={isFirstLoading}
        renderCustomSkeleton={
          <>
            {[1, 2, 3].map((item) => (
              <div key={`task-skeleton-${item}`}>
                <Skeleton className="mb-1.5 h-5 w-full rounded" />
                <Skeleton className="h-4 w-1/2 rounded" />
              </div>
            ))}
          </>
        }>
        {data?.id && (
          <DynamicImportForm<CandidateProfileInputType>
            mode="onSubmit"
            id="profile-candidate-form"
            className="w-full"
            defaultValue={initialValues}
            schema={schemaUpdateProfile(t)}
            onSubmit={onSubmit}>
            {({
              formState,
              control,
              getValues,
              trigger,
              setValue,
              setError,
              clearErrors,
              reset
            }) => {
              const forceSubmit = () => onSubmit(getValues())

              const filterJobWithStatusHired = dataJobs?.filter(
                (item) =>
                  item.status !== JOB_APPLICANT_STATUS.rejected &&
                  item?.jobStage?.stageGroup === JOB_STAGE_GROUP.hires
              )
              const filterJobWithStatusHiredByTab =
                profileCandidateTabState === PROFILE_CANDIDATE_TAB.jobRelated
                  ? filterJobWithStatusHired?.filter(
                      (item) => String(item?.id) === String(applicantId)
                    )
                  : filterJobWithStatusHired

              return (
                <>
                  <div className="mb-4 grid gap-y-2">
                    {isCompanyKind ? (
                      <>
                        {filterJobWithStatusHiredByTab?.map(
                          (item) =>
                            item?.placement && (
                              <PlacementAlert
                                // className="mt-2"
                                key={item?.placement?.id}
                                placement={item?.placement}
                                onPlacementEdited={() => {
                                  triggerFetchApplicants()
                                  setIsRefetchActivitiesList(true)
                                  return fetchProfile()
                                }}
                              />
                            )
                        )}
                      </>
                    ) : (
                      filterJobWithStatusHiredByTab?.map((item, i) =>
                        item?.placement && isShowPlacementFeature ? (
                          <DirectPlacementAlert
                            // className="mt-2"
                            key={item?.placement?.id}
                            placement={item?.placement}
                            onPlacementEdited={() => {
                              triggerFetchApplicants()
                              setIsRefetchActivitiesList(true)
                              return fetchProfile()
                            }}
                          />
                        ) : (
                          <div className="mb-2 min-h-[32px] space-y-2" key={i}>
                            <div className="flex justify-between space-x-4 rounded bg-ava-bg-400 px-3 py-1.5">
                              <div className="flex">
                                <div className="flex h-5 min-w-[15px] items-center justify-center pt-px">
                                  <CheckVerifyFillIcon
                                    size={16}
                                    className="fill-green-800"
                                  />
                                </div>
                                <TypographyText className="ml-2 text-sm text-green-800">
                                  <Trans
                                    i18nKey={
                                      'candidates:tabs:candidateOverview:profileInformation:hiredForJob'
                                    }
                                    values={{
                                      jobTitle: item?.job?.title,
                                      hiredBy:
                                        item?.hiredBy?.fullName ||
                                        item?.hiredBy?.email
                                    }}>
                                    <span className="font-medium" />
                                    <Link
                                      target="_blank"
                                      className="font-medium"
                                      href={configuration.path.jobs.detail(
                                        parseInt(item?.job.id)
                                      )}
                                    />
                                  </Trans>{' '}
                                  <span className="mx-2">•</span>
                                  {item?.currentStagedDate ? (
                                    <>
                                      {defaultFormatDate(
                                        changeTimezone({
                                          date: item?.currentStagedDate,
                                          timezone: user?.timezone
                                        })
                                      )}
                                    </>
                                  ) : null}
                                </TypographyText>
                              </div>

                              <If
                                condition={adminAndMemberCanAction(
                                  currentRole?.code
                                )}>
                                {checkPermissionForActions({
                                  user,
                                  currentRole,
                                  jobDetail: {
                                    owner: item?.job?.owner,
                                    jobRecruiters: item?.job?.jobRecruiters
                                  },
                                  permission: actionJob.update
                                }) ? (
                                  <Tooltip
                                    content={`${t('tooltip:edit')}`}
                                    classNameAsChild="-my-0.5">
                                    <IconButton
                                      size="xs"
                                      iconMenus="Edit3"
                                      type="secondary"
                                      onClick={() => {
                                        setApplicantCurrent &&
                                          setApplicantCurrent({
                                            item: {
                                              ...item,
                                              jobStageId: Number(
                                                item?.jobStage?.id
                                              )
                                            },
                                            callback: () => {
                                              setRefetch && setRefetch(true)
                                              setApplicantCurrent &&
                                                setApplicantCurrent({})
                                            }
                                          })
                                        setOpenMarkAsHired &&
                                          setOpenMarkAsHired(true)
                                      }}
                                    />
                                  </Tooltip>
                                ) : null}
                              </If>
                            </div>
                          </div>
                        )
                      )
                    )}
                    {profileCandidateTabState ===
                    PROFILE_CANDIDATE_TAB.jobRelated ? (
                      <>
                        {(dataInterviews || []).length ? (
                          <div className="mb-4 space-y-2 last:mb-0">
                            {(dataInterviews || [])
                              .filter(
                                (interview) =>
                                  interview?.state ===
                                  INTERVIEW_STATE_VALUE.confirmed
                              )
                              .map(
                                (item: InterviewDetailType, index: number) => {
                                  const feedbackFilter =
                                    FEEDBACK_OPTIONS.filter(
                                      (s) =>
                                        s.value ===
                                        item.currentUserFeedback
                                          ?.overallFeedback
                                    )

                                  const isFeedbackDueInterview =
                                    item.fromDatetime
                                      ? isFeedbackDue({
                                          startTime: item?.fromDatetime,
                                          hasFeedback:
                                            !!item?.ikitFeedbacksSummary?.length
                                        })
                                      : false

                                  if (feedbackFilter?.length) return null

                                  return (
                                    <div
                                      key={item.id}
                                      className={cn(
                                        'flex justify-between rounded px-3 py-1.5',
                                        isFeedbackDueInterview
                                          ? 'bg-ava-bg-50'
                                          : 'bg-blue-100'
                                      )}>
                                      <div className="flex items-center">
                                        <IconWrapper
                                          name="CalendarCheck2"
                                          size={14}
                                          className={
                                            isFeedbackDueInterview
                                              ? 'text-red-800'
                                              : 'text-blue-800'
                                          }
                                        />
                                        <div
                                          className={cn(
                                            'ml-2 text-xs font-medium',
                                            isFeedbackDueInterview
                                              ? 'text-red-800'
                                              : 'text-blue-800'
                                          )}>
                                          {item.eventTypeDescription}
                                        </div>
                                        <span
                                          className={cn(
                                            'mx-2 h-0.5 w-0.5 rounded',
                                            isFeedbackDueInterview
                                              ? 'bg-red-800'
                                              : 'bg-blue-800'
                                          )}
                                        />
                                        <div
                                          className={cn(
                                            'flex space-x-1 text-xs font-medium',
                                            isFeedbackDueInterview
                                              ? 'text-red-800'
                                              : 'text-blue-800'
                                          )}>
                                          {item?.fromDatetime
                                            ? renderDueDate(item.fromDatetime)
                                            : ''}{' '}
                                          <span>
                                            {item?.fromDatetime
                                              ? timeFormatDate(
                                                  changeTimezone({
                                                    date: item?.fromDatetime,
                                                    timezone: user?.timezone
                                                  })
                                                )
                                              : null}
                                            {item?.toDatetime
                                              ? ` (GMT${getTimeZone(
                                                  item.toDatetime
                                                )})`
                                              : null}
                                          </span>
                                        </div>
                                      </div>
                                      {item?.attendees?.length && (
                                        <div className="flex items-center space-x-4">
                                          <AvatarGroup
                                            toolTipPosition="top"
                                            tooltipAlign="center"
                                            size="2xs"
                                            className="-space-x-1"
                                            source={item.attendees.map(
                                              (item) => ({
                                                id: Number(item.id),
                                                alt: item.fullName,
                                                src: item.avatarVariants?.thumb
                                                  ?.url,
                                                tooltip: (
                                                  <>
                                                    {item.fullName}
                                                    <div>{item.email}</div>
                                                  </>
                                                ),
                                                defaultColour:
                                                  item.defaultColour
                                              })
                                            )}
                                          />
                                        </div>
                                      )}
                                    </div>
                                  )
                                }
                              )}
                          </div>
                        ) : null}
                      </>
                    ) : null}
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      <TypographyText className="text-lg font-medium text-gray-900">
                        {t(
                          'candidates:tabs:candidateOverview:contactDetails:heading'
                        )}
                      </TypographyText>
                    </div>

                    {checkIfArrayStringsContainsObjectKeys(
                      CONTACT_DETAILS,
                      data.permittedFields
                    ) ? (
                      <ContactDetailsView
                        applicantId={applicantId}
                        trigger={trigger}
                        submit={forceSubmit}
                        data={data}
                        control={control}
                        submitPartialField={submitPartialField}
                        formState={formState}
                        configHide={{ job: true }}
                      />
                    ) : (
                      <TypographyText className="text-sm text-gray-600">
                        {t(
                          'candidates:tabs:candidateOverview:contactNotAvailable'
                        )}
                      </TypographyText>
                    )}
                  </div>

                  <div className="mt-6 space-y-1">
                    <div className="flex items-center justify-between">
                      <TypographyText className="text-lg font-medium text-gray-900">
                        {t(
                          'candidates:tabs:candidateOverview:profileInformation:heading'
                        )}
                      </TypographyText>
                      {actionCustomField && (
                        <TextButton
                          iconMenus="FileEdit"
                          underline={false}
                          size="md"
                          label={`${t('button:custom')}`}
                          onClick={() => {
                            window.open(SETTING_CUSTOM_FIELDS, '_blank')
                          }}
                        />
                      )}
                    </div>
                    {!!data.permittedFields &&
                    checkEmptyContainsObjectKeys(
                      data.permittedFields,
                      REQUIRED_KEYS
                    ) ? (
                      <ProfileInformationView
                        disableSourceReferral={disableSourceReferral}
                        applicantId={applicantId}
                        trigger={trigger}
                        submit={forceSubmit}
                        data={data}
                        control={control}
                        isLoadingUpdateProfile={isLoadingUpdateProfile}
                        onUpdateProfile={onUpdateProfile}
                        submitPartialField={submitPartialField}
                        formState={formState}
                        setValue={setValue}
                        setError={setError}
                        clearErrors={clearErrors}
                        configHide={{
                          summary: true
                        }}
                        profileOverviewRef={profileOverviewRef}
                        isDrawer={isDrawer}
                      />
                    ) : (
                      <TypographyText className="text-sm text-gray-600">
                        {t('candidates:tabs:candidateOverview:notAvailable')}
                      </TypographyText>
                    )}
                  </div>

                  <EmployeeReferral
                    setDisableSourceReferral={setDisableSourceReferral}
                    profileId={data?.id}
                    setSwitchView={setSwitchView}
                  />
                  <If
                    condition={
                      permittedFieldsManagement(
                        data?.permittedFields?.summary?.roles
                      ) || permittedFields?.summary
                    }>
                    <div className="mb-1.5 mt-5 space-y-1">
                      <div className="flex w-full items-center">
                        <TypographyText className="text-lg font-medium text-gray-900">
                          {t(
                            'candidates:tabs:candidateOverview:summary:heading'
                          )}
                        </TypographyText>
                      </div>
                      <div className="-mx-2 -my-1.5 space-y-0.5">
                        <Controller
                          control={control}
                          name="summary"
                          render={({ field: { onChange, value } }) => {
                            return (
                              <FormControlItem>
                                <DisableControlByPermission
                                  applicantId={applicantId}
                                  text={
                                    <div className="px-2 py-1.5">
                                      {value ? (
                                        <HTMLDisplay
                                          content={value}
                                          className="text-sm text-gray-900"
                                        />
                                      ) : (
                                        <div className="line-clamp-1 text-sm text-gray-600">
                                          {t(
                                            'candidates:tabs:candidateOverview:notAvailable'
                                          )}
                                        </div>
                                      )}
                                    </div>
                                  }>
                                  <InlineEditingEditor
                                    extraToolbar={{
                                      AIWriter: {
                                        show:
                                          isFeatureEnabled(
                                            PLAN_FEATURE_KEYS.ai_writer
                                          ) &&
                                          isUnLockFeature(
                                            PLAN_FEATURE_KEYS.ai_writer
                                          ),
                                        isReplace:
                                          !!data?.permittedFields?.summary
                                            ?.value,
                                        onSubmitAIWriter: onFinishCallback,
                                        loadingGenerate: loadingGenerate,
                                        textGenerate: textGenerate,
                                        defaultValue: {
                                          headline:
                                            initialValues.headline || '',
                                          profileId: Number(initialValues.id),
                                          skills: formatSkills
                                        },
                                        onRegenerate: onRegenerate,
                                        onSubmitAddSummary: onSubmitAddSummary,
                                        modal: isDrawer
                                      }
                                    }}
                                    editorRef={(editor: CoreEditor) => {
                                      richEditorRef.current = editor
                                    }}
                                    toggleAIWriter={toggleAIWriter}
                                    openAIWriter={openAIWriter}
                                    autoSave
                                    localStorageId={`profile-${data?.id}-summary`}
                                    className="min-w-full"
                                    limit={100000}
                                    showCount={false}
                                    onChange={(newValue) => {
                                      onChange(newValue)
                                      return submitPartialField(
                                        'summary',
                                        trigger,
                                        forceSubmit
                                      )
                                    }}
                                    placeholder={`${t(
                                      'candidates:tabs:candidateOverview:summary:addSummary'
                                    )}`}
                                    size="sm"
                                    value={
                                      data?.permittedFields?.summary?.value
                                    }
                                    tooltipActionCancel={{
                                      title: `${t('button:cancel')}`
                                    }}
                                    tooltipActionSave={{
                                      title: `${t('button:saveEnter')}`
                                    }}
                                    tooltipError={{
                                      position: 'right',
                                      align: 'end'
                                    }}
                                    destructiveText={
                                      //@ts-ignore
                                      formState?.errors?.summary
                                        ?.message as string
                                    }>
                                    <div className="flex items-center px-2 py-1.5 text-xl">
                                      {data?.permittedFields?.summary?.value ? (
                                        <HTMLDisplay
                                          content={
                                            data?.permittedFields?.summary
                                              ?.value
                                          }
                                          className="text-sm text-gray-900"
                                        />
                                      ) : (
                                        <TypographyText className="text-sm text-gray-600">
                                          {t(
                                            'candidates:tabs:candidateOverview:summary:addSummary'
                                          )}
                                        </TypographyText>
                                      )}
                                    </div>
                                  </InlineEditingEditor>
                                </DisableControlByPermission>
                              </FormControlItem>
                            )
                          }}
                        />
                      </div>
                    </div>
                  </If>

                  {data?.applicants?.[0]?.coverLetter &&
                  data?.applicants?.length &&
                  data?.applicants?.[0]?.job?.status !==
                    JOB_STATUS_ENUM.archived ? (
                    <div className="mb-1.5 mt-5 space-y-1">
                      <TypographyText className="text-lg font-medium text-gray-900">
                        {t(
                          'candidates:tabs:candidateOverview:coverLetter:heading'
                        )}
                      </TypographyText>
                      {data?.applicants?.[0]?.coverLetter && (
                        <div className="-mx-2 -my-1.5 space-y-0.5">
                          <div className="flex items-center px-2 py-1.5 text-xl text-gray-900">
                            <HTMLDisplay
                              content={data?.applicants?.[0]?.coverLetter}
                              className="text-sm text-gray-900"
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ) : null}
                  <If
                    condition={
                      permittedFieldsManagement(
                        permittedFields?.profileCvs?.roles
                      ) || permittedFields?.profileCvs
                    }>
                    <div className="mt-5 space-y-3">
                      <div className="flex items-center justify-between">
                        <TypographyText className="text-lg font-medium text-gray-900">
                          {t(
                            'candidates:tabs:candidateOverview:resumeCv:heading'
                          )}
                        </TypographyText>

                        {profileCVS ? (
                          <div className="flex items-center space-x-2">
                            {((!isCompanyKind &&
                              !limitedMemberCanAction(currentRole?.code)) ||
                              (isCompanyKind && !userIsAsClient())) && (
                              <Tooltip content={t('tooltip:re_parser')}>
                                <IconButton
                                  size="xs"
                                  iconMenus="FileCheck2"
                                  type="secondary"
                                  onClick={() =>
                                    openAlert({
                                      isPreventAutoFocusDialog: false,
                                      className: 'w-[480px]',
                                      title: `${t(
                                        'candidates:tabs:candidateOverview:resumeCv:re_parser_title'
                                      )}`,
                                      description: `${t(
                                        'candidates:tabs:candidateOverview:resumeCv:re_parser_description'
                                      )}`,
                                      actions: [
                                        {
                                          label: `${t('button:cancel')}`,
                                          type: 'secondary',
                                          size: 'sm'
                                        },
                                        {
                                          label: `${t('button:confirm')}`,
                                          size: 'sm',
                                          onClick: (e) => {
                                            setShowLockApp('')
                                            triggerReparserProfile({
                                              profileId: Number(id)
                                            }).then((result) => {
                                              setCloseLockApp()

                                              if (result.error) {
                                                return setToast({
                                                  open: true,
                                                  type: 'success',
                                                  title: `${t(
                                                    'candidates:tabs:candidateOverview:resumeCv:failed_to_parse'
                                                  )}`
                                                })
                                              }

                                              return fetchProfile().then(
                                                (data) => {
                                                  reset(
                                                    formatData(
                                                      data as unknown as ICandidateProfile
                                                    )
                                                  )
                                                  setToast({
                                                    open: true,
                                                    type: 'success',
                                                    title: `${t(
                                                      'candidates:tabs:candidateOverview:resumeCv:data_successfully_parsed'
                                                    )}`
                                                  })
                                                }
                                              )
                                            })
                                          }
                                        }
                                      ]
                                    })
                                  }
                                />
                              </Tooltip>
                            )}
                            <Tooltip content={t('tooltip:download')}>
                              <IconButton
                                size="xs"
                                iconMenus="Download"
                                type="secondary"
                                onClick={() => {
                                  const item =
                                    data.profileCvs?.[0]?.attachments?.[0]
                                  if (item) {
                                    fetchAndDownloadFile({
                                      file: item?.file,
                                      name: item?.blobs?.filename
                                    })
                                  }
                                }}
                              />
                            </Tooltip>
                            <DisableControlByPermission
                              applicantId={applicantId}>
                              <Tooltip content={t('tooltip:uploadANewFile')}>
                                <IconButton
                                  size="xs"
                                  iconMenus="Upload"
                                  type="secondary"
                                  onClick={() => {
                                    const getId = document.getElementById(
                                      'file-upload-drag-drop'
                                    )
                                    if (getId) {
                                      getId.click()
                                    }
                                  }}
                                />
                              </Tooltip>
                              <Tooltip content={t('tooltip:delete')}>
                                <IconButton
                                  size="xs"
                                  iconMenus="Trash2"
                                  type="secondary"
                                  onClick={() => {
                                    openAlert({
                                      isPreventAutoFocusDialog: false,
                                      className: 'w-[480px]',
                                      title: `${t(
                                        'common:modal:delete_cv_title'
                                      )}`,
                                      description: `${t(
                                        'common:modal:delete_cv_description'
                                      )}`,
                                      actions: [
                                        {
                                          label: `${t('button:cancel')}`,
                                          type: 'secondary',
                                          size: 'sm'
                                        },
                                        {
                                          isCallAPI: true,
                                          label: `${t('button:remove')}`,
                                          type: 'destructive',
                                          size: 'sm',
                                          onClick: async () => {
                                            if (
                                              permittedFields?.profileCvs.value
                                                ?.length
                                            ) {
                                              await onDeleteProfileCVs(
                                                Number(
                                                  permittedFields?.profileCvs
                                                    .value[0].id
                                                )
                                              )
                                            }
                                          }
                                        }
                                      ]
                                    })
                                  }}
                                />
                              </Tooltip>
                            </DisableControlByPermission>
                          </div>
                        ) : null}
                      </div>

                      <DisableControlByPermission
                        applicantId={applicantId}
                        text={
                          <>
                            {data.profileCvs && data.profileCvs.length > 0 ? (
                              (data.profileCvs || []).map((group, i) => (
                                <div key={i} className="min-h-full">
                                  {isBrowser() ? (
                                    <AttachmentView
                                      item={
                                        group.attachments?.length
                                          ? group.attachments[0]
                                          : undefined
                                      }
                                    />
                                  ) : null}
                                </div>
                              ))
                            ) : (
                              <div className="text-sm text-gray-600">
                                {t(
                                  'candidates:tabs:candidateOverview:notAvailable'
                                )}
                              </div>
                            )}
                          </>
                        }>
                        <div
                          className={profileCVS ? 'hidden' : ''}
                          style={{ display: profileCVS ? 'none' : '' }}>
                          <Controller
                            control={control}
                            name="resumeFile"
                            render={({ field: { onChange, value } }) => {
                              return (
                                <FormControlItem
                                  destructive={
                                    formState.errors &&
                                    !!formState.errors?.resumeFile
                                  }
                                  destructiveText={
                                    formState.errors &&
                                    (formState.errors?.resumeFile
                                      ?.message as string)
                                  }>
                                  <UploadFileDragAndDrop
                                    wrapperID="file-upload-drag-drop"
                                    className="w-full"
                                    configText={{
                                      clickToUpload: `${t(
                                        'label:dragAndDrop:clickToUpload'
                                      )}`,
                                      orDragAndDrop: `${t(
                                        'label:dragAndDrop:orDragAndDrop'
                                      )}`,
                                      delete: `${t('tooltip:delete')}`,
                                      tryAgain: `${t('tooltip:tryAgain')}`,
                                      uploadANewFile: `${t(
                                        'tooltip:uploadANewFile'
                                      )}`
                                    }}
                                    dragNDropHelperText={`${t(
                                      'careers:applied:dragNDropTypeFiles'
                                    )}`}
                                    maximumSizeFile="10MB"
                                    maximumFiles={1}
                                    accept={{
                                      'application/pdf': ['.pdf'],
                                      'application/msword': ['.doc'],
                                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                                        ['.docx']
                                    }}
                                    onChange={(files) => {
                                      let acceptedUpload = true
                                      let statusDescription = ''
                                      Array.from(files || []).forEach(
                                        (file) => {
                                          if (
                                            file.size > MAXIMUM_10_MB ||
                                            !LIST_ACCEPT_FILES_PDF_DOC.includes(
                                              file.type
                                            )
                                          ) {
                                            acceptedUpload = false
                                            statusDescription =
                                              file.size > MAXIMUM_10_MB
                                                ? `${t(
                                                    'form:maximum_size_is_10MB'
                                                  )}`
                                                : !LIST_ACCEPT_FILES_PDF_DOC.includes(
                                                    file.type
                                                  )
                                                ? `${t(
                                                    'form:only_supported_PDF_DOCX_DOC'
                                                  )}`
                                                : ''
                                          }
                                        }
                                      )

                                      if (
                                        files &&
                                        typeof files === 'object' &&
                                        acceptedUpload
                                      ) {
                                        onChange(files)
                                        return submitPartialField(
                                          'resumeFile',
                                          trigger,
                                          forceSubmit
                                        )
                                      } else {
                                        setToast({
                                          open: true,
                                          type: 'error',
                                          title: statusDescription
                                        })
                                        return
                                      }
                                    }}
                                  />
                                </FormControlItem>
                              )
                            }}
                          />
                        </div>

                        {(data.profileCvs || []).map((group, i) => (
                          <div key={i} className="min-h-full">
                            {isBrowser() ? (
                              <AttachmentView
                                item={
                                  group.attachments?.length
                                    ? group.attachments[0]
                                    : undefined
                                }
                              />
                            ) : null}
                          </div>
                        ))}
                      </DisableControlByPermission>
                    </div>
                  </If>
                </>
              )
            }}
          </DynamicImportForm>
        )}
        <If
          condition={
            permittedFieldsManagement(
              permittedFields?.workExperiences?.roles
            ) || permittedFields?.workExperiences
          }>
          <div className="mt-6 space-y-1">
            <div className="mb-2.5 flex items-center justify-between">
              <TypographyText className="text-lg font-medium text-gray-900">
                {t('candidates:tabs:candidateOverview:workExperiences:heading')}
              </TypographyText>
              <DisableControlByPermission applicantId={applicantId}>
                <TextButton
                  iconMenus="Plus"
                  underline={false}
                  size="md"
                  label={`${t('button:add')}`}
                  onClick={() => {
                    setOpenWorkExp(true)
                    setDefaultValueWorkExp(undefined)
                  }}
                />
              </DisableControlByPermission>
            </div>

            {permittedFields?.workExperiences?.value?.length ? (
              <WorkExperiencesView
                applicantId={applicantId}
                source={permittedFields.workExperiences.value}
                isLoadingUpdateProfile={isLoadingUpdateProfile}
                onClick={(item) => {
                  setOpenWorkExp(true)
                  setDefaultValueWorkExp({
                    ...item,
                    fromMonth: item.fromMonth
                      ? item.fromMonth.toString()
                      : undefined,
                    toMonth: item.toMonth ? item.toMonth.toString() : undefined,
                    fromYear: item.fromYear
                      ? item.fromYear.toString()
                      : undefined,
                    toYear: item.toYear ? item.toYear.toString() : undefined,
                    location: item?.location
                      ? {
                          id: item?.countryStateId,
                          value: item?.location,
                          supportingObj: {
                            name: item?.location
                          }
                        }
                      : undefined,
                    position: item.position?.toString()
                  })
                }}
                onDelete={(item) => {
                  onUpdateProfile({
                    workExperiences: [
                      {
                        ...item,
                        _destroy: true
                      }
                    ],
                    paramType: 'workExperiences'
                  })
                }}
              />
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </If>
        <If
          condition={
            permittedFieldsManagement(permittedFields?.educations?.roles) ||
            permittedFields?.educations
          }>
          <div className="mt-6 space-y-1">
            <div className="mb-2.5 flex items-center justify-between">
              <TypographyText className="text-lg font-medium text-gray-900">
                {t('candidates:tabs:candidateOverview:education:heading')}
              </TypographyText>
              <DisableControlByPermission applicantId={applicantId}>
                <TextButton
                  iconMenus="Plus"
                  underline={false}
                  size="md"
                  label={`${t('button:add')}`}
                  onClick={() => {
                    setOpenEducation(true)
                    setDefaultValueEducation(undefined)
                  }}
                />
              </DisableControlByPermission>
            </div>

            {permittedFields?.educations?.value?.length ? (
              <EducationView
                applicantId={applicantId}
                source={permittedFields?.educations.value}
                isLoadingUpdateProfile={isLoadingUpdateProfile}
                onClick={(item) => {
                  setOpenEducation(true)
                  setDefaultValueEducation({
                    ...item,
                    from: {
                      month: item?.from
                        ? monthFormatDate(new Date(item.from))
                        : undefined,
                      year: item?.from
                        ? yearFormatDate(new Date(item.from))
                        : undefined
                    },
                    to: {
                      month: item?.to
                        ? monthFormatDate(new Date(item.to))
                        : undefined,
                      year: item?.to
                        ? yearFormatDate(new Date(item.to))
                        : undefined
                    }
                  })
                }}
                onDelete={(item) => {
                  onUpdateProfile({
                    educations: [
                      {
                        ...item,
                        _destroy: true
                      }
                    ],
                    paramType: 'educations'
                  })
                }}
              />
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </If>
        <If
          condition={
            permittedFieldsManagement(permittedFields?.certificates?.roles) ||
            permittedFields?.certificates
          }>
          <div className="mt-6 space-y-1">
            <div className="mb-2.5 flex items-center justify-between">
              <TypographyText className="text-lg font-medium text-gray-900">
                {t('candidates:tabs:candidateOverview:certificates:heading')}
              </TypographyText>
              <DisableControlByPermission applicantId={applicantId}>
                <TextButton
                  iconMenus="Plus"
                  underline={false}
                  size="md"
                  label={`${t('button:add')}`}
                  onClick={() => {
                    setOpenCertificates(true)
                    setDefaultValueCertificates({
                      position:
                        (permittedFields?.certificates?.value || [])?.length + 1
                    })
                  }}
                />
              </DisableControlByPermission>
            </div>

            {permittedFields?.certificates?.value?.length ? (
              <CertificatesView
                applicantId={applicantId}
                source={permittedFields?.certificates.value}
                isLoadingUpdateProfile={isLoadingUpdateProfile}
                onClick={(item) => {
                  setOpenCertificates(true)
                  setDefaultValueCertificates({
                    ...item,
                    issueMonth: String(item.issueMonth || ''),
                    issueYear: String(item.issueYear || '')
                  })
                }}
                onDelete={(item) => {
                  onUpdateProfile({
                    certificates: [
                      {
                        ...item,
                        _destroy: true
                      }
                    ],
                    paramType: 'certificates'
                  })
                }}
              />
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </If>
        <If
          condition={
            permittedFieldsManagement(permittedFields?.references?.roles) ||
            permittedFields?.references
          }>
          <div className="mt-6 space-y-1">
            <div className="mb-2.5 flex items-center justify-between">
              <TypographyText className="text-lg font-medium text-gray-900">
                {t('candidates:tabs:candidateOverview:references:heading')}
              </TypographyText>
              <DisableControlByPermission applicantId={applicantId}>
                <TextButton
                  iconMenus="Plus"
                  underline={false}
                  size="md"
                  label={`${t('button:add')}`}
                  onClick={() => {
                    setOpenReferences(true)
                    setDefaultValueReferences({
                      index: data.references?.length
                    })
                  }}
                />
              </DisableControlByPermission>
            </div>

            {permittedFields?.references?.value?.length ? (
              <ReferenceView
                applicantId={applicantId}
                source={permittedFields?.references.value}
                isLoadingUpdateProfile={isLoadingUpdateProfile}
                onClick={(item) => {
                  setOpenReferences(true)
                  setDefaultValueReferences(item)
                }}
                onDelete={(item) => {
                  onUpdateProfile({
                    references: [
                      {
                        ...item,
                        _destroy: true
                      }
                    ],
                    paramType: 'references'
                  })
                }}
              />
            ) : (
              <TypographyText className="text-sm text-gray-600">
                {t('candidates:tabs:candidateOverview:notAvailable')}
              </TypographyText>
            )}
          </div>
        </If>
      </SkeletonContainer>

      <WorkExperiencesModal
        open={openWorkExp}
        setOpen={setOpenWorkExp}
        isLoadingUpdateProfile={isLoadingUpdateProfile}
        defaultValue={defaultValueWorkExp}
        onSubmit={(data) => {
          onUpdateProfile({
            workExperiences: [
              {
                id: defaultValueWorkExp?.id
                  ? defaultValueWorkExp.id
                  : undefined,
                title: data.title,
                company: data.company,
                fromMonth: data.fromMonth,
                fromYear: data.fromYear,
                toMonth: data.toMonth,
                toYear: data.toYear,
                location: data?.location?.value,
                countryStateId: Number(data?.location?.id),
                description: data.description,
                currentWorking: data.currentWorking,
                position: Number(data?.position),
                _destroy: false
              }
            ],
            paramType: 'workExperiences'
          }).then(() => {
            setOpenWorkExp(false)
            setDefaultValueWorkExp(undefined)
          })

          return Promise.resolve()
        }}
      />

      <EducationModal
        open={openEducation}
        setOpen={setOpenEducation}
        isLoadingUpdateProfile={isLoadingUpdateProfile}
        defaultValue={defaultValueEducation}
        onSubmit={(data) => {
          onUpdateProfile({
            educations: [
              {
                id: defaultValueEducation?.id
                  ? defaultValueEducation.id
                  : undefined,
                schoolName: data.schoolName,
                degree: data.degree,
                from: `${data?.from?.year}-${
                  data?.from?.month
                }-01T00:00:00${convertTimezone(String(user?.timezone))}`,
                to: `${data?.to?.year}-${
                  data?.to?.month
                }-01T00:00:00${convertTimezone(String(user?.timezone))}`,
                degreeSubject: data.degreeSubject,
                description: data.description,
                _destroy: false
              }
            ],
            paramType: 'educations'
          }).then(() => {
            setOpenEducation(false)
            setDefaultValueEducation(undefined)
          })

          return Promise.resolve()
        }}
      />

      <ReferenceModal
        open={openReferences}
        setOpen={setOpenReferences}
        isLoadingUpdateProfile={isLoadingUpdateProfile}
        defaultValue={defaultValueReferences}
        onSubmit={(data) => {
          onUpdateProfile({
            references: [
              {
                index: defaultValueReferences?.index,
                id: defaultValueReferences?.id
                  ? defaultValueReferences.id
                  : undefined,
                name: data.name,
                email: data.email,
                _destroy: false
              }
            ],
            paramType: 'references'
          }).then(() => {
            setOpenReferences(false)
            setDefaultValueReferences(undefined)
          })

          return Promise.resolve()
        }}
      />

      <CertificatesModal
        open={openCertificates}
        setOpen={setOpenCertificates}
        isLoadingUpdateProfile={isLoadingUpdateProfile}
        defaultValue={defaultValueCertificates}
        onSubmit={(data) => {
          onUpdateProfile({
            certificates: [
              {
                id: defaultValueCertificates?.id
                  ? defaultValueCertificates.id
                  : undefined,
                certificateName: data.certificateName,
                institution: data.institution,
                issueMonth: data.issueMonth,
                issueYear: data.issueYear,
                position: defaultValueCertificates?.position,
                _destroy: false
              }
            ],
            paramType: 'certificates'
          }).then(() => {
            setOpenCertificates(false)
            setDefaultValueCertificates(undefined)
          })

          return Promise.resolve()
        }}
      />
    </>
  )
}

export default CandidateProfileViewInfo
