import { InfiniteData } from '@tanstack/react-query'
import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import AssignJobListModal from '~/components/Jobs/AssignJobListModal'
import configuration from '~/configuration'
import { AGENCY_TENANT } from '~/core/constants/enum'
import { Button } from '~/core/ui/Button'
import { Dialog } from '~/core/ui/Dialog'
import { IApplicableJobs } from '~/lib/features/calendar/types'
import useJobManagement from '~/lib/features/jobs/hooks/use-job-management'
import { DEFAULT_SELECTED_JOB_STATUS_ROLE_MAP } from '~/lib/features/jobs/utilities/enum'
import useDetectCompanyWithKind from '~/lib/hooks/use-detect-company-with-kind'

const DialogJobList: FC<{
  onSubmit: (data: string) => Promise<void>
  open: boolean
  onClose: (val: boolean) => void
  onReload: () => void
}> = ({ onSubmit, open, onClose, onReload }) => {
  const { t } = useTranslation()
  const { isCompanyKind, isLoaded } = useDetectCompanyWithKind({
    kind: AGENCY_TENANT
  })

  const { jobPaging, filterControl } = useJobManagement({
    isCompanyKind,
    isLoaded,
    isActive: true
  })
  const [assignedJobIds, setAssignedJobIds] = useState<string[]>([])
  const hasSubmitted = useRef(false)

  const [filter] = useState({
    page: 1,
    limit: configuration.defaultPageSize,
    search: '',
    status: DEFAULT_SELECTED_JOB_STATUS_ROLE_MAP['defaultNoDraft'].map(
      (status) => ({
        value: status,
        supportingObj: { name: '' }
      })
    )
  })
  const handleSubmit = useCallback(
    (jobId: string) => {
      onSubmit(jobId)
      setAssignedJobIds((prev) => [...prev, jobId])
      hasSubmitted.current = true
    },
    [onSubmit]
  )

  const filteredJobData:
    | InfiniteData<{
        jobsList: {
          collection: IApplicableJobs[]
          metadata: {
            totalCount: number
          }
        }
      }>
    | undefined = useMemo(() => {
    if (!jobPaging?.data) return undefined

    return {
      ...jobPaging.data,
      pages: jobPaging.data.pages.map((page) => ({
        ...page,
        jobsList: {
          ...page?.jobsList,
          collection: page?.jobsList?.collection?.filter(
            (item: IApplicableJobs) => !assignedJobIds.includes(item.id)
          )
        }
      }))
    }
  }, [jobPaging?.data, assignedJobIds])

  const handleDialogOpenChange = (isOpen: boolean) => {
    if (!isOpen && hasSubmitted.current) {
      onReload()
      hasSubmitted.current = false
    }
    onClose(isOpen)
  }

  return (
    <Dialog
      open={open}
      size="sm"
      onOpenChange={handleDialogOpenChange}
      isPreventAutoFocusDialog={true}
      label={`${t('button:assignJob')}`}>
      <AssignJobListModal
        filter={{
          ...filter,
          search: filterControl.value?.search || ''
        }}
        setFilter={filterControl.onChange}
        setOpen={onClose}
        data={filteredJobData}
        onAssign={handleSubmit}
        hasNextPage={jobPaging.hasNextPage}
        fetchNextPage={jobPaging.fetchNextPage}
      />
    </Dialog>
  )
}

const AssignJobProfiles: FC<{
  onSubmit: (data: string) => Promise<void>
  onReload: () => void
}> = (props) => {
  const { t } = useTranslation()
  const [openDialog, setOpenDialog] = useState<boolean>(false)

  return (
    <>
      <Button
        size="sm"
        iconMenus="Send"
        type="secondary"
        onClick={() => setOpenDialog(true)}
        label={`${t('button:assignJob')}`}
      />
      {openDialog && (
        <DialogJobList open={openDialog} onClose={setOpenDialog} {...props} />
      )}
    </>
  )
}

export default AssignJobProfiles
