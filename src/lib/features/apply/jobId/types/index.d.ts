import { ILogoAndAvatarVariants } from '~/core/@types/global'
import { CustomFieldFormType } from '~/lib/features/settings/profile-fields/types/custom-field'
import { CompanyDetailType } from '../../../agency/companies/types/company-detail'
export interface JobType {
  id: string
  title: string
  pitch: string
  description: string
  status: string
  jobReferable?: boolean
  remoteStatus: string
  slug: string
  remoteStatusDescription: string
  currency: string
  salaryFrom: number
  salaryTo: number
  currencyDescription: string
  employmentTypeDescription: string
  typeOfSalaryDescription: string
  jobLocations: Array<{
    stateAndCountry?: string
    state: string
    city: string
    country: string
    name: string
    address: string
    zipCode: string
  }>
  industries: Array<{
    name: string
  }>
  tenant: {
    name: string
    logoVariants?: ILogoAndAvatarVariants
    slug: string
    description: string
    careerSiteSettings: {
      canonical_url: string
      description:
        | string
        | {
            [key: string]: string
          }
      logoVariants?: ILogoAndAvatarVariants
      page_title: string
      department_visibility?: string
    }
  }
  department: {
    name: string
    parent: {
      name: string
    }
  }
  createdAt: string
  jobCategory: {
    name: string
    description: string
  }
  jobLevelDescription: string
  educationDescription: string
  skills: string[]
  preferences: {
    google_job_boards?: {
      published?: boolean
      published_date?: string
      unpublished_date?: string
    }
  }
  enablingReward: boolean
  rewardAmount: number
  referralRewardType: string
  rewardCurrency: string
  rewardGift: string
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: (string | string[]) & CompanyDetailType
    }
  }
  publicReferralUri?: string
  recommendationMatchedFields?: {
    field: string
    total_rate: number
    total_rate_string: string
  }[]
  currentUserAppliedAt: string
  tags?: Array<{
    id: string
    name: string
  }>
  customFields?: CustomFieldResponseItem[]
}

export interface ApplicationFormType {
  fullName: string
  email: string
  headline?: string
  phoneNumber: string
  resumeFile?: File[]
  coverLetter?: string
  attachments?: {
    id: string
    blobs: {
      filename: string
      content_type: string
      size: number
    }
    file: string
  }[]
  termsAndConditions?: boolean
  customFields?: CustomFieldFormType['customFields']
}
