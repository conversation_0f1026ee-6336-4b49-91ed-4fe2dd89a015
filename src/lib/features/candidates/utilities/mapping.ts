import { ISelectOption } from '~/core/ui/Select'
import { uuidV4 } from '~/core/ui/utils'
import { formatSubmitCustomFieldData } from '../../settings/profile-fields/mapping/custom-field-mapping'
import { CandidateProfileInputType, IProfileListType } from '../types'

export const mappingFormatFieldUpdateProfile = ({
  currentOriginalValue,
  currentField,
  value
}: {
  currentOriginalValue: IProfileListType
  currentField: string
  value: unknown
}) => {
  let fieldUpdate = currentField
  let newValue = value

  if (currentField === 'ownerId') {
    fieldUpdate = 'owner'
    const formatValueOwner = value as ISelectOption
    newValue = {
      id: formatValueOwner.value,
      avatarVariants: formatValueOwner.avatarVariants,
      fullName: formatValueOwner.supportingObj?.name,
      defaultColour: formatValueOwner.supportingObj?.defaultColour
    }
  }

  if (
    [
      'fullName',
      'headline',
      'email',
      'phoneNumber',
      'locationWithStateID',
      'links',
      'profileTalentPoolIds',
      'preferredWorkStateIds',
      'summary',
      'skills',
      'openToWork',
      'languages',
      'nationality',
      'birthday',
      'willingToRelocate',
      'noticeToPeriodDays',
      'currentSalary',
      'expectedSalary',
      'profileLevel',
      'totalYearsOfExp'
    ].includes(currentField)
  ) {
    fieldUpdate = 'permittedFields'

    switch (currentField) {
      case 'profileTalentPoolIds':
        const formatValueTalentPool = value as ISelectOption[]
        newValue = {
          ...currentOriginalValue.permittedFields,
          talentPools: {
            ...(currentOriginalValue.permittedFields?.talentPools || {}),
            value: formatValueTalentPool?.map((item: ISelectOption) => ({
              id: item.value,
              name: item?.supportingObj?.name
            }))
          }
        }
        break

      case 'preferredWorkStateIds':
        const formatValuePreferredWorkState = value as ISelectOption[]
        newValue = {
          ...currentOriginalValue.permittedFields,
          preferredWorkStates: {
            ...(currentOriginalValue.permittedFields?.preferredWorkStates ||
              {}),
            value: formatValuePreferredWorkState?.map(
              (item: ISelectOption) => ({
                id: item.id,
                full_name: item?.supportingObj?.name
              })
            )
          }
        }
        break

      case 'currentSalary':
        const formatValueCurrentSalary = value as {
          currentSalary: number
          currentSalaryCurrency: string
          typeOfCurrentSalary: string
        }
        newValue = {
          ...currentOriginalValue.permittedFields,
          currentSalary: {
            ...(currentOriginalValue.permittedFields?.currentSalary || {}),
            value: formatValueCurrentSalary?.currentSalary
          },
          currentSalaryCurrency: {
            ...(currentOriginalValue.permittedFields?.currentSalaryCurrency ||
              {}),
            value: formatValueCurrentSalary?.currentSalaryCurrency
          },
          typeOfCurrentSalary: {
            ...(currentOriginalValue.permittedFields?.typeOfCurrentSalary ||
              {}),
            value: formatValueCurrentSalary?.typeOfCurrentSalary
          }
        }
        break

      case 'expectedSalary':
        const formatValueExpectedSalary = value as {
          expectedSalary: number
          expectedSalaryCurrency: string
          typeOfExpectedSalary: string
        }
        newValue = {
          ...currentOriginalValue.permittedFields,
          expectedSalary: {
            ...(currentOriginalValue.permittedFields?.expectedSalary || {}),
            value: formatValueExpectedSalary?.expectedSalary
          },
          expectedSalaryCurrency: {
            ...(currentOriginalValue.permittedFields?.expectedSalaryCurrency ||
              {}),
            value: formatValueExpectedSalary?.expectedSalaryCurrency
          },
          typeOfExpectedSalary: {
            ...(currentOriginalValue.permittedFields?.typeOfExpectedSalary ||
              {}),
            value: formatValueExpectedSalary?.typeOfExpectedSalary
          }
        }
        break

      case 'locationWithStateID':
        const formatValueDynamic = value as ISelectOption
        newValue = {
          ...currentOriginalValue.permittedFields,
          location: {
            ...(currentOriginalValue.permittedFields?.location || {}),
            value: formatValueDynamic.value
          }
        }
        break

      case 'languages':
        const formatValueLanguages = value as ISelectOption[]
        newValue = {
          ...currentOriginalValue.permittedFields,
          languages: {
            ...(currentOriginalValue.permittedFields?.languages || {}),
            value: formatValueLanguages?.map((item: ISelectOption) => ({
              language: item.value,
              languageDescription: item?.supportingObj?.name,
              proficiency: item.extras?.proficiency,
              proficiencyDescription: item.extras?.proficiencyDescription
            }))
          }
        }
        break

      case 'birthday':
        const formatValueBirthday = value as {
          birth_year?: string
          year?: string
          birth_month?: string
          month?: string
          birth_date?: string
          date?: string
        }
        newValue = {
          ...currentOriginalValue.permittedFields,
          birthday: {
            ...(currentOriginalValue.permittedFields?.birthday || {}),
            value: {
              birth_year:
                formatValueBirthday?.birth_year || formatValueBirthday?.year,
              birth_month:
                formatValueBirthday?.birth_month ||
                formatValueBirthday?.month ||
                null,
              birth_date:
                formatValueBirthday?.birth_date ||
                formatValueBirthday?.date ||
                null
            }
          }
        }
        break

      default:
        newValue = {
          ...currentOriginalValue.permittedFields,
          [currentField]: {
            ...(currentOriginalValue.permittedFields?.[
              currentField as keyof unknown
            ] || {}),
            value
          }
        }
        break
    }
  }

  if (currentField === 'customFields') {
    newValue = formatSubmitCustomFieldData(value as keyof object)
  }

  return { fieldUpdate, newValue }
}

export const mappingDataUpdateProfile = (
  param: CandidateProfileInputType & {
    paramType: string
    profileType?: string
  }
) => {
  const fileObj = (param.avatarObject || '') as BlobPart
  let formatParams = {
    ...param,
    id: Number(param.id),
    ...(param.paramType === 'languages' &&
    param.profileType === 'candidateEditingTable'
      ? {
          languages: param.languages?.map((item, index) => {
            const formatValue = item as ISelectOption
            return {
              index,
              language: formatValue.value,
              languageDescription: formatValue.supportingObj?.name,
              proficiency: formatValue.extras?.proficiency,
              proficiencyDescription: formatValue.extras?.proficiencyDescription
            }
          })
        }
      : {}),
    ...(param.paramType === 'currentSalary' &&
    param.profileType === 'candidateEditingTable'
      ? {
          ...(param.currentSalary as unknown as object)
        }
      : {}),
    ...(param.paramType === 'expectedSalary' &&
    param.profileType === 'candidateEditingTable'
      ? {
          ...(param.expectedSalary as unknown as object)
        }
      : {}),
    ...(param.paramType === 'ownerId'
      ? {
          ownerId:
            param.profileType === 'candidateEditingTable'
              ? Number(param.ownerId?.value)
              : param.ownerId || null
        }
      : {}),
    ...(param.paramType === 'locationWithStateID'
      ? {
          location: param?.locationWithStateID?.value || null,
          countryStateId: Number(param?.locationWithStateID?.id),
          locationWithStateID: undefined
        }
      : {}),
    ...(param.avatarObject && typeof param.avatarObject === 'object'
      ? {
          avatar: new File([new Blob([fileObj])], `avatar-${uuidV4()}.jpeg`)
        }
      : {}),
    ...(param.resumeFile && typeof param.resumeFile === 'object'
      ? {
          resumeFile: param.resumeFile[0]
        }
      : {}),
    ...(param.profileTalentPoolIds &&
    typeof param.profileTalentPoolIds === 'object'
      ? {
          profileTalentPoolIds: param.profileTalentPoolIds.map((item) => {
            const formatValue =
              typeof item === 'object' ? (item as ISelectOption).id : item
            return Number(formatValue)
          })
        }
      : {}),

    ...(param.preferredWorkStateIds &&
    typeof param.preferredWorkStateIds === 'object'
      ? {
          preferredWorkStateIds: param.preferredWorkStateIds.map((item) => {
            const formatValue =
              typeof item === 'object' ? (item as ISelectOption).id : item
            return Number(formatValue)
          })
        }
      : {}),
    ...(param.paramType === 'customFields'
      ? {
          customFields: formatSubmitCustomFieldData(param.customFields || {})
        }
      : {}),
    ...(param.paramType === 'birthday'
      ? {
          birthday: {
            birth_year: param.birthday?.year,
            birth_month: param.birthday?.month || null,
            birth_date: param.birthday?.date || null
          }
        }
      : {}),
    paramType: undefined,
    profileType: undefined,
    row: undefined
  }

  delete formatParams.paramType
  delete formatParams.profileType
  delete formatParams.row

  return formatParams
}
