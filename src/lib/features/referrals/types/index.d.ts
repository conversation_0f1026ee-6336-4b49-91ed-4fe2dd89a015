import { ILogoAndAvatarVariants } from '~/core/@types/global'
import { ISelectOption } from '~/core/ui/Select'
import {
  CompanyDetailResponseType,
  CompanyJobResponseType
} from '../../agency/companies/types/company-detail'
import { IJobStage } from '../../jobs/types'
import { CustomFieldResponseItem } from '../../settings/profile-fields/types/custom-field'
import { ICompanyPermittedFields } from '../../agency/companies/types'

export type JobDetailType = {
  id: number
  title: string
  slug: string
  createdAt: string
  referralsCount: number
  jobReferable?: boolean
  savedReferralJobMemberIds?: number[]
  jobLocations?: Array<{
    state?: string
    country?: string
  }>
  department?: {
    name: string
  }
  tenant: {
    name: string
    logoVariants?: ILogoAndAvatarVariants
    slug: string
    description: string
    careerSiteSettings: {
      canonical_url: string
      description: string
      logoVariants?: ILogoAndAvatarVariants
      page_title: string
      department_visibility?: string
    }
  }
  status?: string
  enablingReward: boolean
  referralRewardType: string
  rewardAmount: number
  rewardCurrency: string
  rewardGift: string
  publicReferralUri?: string
  recommendationMatchedFields?: Array<MatchedFieldTotal | MatchedField>
  permittedFields?: {
    [key: string]: {
      role_changeable?: boolean
      visibility_changeable?: boolean
      roles?: Array<string>
      value?: string & { name?: string; id?: string }
    }
  }
  company?: {
    id: string
    permittedFields?: ICompanyPermittedFields
  }
  currentUserAppliedAt: string
  tags?: Array<{
    id: string
    name?: string
  }>
  skills: string[]
  company?: CompanyDetailResponseType
}

export type OpenJobsParams = {
  limit?: number
  page?: number
  search?: string
  locationIds?: Array<number>
  departmentId?: number | string
  talentPoolIds?: Array<number>
  jobsKey?: string
  jobLevel?: string
}

export type IOpenJobsManagementFilter = {
  search?: string
  countryStateIds?: ISelectOption[]
  departmentIds?: ISelectOption[]
  talentPoolIds?: ISelectOption[]
  jobsKey?: ISelectOption
  jobLevel?: ISelectOption
  operator?: string
  tagIds?: ISelectOption[]
  isFilterTouched?: boolean
  remoteStatus?: ISelectOption
  recommendWeight?: IRecommendationSetting
  isGetRecommendation?: boolean
  [key: string]: any
}

export type ReferralFormType = {
  fullName?: string
  email?: string
  phoneNumber?: string
  resumeFile?: Array<File>
  introduction?: string
  jobId?: Array<ISelectOption>
}

export type JobApplicableType = {
  id: number
  title: string
  jobLocations: Array<{
    state?: string
    country?: string
  }>
  department?: {
    name: string
  }
}

export type QueryJobApplicableParam = {
  page?: number
  limit?: number
}

export type MyReferralType = {
  id: number
  introduction: string
  profile: {
    id: number
    fullName: string
    email: string
    avatarVariants?: ILogoAndAvatarVariants
  }
  job: {
    id: number
    title: string
    slug: string
    tenant: {
      slug: string
    }
  }
  status: string
  applicant: {
    jobStage: IJobStage
    currentStagedDate: string
  }
}

export type EmployeeProfileType = {
  id: string
  fullName: string
  email: Array<string>
  headline?: string
  phoneNumber: Array<string>
  address?: string
  coverLetter?: string
  countryStateId?: string
  links?: {
    [key]: Array<string>
  }
  sourced?: string
  sourcedDescription?: string
  sourcedName?: string
  sourcedNameDescription?: string
  profileCvs: Array<{
    id: string
    attachments: Array<{
      id: string
      file: string
      blobs: {
        size: number
        filename: string
        content_type: string
      }
    }>
  }>
  totalYearsOfExp?: string
  permittedFields?: {
    [key: string]: {
      value: string
      roles: Array<string>
      role_changeable: boolean
      visibility_changeable: boolean
    }
    birthday: {
      value: {
        birth_date?: string
        birth_month?: string
        birth_year?: string
      }
      roles: Array<string>
      role_changeable: boolean
      visibility_changeable: boolean
    }
    languages: {
      value: Array<{
        index: number
        language: string
        languageDescription: string
        proficiency: string
        proficiencyDescription: string
      }>
      roles: Array<string>
      role_changeable: boolean
      visibility_changeable: boolean
    }
  }
  tags?: Array<{
    value?: string
    name: string
    id: string
  }>
  customFields?: Array<CustomFieldResponseItem>
}
