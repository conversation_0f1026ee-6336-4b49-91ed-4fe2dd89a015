import { gql } from 'urql'

const QueryInternalJobsShow = gql`
  query ($id: Int!) {
    internalJobsShow(id: $id) {
      id
      title
      pitch
      description
      status
      slug
      jobReferable
      jobLocations {
        state
        city
        country
        name
        address
        zipCode
      }
      industries {
        name
      }
      tenant {
        name
        logoVariants
        slug
        description
        careerSiteSettings
      }
      preferences
      department {
        name
        parent {
          name
        }
      }
      createdAt
      jobCategory {
        name
      }
      educationDescription
      skills
      enablingReward
      referralRewardType
      rewardAmount
      rewardCurrency
      rewardGift
      permittedFields
      publicReferralUri
      recommendationMatchedFields
      currentUserAppliedAt
      referenceId
      tags {
        id
        name
      }
      customFields
    }
  }
`

export default QueryInternalJobsShow
