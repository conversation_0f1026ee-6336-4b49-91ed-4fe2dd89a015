import { UseQueryExecute } from 'urql'
import { boolean } from 'zod'
import { IEmailForm } from '~/components/SendMailFormControl/EmailContentEditor'
import { ILogoAndAvatarVariants } from '~/core/@types/global'
import { ISelectOption } from '~/core/ui/Select'
import { IJobInfoType, IJobStage, IJobStages } from '../../jobs/types'
import {
  IIkitSession,
  IJobInterviewKit
} from '../../settings/interview-kits/types'
import { TaskItemType } from '../../tasks/types'
import { CompanyDetailResponseType } from '../../agency/companies/types/company-detail'

export interface AssigneeType {
  id: number
  email: string
  fullName: string
  avatarVariants?: ILogoAndAvatarVariants
  defaultColour: string
  roles: Array<{
    id: number
    name: string
  }>
}

export interface InterviewType {
  id: number
  fromDatetime: string
  toDatetime: string
  profile: {
    fullName: string
  }
  feedback: {
    id: number
  }
  ikitFeedbacksSummary?: Array<{}>
}

export interface InterviewListResponseType {
  interviewsList: {
    collection: Array<InterviewType>
    metadata: {
      totalCount: number
    }
  }
}
export interface TaskListResponseType {
  tasksList: {
    collection: Array<TaskItemType>
    metadata: {
      totalCount: number
    }
  }
}

export interface InterviewCandidateListResponseType {
  interviewsList: {
    collection: Array<InterviewDetailType>
    metadata: {
      totalCount: number
    }
  }
}

export interface IInterviewsManagementFilter {
  fromDatetime?: string
  toDatetime?: string
  attendeeIds?: Array<ISelectOption>
  filterBy?: string
}

export interface InterviewParamsType {
  id?: number
  applicantId?: number
  fromDatetime?: string
  toDatetime?: string
  timezone?: string
  eventType?: string
  meetingUrl?: string | null
  // sendEmail?: boolean
  sendEmailToCandidate?: boolean
  sendEmailToAttendees?: boolean
  locationId?: number | null
  emailTemplateId?: number
  attendeeIds?: Array<number>
  subject?: string
  cc?: Array<string>
  bcc?: Array<string>
  htmlBody?: string
  attachments?: Array<File>
  profileId?: number
  jobId?: number

  eventTypeDescription?: string
  attendees?: Array<{
    id?: number
    fullName?: string
    avatarVariants?: ILogoAndAvatarVariants
    roles?: string
    email?: string
  }>
  locationName?: string
  jobIkitId?: number
  jobStageId?: number
  remindSchedule?: boolean
  remindFeedback?: boolean
  timeSlots?: Array<{
    fromDatetime?: string
    toDatetime?: string
    timezone?: string
  }>
  jobStage?: {
    id?: string
    stageLabel?: string
    stageGroup?: string
  }
  remindBeforeTime?: string
  remindAfterTime?: string
}

export type InterviewFormStep2 = {
  sendEmailToCandidate?: boolean
  sendEmailToAttendees?: boolean
  to?: Array<ISelectOption>
  toAttendees?: Array<ISelectOption>
  ccAttendees?: Array<ISelectOption>
  bccAttendees?: Array<ISelectOption>
  //editor mail candidate
  htmlBodyCandidate?: string
  subjectCandidate?: string
  //editor mail attendee
  htmlBodyAttendees?: string
  subjectAttendees?: string
  emailTemplate?: ISelectOption
}

export type InterviewStep2EmailEditor = IEmailForm & {
  remindSchedule?: boolean
  sendEmailToCandidate?: boolean
  sendEmailToAttendees?: boolean
  //editor mail candidate
  htmlBodyCandidate?: string
  subjectCandidate?: string
  //editor mail attendee
  htmlBodyAttendees?: string
  subjectAttendees?: string
  toAttendees?: Array<ISelectOption>
  ccAttendees?: Array<ISelectOption>
  bccAttendees?: Array<ISelectOption>
}

export interface FilterControlType {
  value?: IInterviewsManagementFilter
  onChange: Dispatch<SetStateAction<IInterviewsManagementFilter>>
}

export interface InterviewsListControlType {
  data?: InterviewListResponseType
  isLoading: boolean
  error: {
    parseErrors: unknown
    statusCode: number
  }
  refetch: any // ((previousTypeAction?: 'delete' | 'edit') => void) | UseQueryExecute
}
export interface TasksListControlType {
  data?:
    | InfiniteData<{
        TaskListResponseType
      }>
    | undefined
  isLoading: boolean
  error?: {
    parseErrors: unknown
    statusCode: number
  }
  refetch: any // ((previousTypeAction?: 'delete' | 'edit') => void) | UseQueryExecute
}

export interface IApplicableJobs {
  id: string
  jobId: string
  title?: string
  profileId: number
  status: string
  statusDescription: string
  rejectedReasonLabel: string
  createdAt: string
  jobLocations: Array<{
    state: string
    country: string
  }>
  company?: CompanyDetailResponseType
  job: {
    id: string
    slug: string
    title: string
    status: string
    statusDescription: string
    jobStages: IJobStages
    owner?: {
      email?: string
      defaultColour?: string
      avatarVariants?: ILogoAndAvatarVariants
      fullName?: string
    }
    department?: {
      name: string
    }
    jobLocations: {
      state: string
      country: string
    }[]
    permittedFields?: {
      [key: string]: {
        role_changeable?: boolean
        visibility_changeable?: boolean
        roles?: Array<string>
        value?: string & { name?: string; id?: string }
      }
    }
    currentUserAccessible?: boolean
    company?: CompanyDetailResponseType
  }
  createdBy: {
    email: string
    fullName: string
    avatarVariants?: ILogoAndAvatarVariants
    defaultColour?: string
  }
  incoming: string
  jobStage: IJobStage
  owner?: {
    id: number
    fullName: string
    email: string
  }
  department?: {
    name: string
  }
  coverLetter?: string
  pendingInterviewFeedbacks?: InterviewParamsType[]
  overallFeedbacksCount: string
}

interface InterViewDetailDefaultProps {
  id?: string
  eventTypeDescription?: string
  eventType?: string
  fromDatetime?: string
  toDatetime?: string
  timezone?: string
  meetingUrl?: string
  sendEmail?: boolean
  previewLink?: string
  emailTemplate?: {
    id: number | string
    name: string
    subject: string
    body: string
    emailKind: string
  }
  location?: {
    id: number
    address: string
    country: string
    state: string
    city: string
  }
  profile?: ICandidateProfile
  organizer?: {
    id: number
    avatarVariants?: ILogoAndAvatarVariants
    fullName: string
    defaultColour?: string
    email?: string
  }
  attendees?: Array<{
    id?: number
    avatarVariants?: ILogoAndAvatarVariants
    fullName?: string
    email?: Array<string>
    defaultColour?: string
    availableForSchedule?: boolean
    roles?: Array<{
      id: number
      name: string
    }>
  }>
  job?: {
    id: number
    title: string
    pitch: string
    description: string
    tags?: Array<{
      name: string
    }>
  } & IJobInfoType
  applicant?: IApplicableJobs
  ikitFeedbacksSummary?: Array<Array<string | number>>
  currentUserFeedback?: {
    id: number
    overallFeedback?: string
    status?: string
  }
  method?: string
  duration?: string

  state?: string
  stateDescription?: string
  interviewTimeSlots?: Array<{
    fromDatetime: string
    toDatetime: string
    picked?: boolean
    timezone: string
  }>
  sendEmailToCandidate?: boolean
  sendEmailToAttendees?: boolean
  remindBeforeTime?: string
  remindAfterTime?: string
}

export interface InterviewDetailType extends InterViewDetailDefaultProps {
  jobStage?: {
    id?: string
    stageLabel?: string
  }
  jobIkit?: IJobInterviewKit
  remindSchedule?: boolean
  remindFeedback?: boolean

  jobIkitId?: ISelectOption
  jobStageId?: number | ISelectOption
}

export interface InterviewFeedbackDetailProps
  extends InterViewDetailDefaultProps {
  ikitFeedbacksSummary?: Array<Array<string | number>>
  jobIkit?: IJobInterviewKit
  jobStage?: {
    id?: string
    stageLabel?: string
    stageTypeId?: string
  }
  remindSchedule?: boolean
  remindFeedback?: boolean
  currentUserFeedback?: IJobInterviewKit
  userFeedbacks?: {
    ikitSessions?: Array<
      IIkitSession & {
        users?: Array<{
          avatar?: string
          fullName?: string
          id?: number
          defaultColour: string
        }>
      }
    >
    overallFeedbacks?: Array<{
      index?: number
      note?: string
      overallFeedback?: string
      updatedAt: string
      user?: {
        avatar?: string
        fullName?: string
        id?: number
        defaultColour: string
      }
    }>
  }
  pendingFeedbackUsers?: Array<{
    id: string
    email?: string
    fullName?: string
  }>
}

export interface ActionInterviewManagement {
  interviewUpdateAction: {
    updateInterview: (
      args: InterviewParamsType
    ) => Promise<OperationResult<any, AnyVariables>>
  }
}
export interface ActionTaskManagement {
  updateTaskAction: {
    updateTask: (args: {
      id: number
      title?: string
      profileId?: number
      dueDate?: string
      assigneeIds?: Array<number>
    }) => Promise<OperationResult<any, AnyVariables>>
  }
}
export interface FirstStepFormType {
  date?: Date
  startTime?: ISelectOption
  endTime?: ISelectOption
  timezone?: ISelectOption
  jobId?: ISelectOption
  attendees?: Array<ISelectOption>
  locationId?: ISelectOption
  meetingUrl?: string
  eventType?: ISelectOption
  eventTypeDescription?: string
  jobIkitId?: ISelectOption
  jobStageId?: ISelectOption
  remindSchedule?: boolean
  remindFeedback?: boolean
  method?: string
  timeSlots?: Array<{
    fromDatetime?: string
    toDatetime?: string
    timezone?: string
  }>
  candidateReminderTime?: ISelectOption
  feedbackReminderTime?: ISelectOption
  remindBeforeTime?: ISelectOption
  remindAfterTime?: ISelectOption
}

export interface CalendarViewFormType {
  date?: Date
  startTime?: ISelectOption
  endTime?: ISelectOption
  attendees?: Array<ISelectOption>
  duration?: ISelectOption
  timezone?: ISelectOption
}

export interface IEmailParamsFormType {
  id?: number
  subject?: string
  to?: Array<ISelectOption>
  cc?: Array<{ value: string }>
  bcc?: Array<{ value: string }>
  htmlBody?: string
  attachments?: Array<File>
  profileId?: number
  jobId?: Array<{ value: string }>
  emailTemplate?: {
    value: number
  }
}

export interface IEmailParamsSendType {
  id?: number
  subject?: string
  to?: Array<ISelectOption>
  cc?: Array<string>
  bcc?: Array<string>
  htmlBody?: string
  attachments?: Array<File>
  profileId?: number
  jobId?: number
  emailTemplate?: {
    value: number
  }
  emailTemplateId?: number | string
}

export interface IEmailCandidate {
  id?: number
  payload?: {
    bcc?: Array<string>
    cc?: Array<string>
    reply_to?: string
    subject?: string
    to?: string
  }
  body?: string
  attachments?: Array<{ id?: number }>
  createdAt?: string
  createdBy?: {
    avatarVariants?: ILogoAndAvatarVariants
    fullName?: string
    email?: string
    defaultColour?: string
  }
}

export interface IEmailCandidateListResponseType {
  profileEmailsList: {
    collection: Array<IEmailCandidate>
    metadata: {
      totalCount: number
    }
  }
}

export interface IEmailCandidateApplicantListResponseType {
  applicantEmailsList: {
    collection: Array<IEmailCandidate>
    metadata: {
      totalCount: number
    }
  }
}

export type DateTimeMethodType = 'set_specific_time' | 'candidate_self_schedule'

export type AvailableMember = {
  id: number
  email: string
  fullName: string
  avatarVariants: ILogoAndAvatarVariants
  defaultColour: string
  roles?: Array<{
    id: number
    name: string
  }>
  availableForSchedule: boolean
  jobHiringMember?: boolean
  userTenants?: { userKind: string }[]
}

export type AttendeeType = {
  id: number
  email: string
  fullName: string
  defaultColour: string
  avatarVariants: ILogoAndAvatarVariants
}

export type AttendeeEvent = {
  id: number
  fromDatetime: string
  toDatetime: string
  timezone: string
  eventAttendees: Array<AttendeeType>
}

export type FeedbackList = {}
