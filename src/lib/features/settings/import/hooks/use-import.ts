import { useRouter } from 'next/router'
import { useCallback, useState, useMemo } from 'react'
import { useTranslation } from 'react-i18next'
import configuration from '~/configuration'
import { IParamsTableInfinity } from '~/core/@types/global'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import useContextGraphQL, {
  IResponseContextResult
} from '~/core/middleware/use-context-graphQL'
import { IPromiseSearchOption } from '~/core/ui/Select'
import { catchErrorFromGraphQL } from '~/core/utilities/catch-api-error'

// Job
import MutationImportJobs from '~/lib/features/settings/import/graphql/mutation-import-jobs'
import MutationJobImportingValidate from '~/lib/features/settings/import/graphql/mutation-jobs-importing-validation'
import QueryJobFields from '~/lib/features/settings/import/graphql/query-job-fields'
import QueryJobImportFilesList from '~/lib/features/settings/import/graphql/query-job-import-files-list'
import QueryImportedJobHistory from '~/lib/features/settings/import/graphql/query-import-file-history'
import QueryJobImportShow from '~/lib/features/settings/import/graphql/query-job-import-show'

// Candidate
import MutationImportProfiles from '~/lib/features/settings/import/graphql/mutation-import-profiles'
import MutationCandidatesImportingValidate from '~/lib/features/settings/import/graphql/mutation-candidates-importing-validation'
import QueryProfileFields from '~/lib/features/settings/import/graphql/query-profile-fields'
import QueryProfileImportFilesList from '~/lib/features/settings/import/graphql/query-profile-import-files-list'
import QueryImportedProfileHistory from '~/lib/features/settings/import/graphql/query-import-file-history-profiles'
import QueryProfileImportShow from '~/lib/features/settings/import/graphql/query-profile-import-show'

// Course
import MutationImportTenantCourses from '~/lib/features/settings/import/graphql/mutation-import-tenant-courses'
import MutationTenantCoursesImportingValidate from '~/lib/features/settings/import/graphql/mutation-tenant-courses-importing-validation'
import QueryTenantCourseFields from '~/lib/features/settings/import/graphql/query-tenant-course-fields'
import QueryTenantCourseImportFilesList from '~/lib/features/settings/import/graphql/query-tenant-course-import-files-list'
import QueryImportedTenantCourseHistory from '~/lib/features/settings/import/graphql/query-import-file-history-tenant-courses'
import QueryTenantCourseImportShow from '~/lib/features/settings/import/graphql/query-tenant-course-import-show'

// Company
import MutationImportCompany from '~/lib/features/settings/import/graphql/mutation-company-import'
import MutationCompanyImportingValidate from '~/lib/features/settings/import/graphql/mutation-company-importing-validation'
import QueryCompanyFields from '~/lib/features/settings/import/graphql/query-company-fields'
import QueryCompanyImportFilesList from '~/lib/features/settings/import/graphql/query-company-import-list'
import QueryImportedCompanyHistory from '~/lib/features/settings/import/graphql/query-company-import-history'
import QueryCompanyImportShow from '~/lib/features/settings/import/graphql/query-company-import-show'

import {
  ImportFileCoursesHistoriesType,
  ImportFileHistoriesType,
  ImportFileProfilesHistoriesType
} from '~/lib/features/settings/import/types'
import { IPageResult } from '~/lib/hooks/use-infinity-query-search'
import { useSubmitCommon } from '~/lib/hooks/use-submit-graphql-common'
import useBoundStore from '~/lib/store'
import { PartialFieldType, MappedFieldType, ImportFileType } from '../types'
import useLoadingBlock from '~/lib/store/loading-block'
import useToastStore from '~/lib/store/toast'
import { ImportTypeKey } from '~/lib/features/settings/import/types'

const useMappingJobFields = ({
  mappedFields,
  file
}: {
  mappedFields?: MappedFieldType[] | undefined
  file?: File
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlock()
  const { clientGraphQL } = useContextGraphQL()
  const clientRequest = useGraphQLRequest({ language: user?.language })

  const { trigger: importJobs, isLoading: isLoadingImport } =
    useSubmitCommon(MutationImportJobs)
  const { trigger: validateImport, isLoading: isLoadingValidate } =
    useSubmitCommon(MutationJobImportingValidate)

  const promiseJobFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryJobFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<PartialFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { jobFieldsList } = result.data
          const collection = jobFieldsList?.collection || []
          const metadata = jobFieldsList?.metadata || {}

          const cloneData = collection.map((item) => {
            return {
              value: item.uuid,
              supportingObj: {
                name: item.name,
                nameRequired: item.required
              }
            }
          })

          return {
            metadata,
            collection: cloneData
          }
        }),
    [mappedFields]
  )

  const validateImportJob = useCallback(
    async (
      file: File,
      finishCallback?: (data: {
        mappedFields?: MappedFieldType[]
        file?: File
      }) => Promise<any>
    ) => {
      const result = await validateImport({ file })

      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast,
          page: configuration.path.settings.import,
          router
        })
      }

      const mappedFields = result.data?.jobsImportingValidate?.mappedFields

      return finishCallback
        ? finishCallback({ mappedFields, file })
        : Promise.resolve()
    },
    [validateImport, setToast, router]
  )

  const onImportJobs = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setShowLockApp('')
      return importJobs({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          setCloseLockApp()
          const errorMessage = result.error.graphQLErrors?.[0]?.message || ''
          if (errorMessage.includes('duplicated'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:duplicated_title')}`,
              description: errorMessage
            })
          else if (errorMessage.includes('required fields'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:import_mapping_require_title')}`,
              description: errorMessage
            })
          else
            catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router
            })
          return Promise.resolve(false)
        }

        if (result.data.jobsImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?import_id=${result.data.jobsImport?.import.id}&object_kind=job`
          )
        }

        return Promise.resolve(true)
      })
    },
    [file]
  )

  const getImportJobsList = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientRequest
        .query(QueryJobImportFilesList, {
          page,
          limit: configuration.defaultPageSize
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection = result?.data?.jobImportsList?.collection || []
          const metadata = result?.data?.jobImportsList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    []
  )

  const getImportedJobsHistory = async (
    pageParam = {} as IParamsTableInfinity
  ): Promise<IPageResult<ImportFileHistoriesType>> => {
    return clientGraphQL
      .query(QueryImportedJobHistory, {
        ...pageParam,
        limit: configuration.defaultPageSize,
        importId: Number(router.query['import_id'])
      })
      .toPromise()
      .then((result: IResponseContextResult<ImportFileHistoriesType>) => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.departments,
            setToast
          })

          return {
            data: [],
            meta: {
              totalRowCount: 0,
              pageSize: configuration.defaultPageSize
            }
          }
        }

        const { jobImportHitoriesList } = result.data
        const collection = (jobImportHitoriesList?.collection || []).map(
          (item) => ({ ...item, id: item.data.key })
        )
        const metadata = jobImportHitoriesList?.metadata || {}

        return {
          data: collection,
          meta: {
            totalRowCount: metadata.totalCount,
            pageSize: configuration.defaultPageSize
          }
        }
      })
  }

  const getImportedJobsShow = async () => {
    return clientGraphQL
      .query(QueryJobImportShow, {
        id: Number(router.query['import_id'])
      })
      .toPromise()
      .then(
        (
          result: IResponseContextResult<{
            jobImportsShow: Partial<ImportFileType>
          }>
        ) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.settings.import,
              setToast
            })

            return undefined
          }

          return result.data?.jobImportsShow
        }
      )
  }

  return {
    isLoadingImport,
    isLoadingValidate,
    promiseJobFields,
    validateImportJob,
    onImportJobs,
    getImportJobsList,
    getImportedJobsHistory,
    getImportedJobsShow
  }
}

const useMappingCandidateFields = ({
  mappedFields,
  file
}: {
  mappedFields?: MappedFieldType[] | undefined
  file?: File
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlock()
  const { clientGraphQL } = useContextGraphQL()
  const clientRequest = useGraphQLRequest({ language: user?.language })

  const { trigger: importCandidates, isLoading: isLoadingImport } =
    useSubmitCommon(MutationImportProfiles)
  const { trigger: validateImport, isLoading: isLoadingValidate } =
    useSubmitCommon(MutationCandidatesImportingValidate)

  const promiseProfileFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryProfileFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<PartialFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { profileFieldsList } = result.data
          const collection = profileFieldsList?.collection || []
          const metadata = profileFieldsList?.metadata || {}

          const cloneData = collection.map((item) => {
            return {
              value: item.uuid,
              supportingObj: {
                name: item.name,
                nameRequired: item.required
              }
            }
          })

          return {
            metadata,
            collection: cloneData
          }
        }),
    [mappedFields]
  )

  const validateImportCandidate = useCallback(
    async (
      file: File,
      finishCallback?: (data: {
        mappedFields?: MappedFieldType[]
        file?: File
      }) => Promise<any>
    ) => {
      const result = await validateImport({ file })

      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast,
          page: configuration.path.settings.import,
          router
        })
      }

      const mappedFields = result.data?.profilesImportingValidate?.mappedFields

      return finishCallback
        ? finishCallback({ mappedFields, file })
        : Promise.resolve()
    },
    [validateImport, setToast, router]
  )

  const onImportCandidates = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setShowLockApp('')
      return importCandidates({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          setCloseLockApp()
          const errorMessage = result.error.graphQLErrors?.[0]?.message || ''
          if (errorMessage.includes('duplicated'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:duplicated_title')}`,
              description: errorMessage
            })
          else if (errorMessage.includes('required fields'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:import_mapping_require_title')}`,
              description: errorMessage
            })
          else
            catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router
            })
          return Promise.resolve(false)
        }

        if (result.data.profilesImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?import_id=${result.data.profilesImport?.import.id}&object_kind=profile`
          )
        }

        return Promise.resolve(true)
      })
    },
    [file]
  )

  const getImportProfilesList = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientRequest
        .query(QueryProfileImportFilesList, {
          page,
          limit: configuration.defaultPageSize
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection = result?.data?.profileImportsList?.collection || []
          const metadata = result?.data?.profileImportsList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    []
  )

  const getImportedProfilesHistory = async (
    pageParam = {} as IParamsTableInfinity
  ): Promise<IPageResult<ImportFileProfilesHistoriesType>> => {
    return clientGraphQL
      .query(QueryImportedProfileHistory, {
        ...pageParam,
        limit: configuration.defaultPageSize,
        importId: Number(router.query['import_id'])
      })
      .toPromise()
      .then(
        (result: IResponseContextResult<ImportFileProfilesHistoriesType>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.settings.departments,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const { profileImportHitoriesList } = result.data
          const collection = (profileImportHitoriesList?.collection || []).map(
            (item) => ({ ...item, id: item.data.key })
          )
          const metadata = profileImportHitoriesList?.metadata || {}

          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        }
      )
  }

  const getImportedProfilesShow = async () => {
    return clientGraphQL
      .query(QueryProfileImportShow, {
        id: Number(router.query['import_id'])
      })
      .toPromise()
      .then(
        (
          result: IResponseContextResult<{
            profileImportsShow: Partial<ImportFileType>
          }>
        ) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.settings.import,
              setToast
            })

            return undefined
          }

          return result.data?.profileImportsShow
        }
      )
  }

  return {
    isLoadingImport,
    isLoadingValidate,
    validateImportCandidate,
    promiseProfileFields,
    onImportCandidates,
    getImportProfilesList,
    getImportedProfilesHistory,
    getImportedProfilesShow
  }
}

const useMappingCourseFields = ({
  mappedFields,
  file
}: {
  mappedFields?: MappedFieldType[] | undefined
  file?: File
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlock()
  const { clientGraphQL } = useContextGraphQL()
  const clientRequest = useGraphQLRequest({ language: user?.language })

  const { trigger: importCourses, isLoading: isLoadingImport } =
    useSubmitCommon(MutationImportTenantCourses)
  const { trigger: validateImport, isLoading: isLoadingValidate } =
    useSubmitCommon(MutationTenantCoursesImportingValidate)

  const promiseCourseFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryTenantCourseFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<PartialFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { tenantCourseFieldsList } = result.data
          const collection = tenantCourseFieldsList?.collection || []
          const metadata = tenantCourseFieldsList?.metadata || {}

          const cloneData = collection.map((item) => {
            return {
              value: item.uuid,
              supportingObj: {
                name: item.name,
                nameRequired: item.required
              }
            }
          })

          return {
            metadata,
            collection: cloneData
          }
        }),
    [mappedFields]
  )

  const validateImportCourse = useCallback(
    async (
      file: File,
      finishCallback?: (data: {
        mappedFields?: MappedFieldType[]
        file?: File
      }) => Promise<any>
    ) => {
      const result = await validateImport({ file })

      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast,
          page: configuration.path.settings.import,
          router
        })
      }

      const mappedFields =
        result.data?.tenantCoursesImportingValidate?.mappedFields

      return finishCallback
        ? finishCallback({ mappedFields, file })
        : Promise.resolve()
    },
    [validateImport, setToast, router]
  )

  const onImportCourse = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setShowLockApp('')
      return importCourses({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          setCloseLockApp()
          const errorMessage = result.error.graphQLErrors?.[0]?.message || ''
          if (errorMessage.includes('duplicated'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:duplicated_title')}`,
              description: errorMessage
            })
          else if (errorMessage.includes('required fields'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:import_mapping_require_title')}`,
              description: errorMessage
            })
          else
            catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router
            })
          return Promise.resolve(false)
        }

        if (result.data.tenantCoursesImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?import_id=${result.data.tenantCoursesImport?.import.id}&object_kind=tenant_course`
          )
        }

        return Promise.resolve(true)
      })
    },
    [file]
  )

  const getImportTenantCoursesList = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientRequest
        .query(QueryTenantCourseImportFilesList, {
          page,
          limit: configuration.defaultPageSize
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection =
            result?.data?.tenantCourseImportsList?.collection || []
          const metadata = result?.data?.tenantCourseImportsList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    []
  )

  const getImportedTenantCoursesHistory = async (
    pageParam = {} as IParamsTableInfinity
  ): Promise<IPageResult<ImportFileCoursesHistoriesType>> => {
    return clientGraphQL
      .query(QueryImportedTenantCourseHistory, {
        ...pageParam,
        limit: configuration.defaultPageSize,
        importId: Number(router.query['import_id'])
      })
      .toPromise()
      .then(
        (result: IResponseContextResult<ImportFileCoursesHistoriesType>) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.settings.departments,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const { tenantCourseImportHistoriesList } = result.data
          const collection = (
            tenantCourseImportHistoriesList?.collection || []
          ).map((item) => ({ ...item, id: item.data.key }))
          const metadata = tenantCourseImportHistoriesList?.metadata || {}

          return {
            data: collection,
            meta: {
              totalRowCount: metadata.totalCount,
              pageSize: configuration.defaultPageSize
            }
          }
        }
      )
  }

  const getImportedTenantCoursesShow = async () => {
    return clientGraphQL
      .query(QueryTenantCourseImportShow, {
        id: Number(router.query['import_id'])
      })
      .toPromise()
      .then(
        (
          result: IResponseContextResult<{
            tenantCourseImportsShow: Partial<ImportFileType>
          }>
        ) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.settings.import,
              setToast
            })

            return undefined
          }

          return result.data?.tenantCourseImportsShow
        }
      )
  }

  return {
    isLoadingImport,
    isLoadingValidate,
    validateImportCourse,
    promiseCourseFields,
    onImportCourse,
    getImportTenantCoursesList,
    getImportedTenantCoursesHistory,
    getImportedTenantCoursesShow
  }
}

const useMappingCompanyFields = ({
  mappedFields,
  file
}: {
  mappedFields?: MappedFieldType[] | undefined
  file?: File
}) => {
  const { t } = useTranslation()
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const { setShowLockApp, setCloseLockApp } = useLoadingBlock()
  const { clientGraphQL } = useContextGraphQL()
  const clientRequest = useGraphQLRequest({ language: user?.language })

  const { trigger: importCompany, isLoading: isLoadingImport } =
    useSubmitCommon(MutationImportCompany)
  const { trigger: validateImport, isLoading: isLoadingValidate } =
    useSubmitCommon(MutationCompanyImportingValidate)

  const promiseCompanyFields = useCallback(
    (params = {} as IPromiseSearchOption) =>
      clientGraphQL
        .query(QueryCompanyFields, { ...params, mappedFields })
        .toPromise()
        .then((result: IResponseContextResult<PartialFieldType>) => {
          if (result.error) {
            return {
              metadata: {
                totalCount: configuration.defaultAsyncLoadingOptions
              },
              collection: []
            }
          }

          const { companyFieldsList } = result.data
          const collection = companyFieldsList?.collection || []
          const metadata = companyFieldsList?.metadata || {}

          const cloneData = collection.map((item) => {
            return {
              value: item.uuid,
              supportingObj: {
                name: item.name,
                nameRequired: item.required
              }
            }
          })

          return {
            metadata,
            collection: cloneData
          }
        }),
    [mappedFields]
  )

  const validateImportCompany = useCallback(
    async (
      file: File,
      finishCallback?: (data: {
        mappedFields?: MappedFieldType[]
        file?: File
      }) => Promise<any>
    ) => {
      const result = await validateImport({ file })

      if (result.error) {
        return catchErrorFromGraphQL({
          error: result.error,
          setToast,
          page: configuration.path.settings.import,
          router
        })
      }

      const mappedFields = result.data?.companiesImportingValidate?.mappedFields

      return finishCallback
        ? finishCallback({ mappedFields, file })
        : Promise.resolve()
    },
    [validateImport, setToast, router]
  )

  const onImportCompany = useCallback(
    (data: { mappedFields: MappedFieldType[] }): Promise<any> => {
      setShowLockApp('')
      return importCompany({
        file,
        mappedFields: data?.mappedFields
      }).then((result) => {
        if (result.error) {
          setCloseLockApp()
          const errorMessage = result.error.graphQLErrors?.[0]?.message || ''
          if (errorMessage.includes('duplicated'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:duplicated_title')}`,
              description: errorMessage
            })
          else if (errorMessage.includes('required fields'))
            setToast({
              open: true,
              type: 'error',
              title: `${t('notification:import_mapping_require_title')}`,
              description: errorMessage
            })
          else
            catchErrorFromGraphQL({
              error: result.error,
              setToast,
              router
            })
          return Promise.resolve(false)
        }

        if (result.data.companiesImport?.import.id) {
          setCloseLockApp()
          return router.push(
            `${configuration.path.settings.import}?import_id=${result.data.companiesImport?.import.id}&object_kind=company`
          )
        }

        return Promise.resolve(true)
      })
    },
    [file]
  )

  const getImportCompanyList = useCallback(
    async (pageParam = {} as IParamsTableInfinity) => {
      const { page } = pageParam
      return clientRequest
        .query(QueryCompanyImportFilesList, {
          page,
          limit: configuration.defaultPageSize
        })
        .toPromise()
        .then((result) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.candidates.list,
              setToast
            })

            return {
              data: [],
              meta: {
                totalRowCount: 0,
                pageSize: configuration.defaultPageSize
              }
            }
          }

          const collection = result?.data?.companyImportsList?.collection || []
          const metadata = result?.data?.companyImportsList?.metadata

          return {
            data: collection,
            meta: {
              totalRowCount: metadata?.totalCount || 0,
              pageSize: configuration.defaultPageSize
            }
          }
        })
    },
    []
  )

  const getImportedCompanyHistory = async (
    pageParam = {} as IParamsTableInfinity
  ): Promise<IPageResult<ImportFileHistoriesType>> => {
    return clientGraphQL
      .query(QueryImportedCompanyHistory, {
        ...pageParam,
        limit: configuration.defaultPageSize,
        importId: Number(router.query['import_id'])
      })
      .toPromise()
      .then((result: IResponseContextResult<ImportFileHistoriesType>) => {
        if (result.error) {
          catchErrorFromGraphQL({
            error: result.error,
            page: configuration.path.settings.departments,
            setToast
          })

          return {
            data: [],
            meta: {
              totalRowCount: 0,
              pageSize: configuration.defaultPageSize
            }
          }
        }

        const { companyImportHistoriesList } = result.data
        const collection = (companyImportHistoriesList?.collection || []).map(
          (item) => ({ ...item, id: item.data.key })
        )
        const metadata = companyImportHistoriesList?.metadata || {}

        return {
          data: collection,
          meta: {
            totalRowCount: metadata.totalCount,
            pageSize: configuration.defaultPageSize
          }
        }
      })
  }

  const getImportedCompanyShow = async () => {
    return clientGraphQL
      .query(QueryCompanyImportShow, {
        id: Number(router.query['import_id'])
      })
      .toPromise()
      .then(
        (
          result: IResponseContextResult<{
            companyImportsShow: Partial<ImportFileType>
          }>
        ) => {
          if (result.error) {
            catchErrorFromGraphQL({
              error: result.error,
              page: configuration.path.settings.import,
              setToast
            })

            return undefined
          }

          return result.data?.companyImportsShow
        }
      )
  }

  return {
    isLoadingImport,
    isLoadingValidate,
    validateImportCompany,
    promiseCompanyFields,
    onImportCompany,
    getImportCompanyList,
    getImportedCompanyHistory,
    getImportedCompanyShow
  }
}

// Default fallback data
const defaultData = () => ({
  isLoadingImport: false,
  isLoadingValidate: false,
  promiseFields: () =>
    Promise.resolve({ metadata: { totalCount: 0 }, collection: [] }),
  validateImport: () => Promise.resolve(),
  onImport: () => Promise.resolve(false),
  getImportList: () =>
    Promise.resolve({ data: [], meta: { totalRowCount: 0, pageSize: 0 } }),
  getImportedHistory: () =>
    Promise.resolve({ data: [], meta: { totalRowCount: 0, pageSize: 0 } }),
  getImportedShow: () => Promise.resolve(undefined)
})

export const useImportHandler = (props: {
  importType?: {
    value: ImportTypeKey
  }
  mappedFields?: MappedFieldType[]
  file?: File
}) => {
  const { importType, mappedFields, file } = props || {}
  const type = importType?.value

  const jobsData = useMappingJobFields({ mappedFields, file })
  const candidatesData = useMappingCandidateFields({ mappedFields, file })
  const coursesData = useMappingCourseFields({ mappedFields, file })
  const companiesData = useMappingCompanyFields({ mappedFields, file })

  const dataMap = useMemo(
    () => ({
      jobs: {
        isLoadingImport: jobsData.isLoadingImport,
        isLoadingValidate: jobsData.isLoadingValidate,
        validateImport: jobsData.validateImportJob,
        promiseFields: jobsData.promiseJobFields,
        onImport: jobsData.onImportJobs,
        getImportList: jobsData.getImportJobsList,
        getImportedHistory: jobsData.getImportedJobsHistory,
        getImportedShow: jobsData.getImportedJobsShow
      },
      candidate: {
        isLoadingImport: candidatesData.isLoadingImport,
        isLoadingValidate: candidatesData.isLoadingValidate,
        validateImport: candidatesData.validateImportCandidate,
        promiseFields: candidatesData.promiseProfileFields,
        onImport: candidatesData.onImportCandidates,
        getImportList: candidatesData.getImportProfilesList,
        getImportedHistory: candidatesData.getImportedProfilesHistory,
        getImportedShow: candidatesData.getImportedProfilesShow
      },
      course: {
        isLoadingImport: coursesData.isLoadingImport,
        isLoadingValidate: coursesData.isLoadingValidate,
        validateImport: coursesData.validateImportCourse,
        promiseFields: coursesData.promiseCourseFields,
        onImport: coursesData.onImportCourse,
        getImportList: coursesData.getImportTenantCoursesList,
        getImportedHistory: coursesData.getImportedTenantCoursesHistory,
        getImportedShow: coursesData.getImportedTenantCoursesShow
      },
      company: {
        isLoadingImport: companiesData.isLoadingImport,
        isLoadingValidate: companiesData.isLoadingValidate,
        validateImport: companiesData.validateImportCompany,
        promiseFields: companiesData.promiseCompanyFields,
        onImport: companiesData.onImportCompany,
        getImportList: companiesData.getImportCompanyList,
        getImportedHistory: companiesData.getImportedCompanyHistory,
        getImportedShow: companiesData.getImportedCompanyShow
      }
    }),
    [jobsData, candidatesData, coursesData, companiesData]
  )

  const selectedData = useMemo(() => {
    return dataMap[type as keyof typeof dataMap] || defaultData
  }, [dataMap, type, defaultData])

  return useMemo(
    () => ({
      isLoadingImport: selectedData.isLoadingImport,
      isLoadingValidate: selectedData.isLoadingValidate,
      onImport: selectedData.onImport,
      validateImport: selectedData.validateImport,
      promiseFields: selectedData.promiseFields,
      getImportList: selectedData.getImportList,
      getImportedHistory: selectedData.getImportedHistory,
      getImportedShow: selectedData.getImportedShow
    }),
    [selectedData, type]
  )
}

export default useImportHandler
