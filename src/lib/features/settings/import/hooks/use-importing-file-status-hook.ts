import { useRouter } from 'next/router'
import { useCallback, useEffect, useState } from 'react'
import configuration from '~/configuration'
import { useGraphQLRequest } from '~/core/hooks/use-graphQL'
import useBoundStore from '~/lib/store'
import useToastStore from '~/lib/store/toast'
import { ImportFileStatus, ISourceImportType } from '../types'
import useImportHandler from './use-import'

const useImportingFileStatusHook = ({
  importType
}: {
  importType?: ISourceImportType
}) => {
  const router = useRouter()
  const { user } = useBoundStore()
  const { setToast } = useToastStore()
  const clientGraphQL = useGraphQLRequest({ language: user?.language })
  const [importFileStatus, setImportFileStatus] = useState<
    undefined | ImportFileStatus
  >()
  const { getImportedShow } = useImportHandler({ importType })

  const [step, setStep] = useState<undefined | number>() //maximum step is 3; count from index 0

  const nextStep = useCallback(() => {
    if (typeof step === 'number') {
      const nextStepCount = step + 1

      setStep(nextStepCount)
      if (nextStepCount === 2) {
        setImportFileStatus('in_progress')
      }
    }
  }, [step])

  const previousStep = useCallback(() => {
    if (typeof step === 'number') {
      const previousStepCount = step - 1
      if (step > 0 && step <= 2) {
        setStep(previousStepCount)
        if (previousStepCount !== 2) setImportFileStatus(undefined)
      }
    }
  }, [step])

  const backToFirstStep = useCallback(() => {
    setImportFileStatus(undefined)
    setStep(0)
    router.push(
      `${configuration.path.settings.import}?tab=import&object_kind=${router.query?.['object_kind']}`
    )
  }, [router])

  useEffect(() => {
    if (router.query?.['import_id'] && importType?.value) {
      // let import_type = router.query?.['import_type'] || ''
      getImportedShow().then((importedFile) => {
        setImportFileStatus(importedFile?.status)
      })
    } else {
      setImportFileStatus(undefined)
      setStep(0)
    }
  }, [router.query?.['import_id']])

  return {
    currentStep: step,
    importFileStatus,
    nextStep,
    previousStep,
    backToFirstStep
  }
}

export default useImportingFileStatusHook
