import { gql } from 'urql'
import { PartialFieldType } from '../types'

const QueryCompanyFields = gql<
  {
    companyFieldsList: {
      collection: Array<PartialFieldType>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    page?: number
    limit?: number
    search?: string
  }
>`
  query ($limit: Int, $page: Int, $search: String, $mappedFields: [JSON!]) {
    companyFieldsList(
      limit: $limit
      page: $page
      search: $search
      mappedFields: $mappedFields
    ) {
      collection {
        field
        name
        required
        uuid
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCompanyFields
