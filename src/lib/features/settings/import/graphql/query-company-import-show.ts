import { gql } from 'urql'
import { ImportFileStatus } from '../types'

const QueryCompanyImportShow = gql<
  {
    companyImportsShow: {
      status: ImportFileStatus
      statusDescription: string
    }
  },
  {
    id: number
  }
>`
  query ($id: Int!) {
    companyImportsShow(id: $id) {
      name
      rowsCount
      importedCount
      status
      mappedFields
    }
  }
`

export default QueryCompanyImportShow
