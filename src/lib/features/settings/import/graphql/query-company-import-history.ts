import { gql } from 'urql'
import { ImportFileCompaniesHistoriesType } from '../types'

const QueryImportedCompanyHistory = gql<
  {
    companyImportHistoriesList: {
      collection: Array<ImportFileCompaniesHistoriesType>
      metadata: {
        totalCount: number
        currentPage: number
      }
    }
  },
  {
    id: number
  }
>`
  query (
    $importId: Int!
    $limit: Int
    $page: Int
    $key: String
    $keys: [String!]
  ) {
    companyImportHistoriesList(
      importId: $importId
      limit: $limit
      page: $page
      key: $key
      keys: $keys
    ) {
      collection {
        data
        rawData
        grouppedKey
        grouppedKeyDescription
      }
      metadata {
        totalCount
        currentPage
        extras
      }
    }
  }
`

export default QueryImportedCompanyHistory
