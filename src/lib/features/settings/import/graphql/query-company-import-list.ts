import { gql } from 'urql'
import { ImportFileType } from '../types'

const QueryCompanyImportList = gql<
  {
    companyImportsList: {
      collection: Array<ImportFileType>
      metadata: {
        totalCount: number
      }
    }
  },
  {
    page?: number
    limit?: number
  }
>`
  query ($limit: Int, $page: Int) {
    companyImportsList(limit: $limit, page: $page) {
      collection {
        id
        name
        file
        uploadedBy {
          fullName
          avatarVariants
          defaultColour
        }
        importedCount
        rowsCount
        status
        statusDescription
        createdAt
        objectKindDescription
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryCompanyImportList
