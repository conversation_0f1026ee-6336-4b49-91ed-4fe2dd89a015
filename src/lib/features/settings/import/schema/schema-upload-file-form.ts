import { TFunction } from 'i18next'
import { z } from 'zod'
import { ACCEPTED_CSV_FILE, convertFileSizeToBytes } from '~/lib/schema'

const schemaUploadFileForm = (t: TFunction, sizeFile: string) => {
  return z.object({
    file: z
      .any()
      .refine(
        (files) => {
          if (!files?.[0]) return true
          return files?.[0]?.size <= convertFileSizeToBytes({ size: sizeFile })
        },
        `${t('form:invalid_upload_max_size', {
          maximumSizeFile: sizeFile
        })}`
      )
      .refine(
        (files) => {
          if (!files?.[0]) return true
          return ACCEPTED_CSV_FILE.includes(files?.[0]?.type)
        },
        `${t('form:invalid_upload_csv_file', {
          maximumSizeFile: sizeFile
        })}`
      )
  })
}

export default schemaUploadFileForm
