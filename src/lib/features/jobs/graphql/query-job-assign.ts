import { gql } from 'urql'

const QueryTenantJobsApplicableList = gql`
  query ($page: Int, $limit: Int, $search: String, $profileId: Int) {
    jobsApplicableList(
      page: $page
      limit: $limit
      search: $search
      profileId: $profileId
    ) {
      collection {
        id
        title
        jobLocations {
          address
          state
          city
          country
        }
        department {
          name
        }
        company {
          permittedFields
        }
      }
      metadata {
        totalCount
      }
    }
  }
`

export default QueryTenantJobsApplicableList
